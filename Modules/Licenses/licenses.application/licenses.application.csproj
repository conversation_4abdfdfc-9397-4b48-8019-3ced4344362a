<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\libs\Commons\Commons.csproj" />
      <ProjectReference Include="..\..\..\libs\Licencing\Licencing.csproj" />
      <ProjectReference Include="..\licenses.domain\licenses.domain.csproj" />
    </ItemGroup>

    <ItemGroup>
      <Folder Include="CheckServices\Licenses\" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="WolverineFx" Version="4.8.0" />
      <PackageReference Include="WolverineFx.Marten" Version="4.8.0" />
      <PackageReference Include="WolverineFx.Postgresql" Version="4.8.0" />
      <PackageReference Include="WolverineFx.RDBMS" Version="4.8.0" />
    </ItemGroup>

</Project>
