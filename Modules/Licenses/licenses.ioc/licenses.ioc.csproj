<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\libs\ServiceDefaults\ServiceDefaults.csproj" />
      <ProjectReference Include="..\licenses.data\licenses.data.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.Extensions.Configuration" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Configuration.Binder" Version="9.0.8" />
      <PackageReference Include="Microsoft.Extensions.Hosting.Abstractions" Version="9.0.8" />
    </ItemGroup>

</Project>
