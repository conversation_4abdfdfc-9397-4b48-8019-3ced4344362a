<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Dapper" Version="2.1.66" />
      <PackageReference Include="Microsoft.Data.SqlClient" Version="6.1.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\WingLager\wingLager.application\wingLager.application.csproj" />
      <ProjectReference Include="..\wingPrinterListLabel.application\wingPrinterListLabel.application.csproj" />
    </ItemGroup>

    <ItemGroup Condition="!$([MSBuild]::IsOSPlatform('Windows'))">
        <Reference Include="PrinterService">
            <HintPath>..\..\..\PrinterService\PublishDlls\PrinterService.dll</HintPath>
            <Private>true</Private>
        </Reference>
        <Reference Include="combit.ListLabel30">
            <HintPath>..\..\..\PrinterService\PublishDlls\combit.ListLabel30.dll</HintPath>
            <Private>true</Private>
        </Reference>
    </ItemGroup>

    <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
        <ProjectReference Include="..\..\..\PrinterService\PrinterService.csproj" />
    </ItemGroup>

</Project>
