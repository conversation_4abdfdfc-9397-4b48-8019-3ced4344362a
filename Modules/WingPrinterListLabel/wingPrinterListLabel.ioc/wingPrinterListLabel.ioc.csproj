<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="9.0.8" />
      <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\wingPrinterListLabel.application\wingPrinterListLabel.application.csproj" />
      <ProjectReference Include="..\wingPrinterListLabel.data\wingPrinterListLabel.data.csproj" />
    </ItemGroup>

    <ItemGroup Condition="!$([MSBuild]::IsOSPlatform('Windows'))">
        <Reference Include="PrinterService">
            <HintPath>..\..\..\PrinterService\PublishDlls\PrinterService.dll</HintPath>
            <Private>true</Private>
        </Reference>
        <Reference Include="combit.ListLabel30">
            <HintPath>..\..\..\PrinterService\PublishDlls\combit.ListLabel30.dll</HintPath>
            <Private>true</Private>
        </Reference>
        <Reference Include="combit.Reporting.Web">
            <HintPath>..\..\..\PrinterService\PublishDlls\combit.ListLabel30.Web.dll</HintPath>
            <Private>true</Private>
        </Reference>
    </ItemGroup>

    <ItemGroup Condition="$([MSBuild]::IsOSPlatform('Windows'))">
        <ProjectReference Include="..\..\..\PrinterService\PrinterService.csproj" />
    </ItemGroup>
</Project>
