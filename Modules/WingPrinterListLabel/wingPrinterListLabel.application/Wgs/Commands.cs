using MediatR;
using PrinterService.Models;
using WingCore.domain.Common.Configurations;
using wingPrinterListLabel.application.Wgs.Helpers;

namespace wingPrinterListLabel.application.Wgs;

// WGS Email Commands
public record DummyWgsSendEmailCommand(string Email,
                                       string LanguageToUse,
                                       ConfigurationElectricWgs ConfigurationElectricWgs) : IRequest<EmailDataToCreateEmail?>;
public record WgsSendEmailCommand(long WgsNumber, string LanguageToUse) : IRequest<EmailDataToCreateEmail?>;
public record BulkWgsSendEmailAutomaticCommand(string LanguageToUse) : IRequest<BulkWgsSendEmailResult>; // For automatic job - returns detailed results

// WGS PDF Commands (printing functionality)
public record WgsAsPdfBase64Command(long WgsNumber, string LanguageToUse) : IRequest<string>;
