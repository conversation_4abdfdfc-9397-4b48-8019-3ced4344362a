using MediatR;
using PrinterService;
using wingLager.application.PrintableDtos;
using wingPrinterListLabel.application.Contracts;

namespace wingPrinterListLabel.application.PaletLagers.Handler;

public class ShowDeliveryNotePreviewCommandHandler(IListLabelRepository listLabelRepository) : IRequestHandler<ShowDeliveryNotePreviewCommand, string>
{
    public Task<string> Handle(ShowDeliveryNotePreviewCommand request, CancellationToken cancellationToken)
    {
       var base64 =  PrinterCreator.PdfAsBase64(DeliveryNoteDto.Create(request.PaletLagerList, request.CommissionCustomer, request.Auftragskopf,
            request.AuftragsposList),request.Layout, request.AppFolder, listLabelRepository);
        return Task.FromResult(base64);
    }
}