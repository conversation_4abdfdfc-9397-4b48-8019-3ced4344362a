using MediatR;
using PrinterService;
using wingLager.application.PrintableDtos;
using wingPrinterListLabel.application.Contracts;

namespace wingPrinterListLabel.application.PaletLagers.Handler;

public class PrintPaletLagerDeliveryNoteCommandHandler(IListLabelRepository listLabelRepository) : IRequestHandler<PrintPaletLagerDeliveryNoteCommand>
{
    public Task Handle(PrintPaletLagerDeliveryNoteCommand request, CancellationToken cancellationToken)
    {
        if(!string.IsNullOrEmpty(request.ConfigurationCommission.LayoutForDeliveryNote))
            PrinterCreator.PrintPdf(DeliveryNoteDto.Create(request.PaletLagerList, request.CommissionCustomer, request.Auftragskopf,
                request.AuftragsposList),request.ConfigurationCommission.LayoutForDeliveryNote, request.ConfigurationCommission.Printer, request.AppFolder, 1, false, listLabelRepository);
        if(!string.IsNullOrEmpty(request.ConfigurationCommission.LayoutForPallet))
            PrinterCreator.PrintPdf(PalletNoteDto.Create(request.PaletLagerList, request.CommissionCustomer, request.Auftragskopf,
                request.AuftragsposList),request.ConfigurationCommission.LayoutForPallet, request.ConfigurationCommission.Printer, request.AppFolder, 1, false, listLabelRepository);
        
        return Task.CompletedTask;
    }   
}