using MediatR;
using WingCore.domain.Common.Configurations;
using WingCore.domain.Models;
using wingLager.domain.PaletLagers;
using wingPrinterListLabel.domain;

namespace wingPrinterListLabel.application.PaletLagers;

public record PrintPaletLagerDeliveryNoteCommand(
    List<PaletLager> PaletLagerList,
    string AppFolder,
    Kunden? CommissionCustomer,
    Auftragskopf Auftragskopf,
    List<Auftragspo> AuftragsposList,
    ConfigurationCommission ConfigurationCommission) : IRequest;
    
public record ShowDeliveryNotePreviewCommand(List<PaletLager> PaletLagerList,
    string AppFolder,
    Kunden? CommissionCustomer,
    Auftragskopf Auftragskopf,
    List<Auftragspo> AuftragsposList,
    string Layout) : IRequest<string>;

public record ShowPaletNotePreviewCommand(List<PaletLager> PaletLagerList,
    string AppFolder,
    Kunden? CommissionCustomer,
    Auftragskopf Auftragskopf,
    List<Auftragspo> AuftragsposList,
    string Layout) : IRequest<string>;