using System.Runtime.Versioning;
using MediatR;
using PrinterService;
using wingLager.application.Contracts;
using wingLager.application.PrintableDtos;
using wingLager.domain.PaletLagers;
using wingPrinterListLabel.application.Contracts;

namespace wingPrinterListLabel.application.ProcessOrders.Positions.Handlers;

public class PrintProcessPositionCommandHandler(IProcessOrderPositionRepository processOrderPositionRepository,
                                                IProcessOrderHeaderRepository processOrderHeaderRepository,
                                                IPaletLagerRepository paletLagerRepository,
                                                IListLabelRepository listLabelRepository,
                                                IUnitOfWork unitOfWork) : IRequestHandler<PrintProcessPositionCommand>
{
    [SupportedOSPlatform("windows")]
    public async Task Handle(PrintProcessPositionCommand command, CancellationToken cancellationToken)
    {
        if(string.IsNullOrWhiteSpace(command.ChargeNumber))
            throw new Exception($"Keine Chargennummer übergeben!");
        
        var processOrderPositions = await processOrderPositionRepository.GetByIdAsync(command.ProcessOrderPositionId,false);
        if(processOrderPositions is null)
            throw new Exception($"Keine Prozessposition mit der Id({command.ProcessOrderPositionId}) gefunden");
        
        var processOrderPosition = processOrderPositions.FirstOrDefault();
        if(processOrderPosition?.PaletLager is null)
            throw new Exception($"PaletLager für Prozessposition mit der Id({command.ProcessOrderPositionId}) nicht gefunden");
        
        if(processOrderPosition.PaletLager.Count == 0)
            return;
        
        await processOrderHeaderRepository.SetDataForFirstPrintAndSave(processOrderPosition.ProcessOrderHeaderNumber);

        try
        {


            // print all sacketikett in one print
            if (command.PrintModeForProcessPosition is PrintModeForProcessPosition.All
                or PrintModeForProcessPosition.Sack)
            {
                PaletLager? palletLager = null;
                palletLager = command.PrintOnlyThisPalletId == 0
                    ? processOrderPosition.PaletLager.First()
                    : processOrderPosition.PaletLager.FirstOrDefault(p => p.Id == command.PrintOnlyThisPalletId);

                if (palletLager is null)
                    throw new Exception($"Keine Palette mit der Id({command.PrintOnlyThisPalletId}) gefunden");

                var count = command.PrintCountPalletAndSacks.NumberOfCopyForSackCount;

                if (string.IsNullOrWhiteSpace(palletLager.ChargNr))
                    palletLager.ChargNr = command.ChargeNumber;
                //palletLager.CreateChargeNumber();

                PrinterCreator.PrintPdf(LabelBagDto.Create(palletLager, 1), processOrderPosition.LayoutSet,
                    command.SelectedPrinterForSack, command.AppFolder, count, command.WithExportOption,
                    listLabelRepository);
            }

            foreach (var paletLager in processOrderPosition.PaletLager)
            {
                if (command.PrintOnlyThisPalletId > 0 &&
                    command.PrintOnlyThisPalletId != paletLager.Id)
                    continue;

                /*if(!command.PrintAgain &&
                   paletLager.IsAllSackPrinted() &&
                   paletLager.IsLagerSuccessPrinted())
                    continue;*/

                if (paletLager.JobDatum == DateTime.MinValue)
                {
                    paletLager.JobDatum = DateTime.UtcNow;
                    paletLager.JobUhrzeit = DateTime.UtcNow;
                }

                if (string.IsNullOrWhiteSpace(paletLager.ChargNr))
                    paletLager.ChargNr = command.ChargeNumber;
                //paletLager.CreateChargeNumber();

                paletLager.FlagsWorker.SetPrintStartedFlag();

                if (string.IsNullOrWhiteSpace(paletLager.NVENr))
                {
                    await paletLagerRepository.SetNextSsccNrUpdateAndSave(paletLager, command.UserName);
                }
                else
                {
                    await paletLagerRepository.UpdateWithoutTracking(paletLager, command.UserName);
                    await paletLagerRepository.SaveChangesAsync();
                }

                if (command.PrintModeForProcessPosition is PrintModeForProcessPosition.All
                    or PrintModeForProcessPosition.Sack)
                {
                    var count = (int)(paletLager.AnzSack ?? 0);

                    paletLager.SackNumbersWasPrinted.Clear();
                    paletLager.SackNumbersWasPrinted.AddRange(Enumerable.Range(0, count));
                }

                if (command.PrintModeForProcessPosition is PrintModeForProcessPosition.All
                    or PrintModeForProcessPosition.Palette)
                {
                    var count = command.PrintCountPalletAndSacks.NumberOfCopyForPallet;
                    PrinterCreator.PrintPdf(LabelPalletDto.Create(paletLager), processOrderPosition.LayoutPet,
                        command.SelectedPrinterForPalett, command.AppFolder, count, command.WithExportOption,
                        listLabelRepository);
                }

                paletLager.SetWasPrintedSuccess();
                await paletLagerRepository.UpdateWithoutTracking(paletLager, command.UserName);
                await paletLagerRepository.SaveChangesAsync();
                unitOfWork.Detach(paletLager);
            }

            if (processOrderPosition.StartFirstPrintDateTime == DateTime.MinValue)
            {
                processOrderPosition.StartFirstPrintDateTime = DateTime.UtcNow;
                processOrderPosition.FlagsWorker.SetPrintedFlag();
                await processOrderPositionRepository.Update(processOrderPosition, command.UserName);
            }
        }
        catch
        {
            unitOfWork.Detach(processOrderPosition);
            throw;
        }

        await unitOfWork.SaveChangesAsync(cancellationToken);
        unitOfWork.Detach(processOrderPosition);
        
        if(processOrderPosition.IsAllPalletPrinted() && 
           processOrderPosition.IsAllSackPrinted() )
        { 
            await processOrderHeaderRepository.SetDataForIsPrintedAndSave(processOrderPosition.ProcessOrderHeaderNumber);
        }
        
        unitOfWork.Detach(processOrderPosition);
    }
}
