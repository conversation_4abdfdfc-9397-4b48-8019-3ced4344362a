using Dapper;
using Microsoft.EntityFrameworkCore;
using wingLager.application.Contracts;
using wingLager.application.SearchParams;
using wingLager.data.Repositories.Helper;
using wingLager.data.Services;
using wingLager.domain.Common;
using wingLager.domain.PaletLagers;
using wingLager.domain.ProcessOrders;

namespace wingLager.data.Repositories;

public class ProcessOrderHeaderRepository(
    WingLagerDbContext repositoryContext,
    IProcessOrderPositionRepository processOrderPositionRepository,
    IWingLagerDbConnectionBuilder connectionBuilder,
    IAuditLogRepository auditLogRepository)
    : RepositoryBase<ProcessOrderHeader>(repositoryContext), IProcessOrderHeaderRepository
{
    public async Task<IEnumerable<ProcessOrderHeader>?> GetAllAsync(bool trackChanges,
        OrderProcessHeaderSearchParam searchParam)
    {
        if (trackChanges)
            return await RepositoryContext.ProcessOrderHeaders.SetSearchParam(searchParam).ToListAsync();
        else
            return await RepositoryContext.ProcessOrderHeaders.SetSearchParam(searchParam).AsNoTracking().ToListAsync();
    }

    public async Task<ICollection<ProcessOrderHeader>> GetAllByStatusAsync(ProcessOrderHeaderStatus status)
    {
        return await repositoryContext.ProcessOrderHeaders.Where(c => c.Status == status).AsNoTracking().ToListAsync();
    }

    public async Task<IEnumerable<long>?> GetAllOrderPostionsId(long id)
    {
        var processHeaders =
            await GetAllAsync(false, new OrderProcessHeaderSearchParam { Id = id, WithAllData = true });
        var processHeader = processHeaders?.FirstOrDefault();

        return processHeader is null
            ? []
            : (from processPos in processHeader.Positionen ?? [] select processPos.OrderPostionId).ToList();
    }

    public async Task<IEnumerable<ProcessOrderHeader>?> GetByNumberAsync(long number, bool trackChanges)
    {
        return await FindByCondition(c => c.Number.Equals(number), trackChanges).ToListAsync();
    }

    public async Task<ProcessOrderHeader?> GetByIdAsync(long id, bool trackChanges)
    {
        return await FindByCondition(c => c.Id == id, trackChanges).FirstOrDefaultAsync();
    }

    public async Task<ProcessOrderHeader?> GetByIdWithoutTrackingAsync(long id)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    *
                    FROM [ProcessOrderHeader] p
                    WHERE p.[ID] = @id
                    """;
        var parameter = new { id };
        var result = await connection.QueryFirstAsync<ProcessOrderHeader>(query, parameter);
        if (result == null)
            throw new InvalidOperationException($"Kein Prozessauftrag mit ID {id} gefunden");
        return result;
    }

    public async Task<ICollection<PaletLager>> GetAllPalletLagerOfProcessOrderHeader(long id)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    p.*
                    FROM [PaletLager] p
                    JOIN [ProcessOrderPosition] po on p.[process_order_position_id] = po.[ID]
                    JOIN [ProcessOrderHeader] ph on po.[ProcessOrderHeaderNumber] = ph.[Number]
                    WHERE ph.[ID] = @id
                    """;
        var parameter = new { id };
        var result = await connection.QueryAsync<PaletLager>(query, parameter).ContinueWith<ICollection<PaletLager>>(r =>
            r is { IsCompletedSuccessfully: true, Result: not null } ? [..r.Result] : []);
        return result;
    }

    public async Task<ProcessOrderHeader> Create(ProcessOrderHeader newProcessOrderHeader, string userName)
    {
        try
        {
            await SetLockToCreateNumberAsync(userName);
            newProcessOrderHeader.Id = 0;
            newProcessOrderHeader.Number = GetMaxAuftragsnummer() + 1;
            newProcessOrderHeader.CreatedAt = DateTime.Now;
            newProcessOrderHeader.CreationUser = userName;
            newProcessOrderHeader.UpdatedAt = DateTime.Now;
            newProcessOrderHeader.UpdateUser = userName;

            foreach (var pos in newProcessOrderHeader.Positionen ?? [])
            {
                pos.Id = 0;
                pos.ProcessOrderHeaderNumber = newProcessOrderHeader.Number;

                foreach (var paletLager in pos.PaletLager)
                {
                    paletLager.Id = 0;
                }
            }

            Create(newProcessOrderHeader);

            await SaveChangesAsync();
            var entry = RepositoryContext.Entry(newProcessOrderHeader);

            auditLogRepository.AddLogChanges(entry, "Create", userName);
            foreach (var pos in newProcessOrderHeader.Positionen ?? [])
            {
                var entryPos = RepositoryContext.Entry(pos);
                auditLogRepository.AddLogChanges(entryPos, "Create", userName);

                foreach (var paletLager in pos.PaletLager)
                {
                    var entryPaletLager = RepositoryContext.Entry(paletLager);
                    auditLogRepository.AddLogChanges(entryPaletLager, "Create", userName);
                }
            }

            entry.State = EntityState.Detached;

            return newProcessOrderHeader;
        }
        finally
        {
            SetUnLockToCreateNumber();
        }
    }

    public async Task Update(ProcessOrderHeader processOrderHeader, string userName, bool withOutTracking = false)
    {
        await Task.Delay(100);

        var oldObjects = await GetAllAsync(false,
            new OrderProcessHeaderSearchParam { Id = processOrderHeader.Id, WithAllData = true });
        var oldObject = oldObjects?.FirstOrDefault();

        if (oldObject is null)
            throw new Exception($"ProcessOrderHeader with Id ({processOrderHeader.Id}) not found");

        if (oldObject == processOrderHeader)
            return;

        var oldEntry = RepositoryContext.Entry(oldObject);

        var nowDateTime = DateTime.UtcNow;

        processOrderHeader.UpdatedAt = nowDateTime;
        processOrderHeader.UpdateUser = userName;
        var positionIndex = 0;
        foreach (var pos in processOrderHeader.Positionen ?? [])
        {
            pos.Position = positionIndex++;
            await processOrderPositionRepository.Update(pos, userName);
        }
        
        //processOrderHeader.Positionen = null;
        if (withOutTracking)
           await UpdateWithoutTracking(processOrderHeader, "System");
        else 
            Update(processOrderHeader);

        var newEntry = RepositoryContext.Entry(processOrderHeader);

        auditLogRepository.AddLogChanges(oldEntry, newEntry, userName);
    }
    
    private async Task UpdateWithoutTracking(ProcessOrderHeader processOrderHeader, string userName, CancellationToken token = default)
    {
        repositoryContext.Update(processOrderHeader);
        await repositoryContext.SaveChangesAsync(token);
        repositoryContext.Entry(processOrderHeader).State = EntityState.Detached;
    }

    public async Task Delete(long processOrderHeaderId)
    {
        var objSearchResults = await GetAllAsync(false,
            new OrderProcessHeaderSearchParam() with { Id = processOrderHeaderId, WithAllData = true });
        var objSearchResult = objSearchResults?.FirstOrDefault();
        if (objSearchResult is null)
            return;

        await processOrderPositionRepository.DeleteWIthSameHeaderNumber(objSearchResult.Number);
        Delete(objSearchResult);

        var currentEntry = RepositoryContext.Entry(objSearchResult);

        auditLogRepository.AddLogChanges(currentEntry, "");
    }

    public async Task SetDataForFirstPrintAndSave(long processOrderHeaderNumber)
    {
        var headers = await GetByNumberAsync(processOrderHeaderNumber, false);
        var processOrderHeader = headers?.FirstOrDefault();
        if (processOrderHeader is null)
            throw new Exception($"No ProcessOrderHeader with Number {processOrderHeaderNumber} found");

        if (processOrderHeader.Status == ProcessOrderHeaderStatus.InPrinting)
            return;

        if (processOrderHeader.StartFirstPrintDateTime == DateTime.MinValue)
            processOrderHeader.StartFirstPrintDateTime = DateTime.Now;

        processOrderHeader.Status = ProcessOrderHeaderStatus.InPrinting;

        Update(processOrderHeader);
        await SaveChangesAsync();
        ClearChangeTracker();
    }

    public async Task SetDataForIsPrintedAndSave(long processOrderHeaderNumber)
    {
        var headers = await GetAllAsync(false, new OrderProcessHeaderSearchParam{Number = processOrderHeaderNumber, WithAllData = true});
        var processOrderHeader = headers?.FirstOrDefault();
        if (processOrderHeader is null)
            throw new Exception($"No ProcessOrderHeader with Number {processOrderHeaderNumber} found");

        if (processOrderHeader.Status == ProcessOrderHeaderStatus.IsPrinted)
            return;
        
        var posNotPrinted = processOrderHeader.Positionen?
            .FirstOrDefault(p => !p.IsAllPalletPrinted() || !p.IsAllSackPrinted());
        
        if(posNotPrinted is not null)
            return;

        processOrderHeader.Status = ProcessOrderHeaderStatus.IsPrinted;

        Update(processOrderHeader);
        await SaveChangesAsync();
        ClearChangeTracker();
    }

    public long GetMaxAuftragsnummer()
    {
        return RepositoryContext.ProcessOrderHeaders.Any()
            ? RepositoryContext.ProcessOrderHeaders.Max(c => c.Number)
            : 0;
    }

    public async Task SetLockToCreateNumberAsync(string userName)
    {
        while (RepositoryContext.ProcessOrderLocks.Any())
            await Task.Delay(50);

        var sqlCommand =
            $"INSERT INTO ProcessOrderLock (lock_start, lock_user) VALUES ('{DateTime.Now.ToString("yyyy-MM-dd")}', '{userName}')";
        RepositoryContext.Database.ExecuteSqlRaw(sqlCommand);
    }

    public void SetUnLockToCreateNumber()
    {
        if (!RepositoryContext.ProcessOrderLocks.Any())
            return;
        const string sqlCommand = "DELETE FROM ProcessOrderLock";
        RepositoryContext.Database.ExecuteSqlRaw(sqlCommand);
    }
}