using Dapper;
using Microsoft.EntityFrameworkCore;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.Status;

namespace wingLager.data.Services.WarehouseManagementServices;

public class WmStellplatzService(WingLagerDbContext dbContext, IWingLagerDbConnectionBuilder connectionBuilder)
    : IWmStellplatzService
{
    public Task<ICollection<WmStellplatz>> GetAllWmStellplaetzeAsync()
    {
        var result = dbContext.Set<WmStellplatz>().AsNoTracking().ToList();
        var returning = result.Where(s => s.FlagWorker.IsStellplatzFlagSet()).ToList();
        return Task.FromResult<ICollection<WmStellplatz>>(returning);
    }

    public Task<ICollection<WmStellplatz>> GetAllVbrStellplaetzeAsync()
    {
        var result = dbContext.Set<WmStellplatz>().AsNoTracking().ToList();
        return Task.FromResult<ICollection<WmStellplatz>>(result.Where(s => s.FlagWorker.IsVbrFlagSet()).ToList());
    }

    public Task<ICollection<WmStellplatz>> GetAllWepStellplaetzeAsync()
    {
        var result = dbContext.Set<WmStellplatz>().AsNoTracking().ToList();
        return Task.FromResult<ICollection<WmStellplatz>>(result.Where(s => s.FlagWorker.IsWepFlagSet()).ToList());
    }


    public async Task<ICollection<WmStellplatz>> GetWmStellplaetzeByEbenenIddAsync(Guid ebeneId)
    {
        return await dbContext.Set<WmStellplatz>().Where(e => e.WmEbenenId == ebeneId).OrderBy(e => e.Number)
            .AsNoTracking().ToListAsync();
    }

    public async Task<ICollection<HallenPositionInfo>> GetHallenPosInfoOfEmptyWmStellplaetzeAsync()
    {
        await using var connection = connectionBuilder.GetConnection();
        var sql =
            """
            SELECT 
                h.[id] AS HallenId,
                h.[number] AS HallenNumber,
                r.[id] AS ReiheId,
                r.[Number] AS ReiheNumber,
                f.[id] AS FeldId,
                f.[number] AS FeldNumber,
                e.[id] AS EbeneId,
                e.[number] AS EbeneNumber,
                s.[id] AS StellplatzId,
                s.[number] AS StellplatzNumber
            FROM [stellplaetze] s
            INNER JOIN [ebenen] e ON s.[wm_ebenen_id] = e.Id
            INNER JOIN [felder] f ON e.[wm_feld_id] = f.Id
            INNER JOIN [reihen] r ON f.[wm_reihe_id] = r.Id
            INNER JOIN [halls] h ON r.[wm_hall_id] = h.Id
            WHERE s.[status] = @Status
              AND s.[wm_vbr] IS NULL
              AND s.[wm_wep] IS NULL
            ORDER BY h.[number], r.[number], f.[number], e.[number], s.[number];
            """;

        var parameters = new { Status = WmStellplatzStatus.EmptyStellplatz.value };
        var result = await connection
            .QueryAsync<HallenPositionInfo>(sql, parameters)
            .ContinueWith<ICollection<HallenPositionInfo>>(r =>
                r is { IsCompletedSuccessfully: true, Result: not null } ? [..r.Result] : []);
        return result;
    }

    public async Task<ICollection<HallenPositionInfo>> GetHallenPosInfoOfEmptyWmStellplaetzeForRobotAsync()
    {
        await using var connection = connectionBuilder.GetConnection();
        var sql =
            """
            SELECT 
                h.[id] AS HallenId,
                h.[number] AS HallenNumber,
                r.[id] AS ReiheId,
                r.[Number] AS ReiheNumber,
                f.[id] AS FeldId,
                f.[number] AS FeldNumber,
                e.[id] AS EbeneId,
                e.[number] AS EbeneNumber,
                s.[id] AS StellplatzId,
                s.[number] AS StellplatzNumber
            FROM [stellplaetze] s
            INNER JOIN [ebenen] e ON s.[wm_ebenen_id] = e.Id
            INNER JOIN [felder] f ON e.[wm_feld_id] = f.Id
            INNER JOIN [reihen] r ON f.[wm_reihe_id] = r.Id
            INNER JOIN [halls] h ON r.[wm_hall_id] = h.Id
            WHERE s.[status] = @Status
              AND s.[wm_vbr] IS NULL
              AND s.[wm_wep] IS NULL
              AND s.[automatic] IS 1
            ORDER BY h.[number], r.[number], f.[number], e.[number], s.[number];
            """;
        var parameters = new { Status = WmStellplatzStatus.EmptyStellplatz.value };
        var result = await connection
            .QueryAsync<HallenPositionInfo>(sql, parameters)
            .ContinueWith<ICollection<HallenPositionInfo>>(r =>
                r is { IsCompletedSuccessfully: true, Result: not null } ? [..r.Result] : []);
        return result;
    }


    public async Task<HallenPositionInfo?> GetHallenPositionInfoByStellplatzIdAsync(Guid stellplatzId)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    h.[id] AS HallenId,
                    h.[number] AS HallenNumber,
                    r.[id] AS ReiheId,
                    r.[number] AS ReiheNumber,
                    f.[id] AS FeldId,
                    f.[number] AS FeldNumber,
                    e.[id] AS EbeneId,
                    e.[number] AS EbeneNumber,
                    s.[id] AS StellplatzId,
                    s.[number] AS StellplatzNumber
                    FROM [stellplaetze] s
                    JOIN [ebenen] e ON s.[wm_ebenen_id] = e.[id]
                    JOIN [felder] f ON e.[wm_feld_id] = f.[id]
                    JOIN [reihen] r ON f.[wm_reihe_id] = r.[id]
                    JOIN [halls] h ON r.[wm_hall_id] = h.[id]
                    WHERE s.[id] = @StellplatzId
                    """;
        var parameters = new { StellplatzId = stellplatzId };
        var result = await connection.QueryFirstAsync<HallenPositionInfo>(query, parameters);
        return result;


    }

    public async Task<WmStellplatz?> GetByHallposition(HallenPositionInfo positionInfo)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    s.*
                    FROM [stellplaetze] s
                    JOIN [ebenen] e ON s.[wm_ebenen_id] = e.[id]
                    JOIN [felder] f ON e.[wm_feld_id] = f.[id]
                    JOIN [reihen] r ON f.[wm_reihe_id] = r.[id]
                    JOIN [halls] h ON r.[wm_hall_id] = h.[id]
                    WHERE s.[id] = @StellplatzId
                    AND h.[id] = @HallenId
                    AND r.[id] = @ReiheId
                    AND f.[id] = @FeldId
                    AND e.[id] = @EbeneId
                    """;
        var parmeter = new {StellplatzId = positionInfo.StellplatzId, HallenId = positionInfo.HallenId, ReiheId = positionInfo.ReiheId, FeldId = positionInfo.FeldId, EbeneId = positionInfo.EbeneId};
        var result = await connection.QueryFirstAsync<WmStellplatz>(query, parmeter);
        return result; 
       
    }

    public Task<WmStellplatz?> GetByHallpositionString(string hallPositionString)
    {
        var hallenPositionInfo = hallPositionString.ToHallPosition();

        var query = from s in dbContext.Set<WmStellplatz>()
            join e in dbContext.Set<WmEbene>() on s.WmEbenenId equals e.Id
            join f in dbContext.Set<WmFeld>() on e.WmFeldId equals f.Id
            join r in dbContext.Set<WmReihe>() on f.WmReiheId equals r.Id
            join h in dbContext.Set<WmHall>() on r.WmHallId equals h.Id
            where h.Number == hallenPositionInfo.HallenNumber
            where r.Number == hallenPositionInfo.ReiheNumber
            where f.Number == hallenPositionInfo.FeldNumber
            where e.Number == hallenPositionInfo.EbeneNumber
            where s.Number == hallenPositionInfo.StellplatzNumber
            select s;

        return query.AsNoTracking().FirstOrDefaultAsync();
    }

    public async Task<WmStellplatz?> GetWmStellplatzByIdAsync(Guid id)
    {
        var query = dbContext.Set<WmStellplatz>()
            .Where(s => s.Id == id)
            .AsNoTracking();
        
        // SQL-Abfrage ausgeben
        var sql = query.ToQueryString();
        Console.WriteLine($"SQL: {sql}");
    
        var result = await query.FirstOrDefaultAsync();
        Console.WriteLine($"Gefundenes Ergebnis: {result != null}");
    
        return result;

    }

    public async Task<ICollection<WmStellplatz>> GetWmStellplatzByPalletTypeAsync(string pallettype)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    [stellplaetze].*
                    FROM [stellplaetze]
                    INNER JOIN [ebenen] ON [stellplaetze].[wm_ebenen_id] = [ebenen].[id]
                    INNER JOIN [felder] ON [ebenen].[wm_feld_id] = [felder].[id]
                    WHERE [stellplaetze].[status] = @Status
                    AND EXISTS (
                          SELECT 1
                          FROM OPENJSON([stellplaetze].[possible_pallet_type])
                          WHERE value = @StellplatzPalletType
                    )
                    AND ([felder].[pallet_type] LIKE @Feldpallettype 
                    OR [felder].[pallet_type] LIKE @palletTypeEmpty)
                    """;
        var parameters = new
        {
            Status = WmStellplatzStatus.EmptyStellplatz.value, StellplatzPalletType = pallettype,
            Feldpallettype = pallettype, palletTypeEmpty = string.Empty
        };
        var result = await connection
            .QueryAsync<WmStellplatz>(query, parameters).ContinueWith<ICollection<WmStellplatz>>(r =>
                r is { IsCompletedSuccessfully: true, Result: not null } ? [..r.Result] : []);
        ;
        return result;
    }

    public async Task<ICollection<WmStellplatz>> GetNextStellplatzByPalletTypeForRobotAsync(string pallettype)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    [stellplaetze].*
                    FROM [stellplaetze]
                    INNER JOIN [ebenen] ON [stellplaetze].[wm_ebenen_id] = [ebenen].[id]
                    INNER JOIN [felder] ON [ebenen].[wm_feld_id] = [felder].[id]
                    WHERE [stellplaetze].[status] = @Status
                    AND EXISTS (
                          SELECT 1
                          FROM OPENJSON([stellplaetze].[possible_pallet_type])
                          WHERE value = @StellplatzPalletType
                    )
                    AND ([felder].[pallet_type] LIKE @Feldpallettype 
                    OR [felder].[pallet_type] LIKE @palletTypeEmpty)
                    AND [stellplaetze].[automatic] = 1
                    """;
        var parameters = new
        {
            Status = WmStellplatzStatus.EmptyStellplatz.value, 
            StellplatzPalletType = pallettype,
            Feldpallettype = pallettype, 
            palletTypeEmpty = string.Empty
        };
        var result = await connection
            .QueryAsync<WmStellplatz>(query, parameters).ContinueWith<ICollection<WmStellplatz>>(r =>
                r is { IsCompletedSuccessfully: true, Result: not null } ? [..r.Result] : []);
        ;
        return result;
    }

    public async Task<WmFeld> GetFeldOfStellplatz(Guid stellplatzId)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    [felder].[id] AS Id,
                    [felder].[description] AS Description,
                    [felder].[number] AS Number,
                    [felder].[wm_reihe_id] AS WmReiheId,
                    [felder].[pallet_type] AS PalletType
                    FROM [felder]
                    JOIN [ebenen] ON [felder].[id] = [ebenen].[wm_feld_id]
                    JOIN [stellplaetze] ON [ebenen].[id] = [stellplaetze].[wm_ebenen_id]
                    WHERE [stellplaetze].[id] = @StellplatzId
                    """;
        var parameters = new { StellplatzId = stellplatzId };
        var result = await connection
            .QueryFirstAsync<WmFeld>(query, parameters);
        return result;
    }

    public async Task<ICollection<WmStellplatz>> GetEmptyStellplaetzeByFeldId(List<Guid> requestFeldIds)
    {
        await using var connection = connectionBuilder.GetConnection();
        var query = """
                    SELECT
                    *
                    FROM [stellplaetze]
                    JOIN [ebenen] ON [stellplaetze].[wm_ebenen_id] = [ebenen].[id]
                    JOIN [felder] ON [ebenen].[wm_feld_id] = [felder].[id]
                    WHERE [felder].[id] IN @FeldIds

                    """;
        var parameters = new { FeldId = requestFeldIds };
        var result = await connection
            .QueryAsync<WmStellplatz>(query, parameters)
            .ContinueWith<ICollection<WmStellplatz>>(r =>
                r is { IsCompletedSuccessfully: true, Result: not null } ? [..r.Result] : []);
        return result;
    }

    public async Task<ICollection<HallenPositionInfo>> GetHallenPosInfoListOfEmptyStellplatzByPalletType(string palletType)
    {
        await using var connection = connectionBuilder.GetConnection();
        var sql =
            """
            SELECT 
                h.[id] AS HallenId,
                h.[number] AS HallenNumber,
                r.[id] AS ReiheId,
                r.[Number] AS ReiheNumber,
                f.[id] AS FeldId,
                f.[number] AS FeldNumber,
                e.[id] AS EbeneId,
                e.[number] AS EbeneNumber,
                s.[id] AS StellplatzId,
                s.[number] AS StellplatzNumber
            FROM [stellplaetze] s
            INNER JOIN [ebenen] e ON s.[wm_ebenen_id] = e.[id]
            INNER JOIN [felder] f ON e.[wm_feld_id] = f.[id]
            INNER JOIN [reihen] r ON f.[wm_reihe_id] = r.[id]
            INNER JOIN [halls] h ON r.[wm_hall_id] = h.[id]
            WHERE s.[status] = @Status
              AND (f.[pallet_type] LIKE @FeldPalletType OR f.[pallet_type] = @palletTypeEmpty)
              AND EXISTS (
                    SELECT 1
                    FROM OPENJSON(s.[possible_pallet_type])
                    WHERE value = @FeldPalletType 
              )
            ORDER BY h.[number], r.[number], f.[number], e.[number], s.[number];
            """;
        var parameters = new
            { Status = WmStellplatzStatus.EmptyStellplatz.value, FeldPalletType = palletType, palletTypeEmpty = string.Empty };
        var result = await connection
            .QueryAsync<HallenPositionInfo>(sql, parameters)
            .ContinueWith<ICollection<HallenPositionInfo>>(r =>
                r is { IsCompletedSuccessfully: true, Result: not null } ? [..r.Result] : []);
        return result;
    }
}