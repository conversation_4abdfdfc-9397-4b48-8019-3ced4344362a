using System.Data.Common;
using Microsoft.Data.SqlClient;
using WingCore.application.Contract;
using wingLager.application.Contracts;

namespace wingLager.data.Services;

public interface IWingLagerDbConnectionBuilder
{
    public DbConnection GetConnection();

}

public class WingLagerDbConnectionBuilder( IWingLagerDbContextAccessor? wingLagerDbContextAccessor) : IWingLagerDbConnectionBuilder
{
    public DbConnection GetConnection()
    {
        return new SqlConnection(wingLagerDbContextAccessor?.GetConnectionString());
    }
}