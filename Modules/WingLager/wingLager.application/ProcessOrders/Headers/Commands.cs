using WingCore.domain.Models;
using MediatR;
using wingLager.domain.Common;
using wingLager.domain.ProcessOrders;

namespace wingLager.application.ProcessOrders.Headers;

public sealed record CreateWithOutSaveProcessOrderHeaderCommand(Artikel SelectedMainArticle, decimal MaxWeightIngKg, Dictionary<Auftragskopf, List<Auftragspo>> OrderHeaderWithPos) : IRequest<ProcessOrderHeader>;

public sealed record CreateProcessOrderHeaderCommand(ProcessOrderHeader ProcessOrderHeader) : IRequest<ProcessOrderHeader>;

public interface IProcessOrderHeaderCommand
{
    long ProcessOrderHeaderId { get; init; }
}

public sealed record ModifyProcessOrderHeaderCommand(ProcessOrderHeader ProcessOrderHeader) : IRequest;
public sealed record DeleteProcessOrderHeaderCommand(long ProcessOrderHeaderId) : IProcessOrderHeader<PERSON>ommand, IRequest;

public record UpdateProcessOrderHeaderStatusCommand(long Id, ProcessOrderHeaderStatus Status) : IRequest;