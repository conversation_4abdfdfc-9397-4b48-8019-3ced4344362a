using MediatR;
using wingLager.application.Contracts;
using wingLager.domain.Common;

namespace wingLager.application.ProcessOrders.Headers.Notifications.Handlers;

public class CheckStorageStatusNotificationHandler(IProcessOrderHeaderRepository processOrderHeaderRepository) : INotificationHandler<CheckStorageStatusNotification>

{
    public async Task Handle(CheckStorageStatusNotification notification, CancellationToken cancellationToken)
    {
        var processOrderHeader = await processOrderHeaderRepository.GetByIdAsync(notification.ProcessOrderHeaderId,false);
        if(processOrderHeader is null)
            throw new Exception("ProcessOrderHeader not found");
        var allPalletLager = await processOrderHeaderRepository.GetAllPalletLagerOfProcessOrderHeader(notification.ProcessOrderHeaderId);
        if (allPalletLager.All(p => p.FlagsWorker.IsStoredInWareHouseFlagSet()))
        {
            processOrderHeader.Status = ProcessOrderHeaderStatus.InWareHouse;
            await processOrderHeaderRepository.Update(processOrderHeader,"System");
        }

        if (allPalletLager.All(p => p.FlagsWorker.IsCommissionFlagSet() || p.FlagsWorker.IsManualUnStoredFlagSet()))
        {
            processOrderHeader.Status = ProcessOrderHeaderStatus.Commissioned;
            await processOrderHeaderRepository.Update(processOrderHeader,"System");       
        }
    }
}