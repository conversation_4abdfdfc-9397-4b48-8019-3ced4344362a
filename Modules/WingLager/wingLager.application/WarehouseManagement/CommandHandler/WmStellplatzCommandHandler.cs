using MediatR;
using wingLager.application.Contracts;
using wingLager.application.Contracts.WarehouseManagementRepositories;
using wingLager.application.IServices.LindeQueueIService;
using wingLager.application.IServices.WarehouseManagementIServices;
using wingLager.application.WarehouseManagement.Commands;
using wingLager.domain.WarehouseManagementModels;
using wingLager.domain.WarehouseManagementModels.Status;

namespace wingLager.application.WarehouseManagement.CommandHandler;

public class CreateWmStellplatzCommandHandler(IWmStellplatzRepository repository)
    : IRequestHandler<CreateWmStellplatzCommand, WmStellplatz>
{
    public async Task<WmStellplatz> Handle(CreateWmStellplatzCommand request, CancellationToken cancellationToken)
    {
        var ebeneEntry = WmStellplatz.Create(request.Description, request.Number, request.EbeneId, null, null,
            request.PossiblePalletType, request.Status, request.Length, request.Height, request.Width,
            request.MaxWeight, request.Automatic);
        ebeneEntry.FlagWorker.SetStellplatzFlag();
        await repository.AddAsync(ebeneEntry, cancellationToken);
        return ebeneEntry;
    }
}

public record CreateWepStellplatzCommandHandler(IWmStellplatzRepository Repository, IWmStellplatzService Service)
    : IRequestHandler<CreateWepStellplatzCommand, WmStellplatz?>
{
    public async Task<WmStellplatz?> Handle(CreateWepStellplatzCommand request, CancellationToken cancellationToken)
    {
        var wepList = await Service.GetAllWepStellplaetzeAsync();
        if(wepList.Count >= 1)
            return null;
        var ebeneEntry = WmStellplatz.Create(request.Description, request.Number, null, null, request.WepId, [],
            WmStellplatzStatus.EmptyStellplatz, 0, 0, 0, 0, true);
        ebeneEntry.FlagWorker.SetWepFlag();
        await Repository.AddAsync(ebeneEntry, cancellationToken);
        return ebeneEntry;
    }
}

public record CreateVbrStellplatzCommandHandler(IWmStellplatzRepository Repository)
    : IRequestHandler<CreateVbrStellplatzCommand, WmStellplatz>
{
    public async Task<WmStellplatz> Handle(CreateVbrStellplatzCommand request, CancellationToken cancellationToken)
    {
        var ebeneEntry = WmStellplatz.Create(request.Description, request.Number, null, request.VbrId, null, [],
            WmStellplatzStatus.EmptyStellplatz, 0, 0, 0, 0, true);
        ebeneEntry.FlagWorker.SetVbrFlag();
        await Repository.AddAsync(ebeneEntry, cancellationToken);
        return ebeneEntry;
    }
}

public record CreateWmStellplatzFromListCommandHandler(IWmStellplatzRepository Repository)
    : IRequestHandler<CreateWmStellplatzFromListCommand>
{
    public async Task Handle(CreateWmStellplatzFromListCommand request, CancellationToken cancellationToken)
    {
        await Repository.AddRangeAsync(request.Stellplaetze, cancellationToken);
    }
}

public record UpdateWmStellplatzCommandHandler(IWmStellplatzRepository Repository)
    : IRequestHandler<UpdateWmStellplatzCommand>
{
    public async Task Handle(UpdateWmStellplatzCommand request, CancellationToken cancellationToken)
    {
        await Repository.UpdateAsync(request.Stellplatz, cancellationToken);
    }
}

public record UpdateWmStellplatzFromListCommandHandler(IWmStellplatzRepository Repository)
    : IRequestHandler<UpdateWmStellplatzFromListCommand>
{
    public async Task Handle(UpdateWmStellplatzFromListCommand request, CancellationToken cancellationToken)
    {
        await Repository.UpdateRangeAsync(request.Stellplaetze, cancellationToken);
    }
}

public record DeleteWmStellplatzCommandHandler(IWmStellplatzRepository Repository)
    : IRequestHandler<DeleteWmStellplatzCommand>
{
    public async Task Handle(DeleteWmStellplatzCommand request, CancellationToken cancellationToken)
    {
        var ebeneEntry = await Repository.GetAsync(request.StellplatzId, cancellationToken);
        if (ebeneEntry is not null)
        {
            await Repository.DeleteAsync(ebeneEntry, cancellationToken);
        }
    }
}

public class StorePalletOnStellplatzCommandHandler(IWmStellplatzRepository stellplatzRepository, IWmStellplatzService stellplatzService, IWmFeldRepository feldRepository)
    : IRequestHandler<StorePalletOnStellplatzCommand>
{
    public async Task Handle(StorePalletOnStellplatzCommand request, CancellationToken cancellationToken)
    {
        try
        {
            var (stellplatzId, ean, env, palletDescription, referenceNumber, loadType, palletLagerId, pallettType ) = request;
            var stellplatz = await stellplatzRepository.GetAsync(stellplatzId, cancellationToken);

            if (stellplatz is null ||
                !stellplatz.Status.IsEmptyStellplatz)
                throw new Exception("Stellplatz ist nicht vorhanden oder ist bereits belegt");

            stellplatz.Ean = ean ?? string.Empty;
            stellplatz.Nve = env ?? string.Empty;
            stellplatz.PalletDescription = palletDescription ?? string.Empty;
            stellplatz.ReferenceNumber = referenceNumber ?? string.Empty;
            stellplatz.LoadType = loadType;
            stellplatz.Status = WmStellplatzStatus.OccupiedStellplatz;
            stellplatz.PalletLagerId = palletLagerId;
            await stellplatzRepository.UpdateAsync(stellplatz, cancellationToken);
            
            var feld = await stellplatzService.GetFeldOfStellplatz(stellplatzId);
            
            if (string.IsNullOrEmpty(feld.PalletType))
            {
                feld.PalletType = pallettType;
                await feldRepository.UpdateAsync(feld, cancellationToken);
            }
        }
        catch (Exception e)
        {
            throw new Exception(e.InnerException?.Message ?? e.Message);
        }
    }
}

public class UnStorePalletOnStellplatzCommandHandler(
    IWmStellplatzRepository stellplatzRepository,
    IWmStellplatzService stellplatzService,
    IPaletLagerRepository paletLagerRepository, ILindeQueueService lindeQueueService) : IRequestHandler<UnStorePalletOnStellplatzCommand>
{
    public async Task Handle(UnStorePalletOnStellplatzCommand request, CancellationToken cancellationToken)
    {
        var lindeQueue = await lindeQueueService.GetAllOpenEntries();
        var hallPosition = await stellplatzService.GetHallenPositionInfoByStellplatzIdAsync(request.StellplatzId);
        if(hallPosition is null)
            return;
        if(lindeQueue.Any(l => l.Destination == hallPosition.HallenPosition))
            throw new Exception("Es befindet sich eine Pallete auf dem Weg zum Stellplatz");
        
        var stellplatz = await stellplatzService.GetWmStellplatzByIdAsync(request.StellplatzId);
        if (stellplatz?.PalletLagerId is null)
            return;
        var paletLagerOld = await paletLagerRepository.GetByIdWithoutTrackingAsync(stellplatz.PalletLagerId.Value);
        if(paletLagerOld is null)
            return;
        paletLagerOld.FlagsWorker.RemoveStoredInWareHouseFlag();
        paletLagerOld.FlagsWorker.SetManualUnStoredFlag();
        await paletLagerRepository.UpdateWithoutTracking(paletLagerOld, "");
        stellplatz.ClearStellplatz();
        
        await stellplatzRepository.UpdateAsync(stellplatz, cancellationToken);
        if (stellplatz.WmEbenenId != null)
        {
            var stellplatzList =
                (await stellplatzService.GetWmStellplaetzeByEbenenIddAsync(stellplatz.WmEbenenId.Value)).ToList();
        
            if (stellplatzList.All(x => x.Status != WmStellplatzStatus.OccupiedStellplatz))
            {
                foreach (var entry in stellplatzList)
                {
                    entry.Status = WmStellplatzStatus.EmptyStellplatz;
                }

                await stellplatzRepository.UpdateRangeAsync(stellplatzList, cancellationToken);
            }
        }
    }
}

public class ClearStellplatzCommandHandler(IWmStellplatzService stellplatzService, IWmStellplatzRepository stellplatzRepository) : IRequestHandler<ClearStellplatzCommand>
{
    public async Task Handle(ClearStellplatzCommand request, CancellationToken cancellationToken)
    {
        var stellplatz = await stellplatzService.GetByHallpositionString(request.HallPosition);
        if(stellplatz is null || (stellplatz.Status.IsEmptyStellplatz && stellplatz.PalletLagerId is null) || (stellplatz.PalletLagerId != request.PalletLagerId))
            return;
        stellplatz.Status = WmStellplatzStatus.EmptyStellplatz;
        stellplatz.PalletLagerId = null;
        stellplatz.PalletDescription = string.Empty;
        stellplatz.ReferenceNumber = string.Empty;
        stellplatz.Ean = string.Empty;
        stellplatz.Nve = string.Empty;
        await stellplatzRepository.UpdateAsync(stellplatz, cancellationToken);
    }
}