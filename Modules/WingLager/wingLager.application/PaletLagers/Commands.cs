using MediatR;
using wingLager.application.Contracts.IRequests;
using wingLager.domain.PaletLagers;
using wingLager.domain.WarehouseManagementModels;

namespace wingLager.application.PaletLagers;


public record UpdatePaletLagerCommand(PaletLager PaletLager, string UserName);
public record SetFlagPrintedStartPaletLagerCommand(long Id) : IRequest<PaletLager>;
public record RemoveFlagPrintedStartPaletLagerCommand(long Id) : IRequest<PaletLager>;

public record CreateManualPaletLagerEntryCommand(WmEinlagerung Einlagerung) : ICommand<PaletLager>;

public record SetInWareHouseFlagCommand(long Id) : ICommand<PaletLager>;
public record SetManualUnstoredFlagCommand(long Id) : ICommand;

public record SetCommissionedFlagCommand(long Id) : ICommand;

public record SetLindeQueueCreatedFlagCommand(long Id) : ICommand<PaletLager>;
public record RemoveLindeQueueCreatedFlagCommand(long Id) : ICommand<PaletLager>;

public record SetLindeQueuePickFlagCommand(long Id) : ICommand<PaletLager>;
public record RemoveLindeQueuePickFlagCommand(long Id) : ICommand<PaletLager>;

public record SetLindeQueueWaitingFlagCommand(long Id) : ICommand<PaletLager>;
public record RemoveLindeQueueWaitingFlagCommand(long Id) : ICommand<PaletLager>;

public record SetLindeQueueDropFlagCommand(long Id) : ICommand<PaletLager>;
public record RemoveLindeQueueDropFlagCommand(long Id) : ICommand<PaletLager>;

public record SetLindeQueueDoneFlagCommand(long Id) : ICommand<PaletLager>;
public record RemoveLindeQueueDoneFlagCommand(long Id) : ICommand<PaletLager>;

public record SetLindeQueueErrorFlagCommand(long Id) : ICommand<PaletLager>;
public record RemoveLindeQueueErrorFlagCommand(long Id) : ICommand<PaletLager>;