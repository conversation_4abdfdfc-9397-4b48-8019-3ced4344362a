namespace wingLager.domain.Common;

public record ProcessOrderHeaderStatus(string Value) : IComparable<ProcessOrderHeaderStatus>
{
    public const string OpenString = "OPEN";
    public const string DoneString = "DONE";
    public const string PlanedString = "PLANED";
    public const string InPrintingString = "INPRINTING";
    public const string IsPrintedString = "ISPRINTED";
    public const string InWareHouseString = "INWAREHOUSE";
    public const string InShippedString = "SHIPPED";
    public const string CommissionedString = "COMMISSIONED";
    public const string InCommissionString = "INCOMMISSION";
    
    public static readonly ProcessOrderHeaderStatus Empty = new("");
    public static readonly ProcessOrderHeaderStatus Open = new(OpenString);
    public static readonly ProcessOrderHeaderStatus Done  = new(DoneString);
    public static readonly ProcessOrderHeaderStatus Planed = new(PlanedString);
    public static readonly ProcessOrderHeaderStatus InPrinting = new(InPrintingString);
    public static readonly ProcessOrderHeaderStatus IsPrinted = new(IsPrintedString);
    public static readonly ProcessOrderHeaderStatus InWareHouse = new(InWareHouseString);
    public static readonly ProcessOrderHeaderStatus InCommission = new(InCommissionString);
    public static readonly ProcessOrderHeaderStatus Commissioned = new(CommissionedString);
    public static readonly ProcessOrderHeaderStatus InShipped = new(InShippedString);


    public bool IsOpen => Value == OpenString;
    public bool IsDone => Value == DoneString;
    public bool IsPlaned => Value == PlanedString;
    public bool IsInPrinting => Value == InPrintingString;
    public bool IsInWareHouse => Value == InWareHouseString;
    public bool IsInShipped => Value == InShippedString;

    
    public override string ToString()
    {
        return Value switch
        {
            OpenString => "Offen",
            DoneString => "Fertig",
            PlanedString => "Geplant",
            InPrintingString => "Drucken",
            IsPrintedString => "Fertig gedruckt",
            InWareHouseString => "Im Lager",
            InShippedString => "Verladen",
            _ => string.Empty
        };
    }
    
    public static implicit operator ProcessOrderHeaderStatus(string searchString)
    {
        if(Open.ToString().Contains(searchString, StringComparison.CurrentCultureIgnoreCase))
            return Open;
        if(Done.ToString().Contains(searchString, StringComparison.CurrentCultureIgnoreCase))
            return Done;
        if(Planed.ToString().Contains(searchString, StringComparison.CurrentCultureIgnoreCase))
            return Planed;
        if(InPrinting.ToString().Contains(searchString, StringComparison.CurrentCultureIgnoreCase))
            return InPrinting;
        if(InWareHouse.ToString().Contains(searchString, StringComparison.CurrentCultureIgnoreCase))
            return InWareHouse;
        if(InShipped.ToString().Contains(searchString, StringComparison.CurrentCultureIgnoreCase))
            return InShipped;
        if(IsPrinted.ToString().Contains(searchString, StringComparison.CurrentCultureIgnoreCase))
            return IsPrinted;
        
        return Empty;
    }
    
    public int CompareTo(ProcessOrderHeaderStatus? postingsSchema)
    {
        return string.Compare(Value, postingsSchema?.Value, StringComparison.Ordinal);
    }
    public override int GetHashCode() => Value.GetHashCode();
    
    public virtual bool Equals(ProcessOrderHeaderStatus? postingsSchema)
    {
        return Value == postingsSchema?.Value;
    }
}