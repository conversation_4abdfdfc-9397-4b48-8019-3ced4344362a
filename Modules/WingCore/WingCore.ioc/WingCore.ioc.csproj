<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\libs\ElectronicInvoices\EDIFACT\EDIFACT.csproj" />
        <ProjectReference Include="..\..\..\libs\ElectronicInvoices\ZUGFeRDXRechnung\ZUGFeRDXRechnung.csproj" />
        <ProjectReference Include="..\..\..\libs\Exports\AddisonInvoiceExports\AddisonInvoiceExports.csproj" />
        <ProjectReference Include="..\..\..\libs\Exports\DatevExport\DatevExport.csproj" />
        <ProjectReference Include="..\..\Licenses\licenses.domain\licenses.domain.csproj" />
        <ProjectReference Include="..\WingCore.data\WingCore.data.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
      <PackageReference Include="FluentValidation" Version="12.0.0" />
      <PackageReference Include="FluentValidation.DependencyInjectionExtensions" Version="12.0.0" />
    </ItemGroup>

</Project>
