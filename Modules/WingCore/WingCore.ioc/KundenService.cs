using Commons.Dtos;

using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using WingCore.domain.Exceptions;
using WingCore.domain.Mappers;
using WingCore.domain.Models;

namespace WingCore.ioc;

public class KundenService(IKundenRepository kundenRepository)
    : IKundenService
{
    public IEnumerable<Kunden> GetAllCustomers(bool trackChanges)
    {
        var customers = kundenRepository.GetAllCostumers(false).ToList();
        if (customers.Count == 0) throw new KundenNotFoundException();
        return customers;
    }

    public ValueTask<Kunden?> GetCustomer(bool trackChanges,long id) => kundenRepository.GetCostumer(false,id);

    public ValueTask<Kunden?> GetCustomerOverNumber(bool trackChanges, long number)
    {
        var result = kundenRepository.GetCustomerOverNumber(false, number);
        kundenRepository.ClearChangeTracker();
        return result;
    }

    public async Task<CustomerDto?> GetCustomerDtoOverNumber(long number)
    {
        var result = await kundenRepository.GetCustomerOverNumber(false, number);
        
        return result is null ? null : KundenMapper.MapToDto(result);
    }

    public ValueTask<IEnumerable<Kunden>> GetCustomer(bool trackChanges, CustomerSearchParam customerSearchParam)
        => kundenRepository.GetCustomerOverSearchParam(trackChanges, customerSearchParam);

    public async Task Update(Kunden customer)
    {
        kundenRepository.Update(customer);
        await kundenRepository.SaveChangesAsync();
        kundenRepository.ClearChangeTracker();
    } 
}