using MediatR;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Routing;
using WingCore.application.Api;
using WingCore.application.ApiDtos.OrderData;
using WingCore.application.ApiDtos.OrderData.Create;
using WingCore.application.ApiDtos.OrderData.Put;
using WingCore.ioc.Endpoints.Filter;
using WingCore.ioc.Endpoints.Validators.Create;


namespace WingCore.ioc.Endpoints;

public static class OrderDataEndpoints
{
    public static IEndpointRouteBuilder MapOrderDataEndpoints(this IEndpointRouteBuilder app)
    {
        var orderData = app.MapGroup("/OrderData").WithTags("OrderData");
        orderData.MapGet("/OrderHeaders", GetAllOrderHeader)
            .Produces<ICollection<OrderHeaderDtoV1>>().WithName("GetAllOrderHeaders");

        orderData.MapGet("/OrderHeaders/{category}", GetAllOrderHeaderByCategory)
            .Produces<ICollection<OrderHeaderDtoV1>>();

        orderData.MapGet("/OrderLines", GetAllOrderLines)
            .Produces<ICollection<OrderLineDtoV1>>().WithName("GetAllOrderLines");

        orderData.MapGet("/OrderHeaderWithOrderLines", GetAllOrderHeaderWithOrderLines)
            .Produces<ICollection<OrderHeaderDtoV1>>();

        orderData.MapGet("/OrderHeaderWithOrderLines/{category}", GetAllOrderHeaderWithOrderLinesByCategory)
            .Produces<ICollection<OrderHeaderDtoV1>>();

        orderData.MapGet("/OrderHeaderWithOrderLines/{orderNumber:long}", GetOrderHeaderByOrderNumberWithOrderLines)
            .Produces<OrderHeaderDtoV1>();

        orderData.MapGet("/OrderLines/{orderNumber:long}", GetOrderLinesByOrderNumber)
            .Produces<ICollection<OrderLineDtoV1>>();

        orderData.MapGet("/OrderHeaders/{orderNumber:long}", GetOrderHeaderByOrderNumber)
            .Produces<OrderHeaderDtoV1>();

        orderData.MapPut("/OrderHeaders/{orderNumber:long}", UpdateOrderHeader)
            .WithRequestValidation<OrderHeaderPutDtoV1>()
            .Produces(statusCode: StatusCodes.Status200OK);

        orderData.MapPut("/OrderLine/{id:long}", UpdateOrderLine).WithRequestValidation<OrderLinePutDtoV1>()
            .Produces(statusCode: StatusCodes.Status200OK);
        orderData.MapPost("/OrderHeaders", AddNewOrderHeader).WithRequestValidation<OrderHeaderCreateDtoV1>()
            .Produces<long>(statusCode: StatusCodes.Status200OK);
        return app;
    }


    private static async Task<IResult> AddNewOrderHeader([FromServices] IMediator mediator,
        OrderHeaderCreateDtoV1 orderHeaderCreate)
    {
        try
        {
            var ordernumber = await mediator.Send(new CreateOrderHeaderCommand(orderHeaderCreate));
            return Results.Ok(ordernumber);
        }
        catch (Exception e)
        {
            return Results.BadRequest(e.InnerException?.Message ?? e.Message);
        }
    }

    private static async Task<IResult> UpdateOrderLine(long id, OrderLinePutDtoV1 orderLine,
        [FromServices] IMediator mediator)
    {
        if (id != orderLine.Id)
            return Results.BadRequest("The Id and Objekt Id are not equal ");
        try
        {
            await mediator.Send(new UpdateOderLineCommand(orderLine));
            return Results.Ok();
        }
        catch (Exception e)
        {
            return Results.BadRequest(e.InnerException?.Message ?? e.Message);
        }
    }

    private static async Task<IResult> UpdateOrderHeader(long orderNumber, OrderHeaderPutDtoV1 orderHeader,
        [FromServices] IMediator mediator)
    {
        if (orderNumber != orderHeader.OrderNumber)
            return Results.BadRequest("The Id and Objekt Id are not equal ");
        try
        {
            await mediator.Send(new UpdateOrderHeaderCommand(orderHeader));
            return Results.Ok();
        }
        catch (Exception e)
        {
            return Results.BadRequest(e.InnerException?.Message ?? e.Message);
        }
    }

    private static async Task<IResult> GetOrderHeaderByOrderNumber(long orderNumber, [FromServices] IMediator mediator,
        CancellationToken cancellationToken)
    {
        try
        {
            var orderHeader = await mediator.Send(new GetOrderHeaderByOrderNumberQuery(orderNumber, false),
                cancellationToken);
            return TypedResults.Ok(orderHeader);
        }
        catch (Exception e)
        {
            return TypedResults.NotFound(e.InnerException?.Message ?? e.Message);
        }
    }

    private static async Task<IResult> GetOrderLinesByOrderNumber(long orderNumber, [FromServices] IMediator mediator,
        CancellationToken cancellationToken)
    {
        try
        {
            var orderline = await mediator.Send(new GetOrderLineByOrderNumberQuery(orderNumber), cancellationToken);
            return TypedResults.Ok(orderline);
        }
        catch (Exception e)
        {
            return TypedResults.NotFound(e.InnerException?.Message ?? e.Message);
        }
    }

    private static async Task<IResult> GetAllOrderLines([FromServices] IMediator mediator,
        CancellationToken cancellationToken)
    {
        var orderLineList = await mediator.Send(new GetAllOrderLineQuery(), cancellationToken);
        return TypedResults.Ok(orderLineList);
    }

    private static async Task<IResult> GetOrderHeaderByOrderNumberWithOrderLines(long orderNumber,
        [FromServices] IMediator mediator,
        CancellationToken cancellationToken)
    {
        try
        {
            var orderHeader = await mediator.Send(new GetOrderHeaderByOrderNumberQuery(orderNumber, true),
                cancellationToken);
            return TypedResults.Ok(orderHeader);
        }
        catch (Exception e)
        {
            return TypedResults.NotFound(e.InnerException?.Message ?? e.Message);
        }
    }

    private static async Task<IResult> GetAllOrderHeaderWithOrderLines([FromServices] IMediator mediator,
        CancellationToken cancellationToken)
    {
        var orderheaderList = await mediator.Send(new GetAllOrderHeaderQuery(true), cancellationToken);
        return TypedResults.Ok(orderheaderList);
    }

    private static async Task<IResult> GetAllOrderHeaderWithOrderLinesByCategory([FromServices] IMediator mediator,
        string category,
        CancellationToken cancellationToken)
    {
        var allowedValues = OrderHeaderCategoryV1.GetAllowedValues();
        if (!allowedValues.Contains(category))
            return TypedResults.BadRequest(
                $"Invalid Status '{category}'. Allowed Values are: {string.Join(", ", allowedValues)}");
        var orderheaderList = await mediator.Send(new GetAllOrderHeaderQuery(true, category), cancellationToken);
        return TypedResults.Ok(orderheaderList);
    }

    private static async Task<IResult> GetAllOrderHeaderByCategory([FromServices] IMediator mediator, string category,
        CancellationToken cancellationToken)
    {
        var allowedValues = OrderHeaderCategoryV1.GetAllowedValues();
        if (!allowedValues.Contains(category))
            return TypedResults.BadRequest(
                $"Invalid Status '{category}'. Allowed Values are: {string.Join(", ", allowedValues)}");
        var orderheaderList = await mediator.Send(new GetAllOrderHeaderQuery(false, category), cancellationToken);
        return TypedResults.Ok(orderheaderList);
    }

    private static async Task<IResult> GetAllOrderHeader([FromServices] IMediator mediator,
        CancellationToken cancellationToken)
    {
        var orderheaderList = await mediator.Send(new GetAllOrderHeaderQuery(false), cancellationToken);
        return TypedResults.Ok(orderheaderList);
    }
}