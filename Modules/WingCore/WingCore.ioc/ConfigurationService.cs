using WingCore.domain.Models;
using ObjectDeAndSerialize;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;
using WingCore.domain.Common.Configurations;

namespace WingCore.ioc;

internal sealed class ConfigurationService(IConfigurationRepository configurationRepository)
    : IConfigurationService
{

   public async ValueTask<ConfigurationDatev> GetConfigurationDatevAsync(bool trackChanges = false) => await GetConfiguration<ConfigurationDatev>(ConfigurationType.DatevExportConfiguration, trackChanges);
   public async ValueTask<ConfigurationEdifact> GetConfigurationEdifactAsync(bool trackChanges = false) => await GetConfiguration<ConfigurationEdifact>(ConfigurationType.EdifactExportConfiguration, trackChanges);
   public async ValueTask<ConfigurationAddisonTseNit> GetConfigurationAddisonTseNitAsync(bool trackChanges = false) => await GetConfiguration<ConfigurationAddisonTseNit>(ConfigurationType.AddisonTseNitExportConfiguration, trackChanges);
   public async ValueTask<ConfigurationAddisonPipeline> GetConfigurationAddisonPipelineAsync(bool trackChanges = false) => await GetConfiguration<ConfigurationAddisonPipeline>(ConfigurationType.AddisonPipelineExportConfiguration, trackChanges);
   public async ValueTask<ConfigurationElectricInvoice> GetConfigurationElectricInvoiceAsync(bool trackChanges = false) => await GetConfiguration<ConfigurationElectricInvoice>(ConfigurationType.ElectricInvoiceConfiguration, trackChanges);
   public async ValueTask<ConfigurationElectricWgs> GetConfigurationElectricWgsAsync(bool trackChanges = false) => await GetConfiguration<ConfigurationElectricWgs>(ConfigurationType.ElectricWgsConfiguration, trackChanges);
   public async ValueTask<ConfigurationLindeRoboterManager> GetConfigurationLindeRoboterManagerAsync(bool trackChanges = false) => await GetConfiguration<ConfigurationLindeRoboterManager>(ConfigurationType.LindeRoboterManagerConfiguration, trackChanges);
   public ValueTask<ConfigurationEasiAdfinityRestApi> GetConfigurationEasiAdfinityRestApiAsync(bool trackChanges = false) => GetConfiguration<ConfigurationEasiAdfinityRestApi>(ConfigurationType.EasiAdfinityRestApiConfiguration, trackChanges);
   public ValueTask<ConfigurationCommission> GetConfigurationCommissionAsync(bool trackChanges = false) => GetConfiguration<ConfigurationCommission>(ConfigurationType.CommissionConfiguration, trackChanges);

   private async ValueTask<T> GetConfiguration<T>(ConfigurationType configurationType, bool trackChanges = false)
   where T : ConfigurationBase, new()
   {
       var config = await configurationRepository.GetAsync(configurationType, trackChanges);

       if (config is null)
           return new T();

       T configObject;
       
       try
       {
           configObject = config.ConfigData?.Deserialize<T>() ?? new T();    
       }
       catch (Exception)
       {
           configObject = new T();
       }

       configObject.Id = config.Id;

       return configObject;
   }

   public async ValueTask<long> SetConfigurationDatevAsync(ConfigurationDatev configuration) => await SetConfigurationAsync(configuration, ConfigurationType.DatevExportConfiguration);
   public async ValueTask<long> SetConfigurationEdifactAsync(ConfigurationEdifact configuration) => await SetConfigurationAsync(configuration, ConfigurationType.EdifactExportConfiguration);
   public async ValueTask<long> SetConfigurationAddisonTseNitAsync(ConfigurationAddisonTseNit configuration) => await SetConfigurationAsync(configuration, ConfigurationType.AddisonTseNitExportConfiguration);
   public async ValueTask<long> SetConfigurationAddisonPipelineAsync(ConfigurationAddisonPipeline configuration) => await SetConfigurationAsync(configuration, ConfigurationType.AddisonPipelineExportConfiguration);
   public async ValueTask<long> SetConfigurationElectricInvoiceAsync(ConfigurationElectricInvoice configuration) => await SetConfigurationAsync(configuration, ConfigurationType.ElectricInvoiceConfiguration);
   public async ValueTask<long> SetConfigurationElectricWgsAsync(ConfigurationElectricWgs configuration) => await SetConfigurationAsync(configuration, ConfigurationType.ElectricWgsConfiguration);
   public async ValueTask<long> SetConfigurationLindeRoboterManagerAsync(ConfigurationLindeRoboterManager configuration) => await SetConfigurationAsync(configuration, ConfigurationType.LindeRoboterManagerConfiguration);
   public ValueTask<long> SetConfigurationEasiAdfinityRestApiAsync(ConfigurationEasiAdfinityRestApi configuration) => SetConfigurationAsync(configuration, ConfigurationType.EasiAdfinityRestApiConfiguration);
   public ValueTask<long> SetConfigurationCommissionAsync(ConfigurationCommission configuration) => SetConfigurationAsync(configuration, ConfigurationType.CommissionConfiguration);


   private async ValueTask<long> SetConfigurationAsync<T>(T configurationDatev, ConfigurationType configurationType)
    where T : ConfigurationBase
   {
       var config = await configurationRepository.GetAsync(configurationDatev.Id, false);
       
       if (config is null)
       {
           config = new Config(configurationType)
           {
               ConfigData = configurationDatev.SerializeToByteArray()
           };
           
           configurationRepository.Create(config);
       }
       else
       {
           config.ConfigData = configurationDatev.SerializeToByteArray();
           configurationRepository.Update(config);    
       }

       await configurationRepository.SaveChangesAsync();
       configurationRepository.ClearChangeTracker();
        
       return config.Id;
   }
}