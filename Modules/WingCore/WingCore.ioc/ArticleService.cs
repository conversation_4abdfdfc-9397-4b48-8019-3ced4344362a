using MediatR;
using WingCore.application.ARHauptdatei.Queries;
using WingCore.application.Aufträge.Auftragspositionen;
using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using WingCore.application.Getreideabrechnung.Positions;

namespace WingCore.ioc;

public class ArticleService(IArtikelRepository repository, IMediator mediator)
    : IArticleService
{
    public IEnumerable<Artikel> GetAll(bool trackChanges) => repository.GetAllArtikel(trackChanges).ToList();
    public async Task<IEnumerable<Artikel>> GetAllArticlesOverMainArticleNumberAsync(bool trackChanges, long mainArticleNumber) => await Get(trackChanges,new ArticleSearchParam(){MainArticleNumber = mainArticleNumber} );
    public IEnumerable<Artikel> GetMainArtikels(bool trackChanges) => repository.GetMainArtikels(trackChanges);

    public ValueTask<Artikel?> Get(bool trackChanges, long id) => repository.GetArtikelById(id,false);

    public async ValueTask<Artikel?> GetOverNumber(bool trackChanges, long number)
    {
        var allArticles = await Get(trackChanges,new ArticleSearchParam(){Number = number});
        
        return allArticles.FirstOrDefault();
    }

    public ValueTask<IEnumerable<Artikel>> Get(bool trackChanges, ArticleSearchParam searchParam)
        => repository.GetOverSearchParam(trackChanges, searchParam);

    public async Task Update(Artikel updatedObject)
    {
        await repository.UpdateArtikel(updatedObject);
        await repository.SaveChangesAsync();
        repository.ClearChangeTracker();
    }

    //Only used in Unit Tests
    public async Task Create(Artikel updatedObject)
    {
        await repository.UpdateArtikel(updatedObject);
        await repository.SaveChangesAsync();
        repository.ClearChangeTracker();
    }
    
    public async Task<Dictionary<long, int?>> GetArticleRefNumber(List<long> articleNumbers)
            => await repository.GetArticleRefNumber(articleNumbers);
    
    public async Task<bool> CanBeDeleted(long articleNumber)
    {
        var article = await repository.GetArtikelById(articleNumber, false);

        if (article is null)
            return false;
        
        var articleExistsInPosition = 
            await mediator.Send(new CheckArticleNumberExistsInPositionQuery(article.Artikelnr));
        var articleExistsInErPos =
            await mediator.Send(new CheckArticleNumberExistsInErPositionQuery(article.Artikelnr));
        var articleExistsInAuftragsPos = 
            await mediator.Send(new CheckArticleNumberExistsInAuftragsPosQuery(article.Artikelnr));
        var articleExistsInGbvPos=
            await mediator.Send(new CheckArticleNumberExistsInGbvPositionQuery(article.Artikelnr));
        var articleExistsInRgbvPos =
            await mediator.Send(new CheckArticleNumberExistsInRgbvPositionQuery(article.Artikelnr));
        
        if (articleExistsInPosition 
            || articleExistsInErPos
            || articleExistsInAuftragsPos
            || articleExistsInGbvPos
            || articleExistsInRgbvPos)
            return false;
    
        return true;
    }

    public async Task<long> GetNextArticleNumberFromDb()
    {
        var maxArticleNumber = await repository.GetMaxArticleNumber();
        var nextArticleNumber = maxArticleNumber < 1000 ? 1000 : maxArticleNumber + 1;
        
        return nextArticleNumber;
    }
}