using AdginityRestApi;
using EDIFACT;
using EDIFACT.TrueCommerce.Order.V1;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Identity;
using Microsoft.Extensions.DependencyInjection;
using WingCore.application.Contract;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.StammDb;
using WingCore.application.Contract.IModels.WarenEingangDb;
using WingCore.application.Contract.Services;
using WingCore.application.PriceList.IModels;
using WingCore.data;
using WingCore.data.Repositories;
using WingCore.data.Repositories.StammDB;
using WingCore.data.Repositories.WarenEingangDb;
using WingCore.data.StammContext;
using WingCore.data.WaWiContext;
using WingCore.domain;
using WingCore.ioc.Exports;
using WingCore.ioc.Middlewares;
using ZUGFeRDXRechnung;

namespace WingCore.ioc;

public static class ServiceManager
{
    public static void AddServicesWithNeededClasses(this IServiceCollection services)
    {
        services.AddScoped<IMandantRepository, MandantRepository>();
        services.AddScoped<ICountryRepository, CountryRepository>();
        services.AddScoped<IBundLandRepository, BundLandRepository>();
        services.AddScoped<ICompanyMasterDataRepository, CompanyMasterDataRepository>();
        services.AddScoped<IConfigurationForAllMandantRepository, ConfigurationForAllMandantRepository>();
        services.AddScoped<IAuftragskopfRepository, AuftragskopfRepository>();
        services.AddScoped<IApiKeyRepository, ApiKeyRepository>();
        
        services.AddScoped<IOutgoingInvoiceRepository, OutgoingInvoiceRepository>();
        services.AddScoped<IOutgoingInvoicePostingsRepository, ArhauptdateiRepository>();
        services.AddScoped<IIncomingInvoiceRepository, IncomingInvoiceRepository>();
        services.AddScoped<IConfigurationRepository, ConfigurationRepository>();
        services.AddScoped<IGrainSettlementInvoicePostingsRepository, GrainSettlementInvoicePostingsRepository>();
        services.AddScoped<IGrainSettlementInvoiceRepository, GrainSettlementInvoiceRepository>();
        services.AddScoped<IIncomingInvoicePostingsRepository, IncomingInvoicePostingsRepository>();
        services.AddScoped<IIncomingInvoiceRepository, IncomingInvoiceRepository>();
        services.AddScoped<IKundenRepository, KundenRepository>();
        services.AddScoped<ILieferantenRepository, LieferantenRepository>();
        services.AddScoped<IKontraktRepository, KontraktRepository>();
        services.AddScoped<IRapeSettlementInvoicePostingsRepository, RapeSettlementInvoicePostingsRepository>();
        services.AddScoped<IRapeSettlementInvoiceRepository, RapeSettlementInvoiceRepository>();
        services.AddScoped<IArtikelRepository, ArtikelRepository>();
        
        services.AddScoped<IGrainParametersService, GrainParametersService>();
        services.AddScoped<IGrainParametersRepository, GrainParametersRepository>();
        services.AddScoped<IAuftragsposRepository, AuftragsposRepository>();
        services.AddScoped<IPreislistenRepository, PreislistenRepository>();
        services.AddScoped<IKontraktRepository,KontraktRepository>();

        services.AddScoped<IOutgoingInvoiceHeaderService, OutgoingInvoiceHeaderService>();
        services.AddScoped<IOutgoingInvoicePostingsService, ArhauptdateiService>();
        
        services.AddScoped<IIncomingInvoiceHeaderService, IncomingInvoiceHeaderService>();
        services.AddScoped<IIncomingInvoicePostingsService, IncomingInvoicePostingsService>();
        
        services.AddScoped<IGrainSettlementInvoiceHeaderService, GrainSettlementInvoiceHeaderService>();
        services.AddScoped<IGrainSettlementInvoicePostingsService, GrainSettlementInvoicePostingsService>();
        
        services.AddScoped<IRapeSettlementInvoiceHeaderService, RapeSettlementInvoiceHeaderService>();
        services.AddScoped<IRapeSettlementInvoicePostingsService, RapeSettlementInvoicePostingsService>();
        services.AddScoped<IKundenService, KundenService>();
        services.AddScoped<IArticleService, ArticleService>();
        services.AddScoped<IFBERepository, FBERepository>();
        services.AddScoped<IWgsService, WgsService>();
        services.AddScoped<IWgsRepository, WgsRepository>();
        services.AddScoped<IWgslabRepository, WgslabRepository>();

        services.AddScoped<IGbvadrRepository, GbvadrRepository>();
        services.AddScoped<IGbvhptRepository, GbvhptRepository>();
        
        
        services.AddScoped<IDatevExportService, DatevExportService>();
        services.AddScoped<IConfigurationService, ConfigurationService>();
        services.AddScoped<IZugFeRdxRechnungInvoiceExport, ZugFeRdxRechnungInvoiceExport>();
        services.AddScoped<IEdifactExportImportService, EdifactExportImportService>();
        services.AddScoped<IAddisonInvoiceExportService, AddisonInvoiceExportService>();
        services.AddScoped<ICountryService, CountryService>();
        services.AddScoped<ICompanyMasterDataService, CompanyMasterDataService>();
        services.AddScoped<IConfigurationForAllMandantService, ConfigurationForAllMandantService>();
        services.AddScoped<IConvertEdifactToOrderV1, ConvertEdifactToOrderV1>();
        services.AddScoped<IOrderService, OrderService>();
        services.AddScoped<IOrderPosService, OrderPosService>();
        services.AddScoped<INumberSetRepository, NumberSetRepository>();
        services.AddScoped<IPriceListRepository, PriceListRepository>();
        services.AddScoped<IPriceListHeaderRepository, PriceListHeaderRepository>();
        
        //FOR TESTS
        services.AddScoped(provider => new Lazy<IOrderService>(provider.GetRequiredService<IOrderService>));
        services.AddScoped(provider => new Lazy<IOrderPosService>(provider.GetRequiredService<IOrderPosService>));
        services.AddScoped(provider => new Lazy<IArticleService>(provider.GetRequiredService<IArticleService>));
        
        services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = IdentityConstants.ApplicationScheme;
                options.DefaultChallengeScheme = IdentityConstants.ApplicationScheme;
            })
            .AddIdentityCookies();
        
        services.AddIdentityCore<ApplicationUser>(options => options.SignIn.RequireConfirmedAccount = true)
            .AddEntityFrameworkStores<StammDbContext>()
            .AddSignInManager()
            .AddDefaultTokenProviders();
        services.AddCascadingAuthenticationState();
        
        
        services.AddScoped<IUnitOfWork>(e => e.GetRequiredService<RepositoryContext>());
        services.AddMediatR(mediatRServiceConfiguration =>
        {
            mediatRServiceConfiguration.RegisterServicesFromAssemblies(Assemblies.Application);
        });

        services.AddServicesForAdgintyRestApiClient();
    }
    
    public static void AddWingCoreForApi(this WebApplication app)
    {
        app.UseMiddleware<ApiAuthMiddleware>();
    }
}