using WingCore.domain.Models;
using WingCore.domain.Models.StammDbModels;

namespace WingCore.domain.Common.CustomerAndSuppliers;

public abstract record BaseCustomerOrSupplierData(
    long? Number,
    string Sbg,
    string Anrede,
    string? Name,
    string? Name2,
    string? Name3,
    string Street,
    string Plz,
    string Ort,
    string Country,
    string UstIdNr,
    bool IsForeigner,
    string Phone,
    string Email,
    string Kdleh,
    long DueDays,
    string LfNr)
{
    public string? Name4;
    public string? CountryId;
    public string? Bundesland { get; set; }
    public short? BundeslandNumber { get; set; }
    public string Fax { get; set; } = string.Empty;
    public string MobilePhone { get; set; } = string.Empty;
    public string Postfach { get; set; } = string.Empty;
    public long? VertrNr { get; set; }
    public string? EInvoiceEmail { get; set; }
    public string? PaymentTerm { get; set; }
    public string FullName => string.Join(' ', Name, Name2, Name3, Name4);
    public string FullAddress => string.Join(' ', Street, Plz, Ort, Country);
 
    public  BaseCustomerOrSupplierData()
        : this(0,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,string.Empty,false,string.Empty,string.Empty,string.Empty,0, string.Empty)
    {}
    
    public BaseCustomerOrSupplierData(Mandant mandant)
    : this(
        Convert.ToInt64(mandant.Mnr),
        "",
        "",
        mandant.Name,
        "",
        "",
        mandant.Straße ?? string.Empty,
        mandant.Plz ?? string.Empty,
        mandant.Ort ?? string.Empty,
        mandant.Lkz ?? "Deutschland",
        mandant.Ilnnr ?? string.Empty,
        false,
        "",
        "",
        mandant.Ilnnr ?? string.Empty,
        0,
        string.Empty
    )
    {
    }

    public BaseCustomerOrSupplierData(Kunden kunden)
    : this(
        Convert.ToInt64(kunden.Kdnummer),
        kunden.Kdsbg,
        kunden.Kdanrede ?? string.Empty,
        kunden.Kdname1 ?? string.Empty,
        kunden.Kdname2 ?? string.Empty,
        kunden.Kdname3 ?? string.Empty,
        kunden.Kdstrasse ?? string.Empty,
        kunden.Kdplz ?? string.Empty,
        kunden.Kdort ?? string.Empty,
        kunden.Kdland ??"Deutschland",
        kunden.KdustIdnr ?? string.Empty,
        kunden.Kdland?.Equals("Deutschland") ?? false,
        kunden.Kdtelefon1 ?? string.Empty,
        kunden.Kdemail ?? string.Empty,
        kunden.Kdleh ?? string.Empty,
        kunden.KdtageNetto ?? 0,
        kunden.KdLfNr ?? string.Empty
    )
    {
        Bundesland = kunden.Kdbundesland;
        EInvoiceEmail = kunden.KdEinvoiceMail;
        PaymentTerm = kunden.Kdzahlungsziel;
    }
    
    public BaseCustomerOrSupplierData(Lieferanten lieferanten)
    : this(
        Convert.ToInt64(lieferanten.Lfnummer),
        lieferanten.Lfsbg,
        lieferanten.Lfanrede ?? string.Empty,
        lieferanten.Lfname1 ?? string.Empty,
        lieferanten.Lfname2 ?? string.Empty,
        lieferanten.Lfname3 ?? string.Empty,
        lieferanten.Lfstrasse ?? string.Empty,
        lieferanten.Lfplz ?? string.Empty,
        lieferanten.Lfort ?? string.Empty,
        lieferanten.Lfland ??"Deutschland",
        lieferanten.LfustIdNr ?? string.Empty,
        lieferanten.Lfland?.Equals("Deutschland") ?? false,
        lieferanten.Lftelefon1 ?? string.Empty,
        lieferanten.Lfemail ?? string.Empty,
        lieferanten.Leh ?? string.Empty,
        lieferanten.LfnettoTage ?? 0,
        string.Empty
    )
    {
        Bundesland = lieferanten.Lfbundesland;
    }

    public BaseCustomerOrSupplierData(CompanyMasterData companyMasterData)
        : this(
            Convert.ToInt64(companyMasterData.Mnr),
            "",
            "",
            companyMasterData.FName1 ?? string.Empty,
            companyMasterData.FName2 ?? string.Empty,
            companyMasterData.FName3 ?? string.Empty,
            companyMasterData.FStrasse ?? string.Empty,
            companyMasterData.Fplz ?? string.Empty,
            companyMasterData.FOrt ?? string.Empty,
            "",
            companyMasterData.FIdentNr ?? string.Empty,
            true,
            companyMasterData.FTelefon ?? string.Empty,
            companyMasterData.FEmail ?? string.Empty,
            companyMasterData.IlnNummer ?? string.Empty,
            0,
            string.Empty
            )
    {
        Name4 = companyMasterData.FName4;
    }
}
