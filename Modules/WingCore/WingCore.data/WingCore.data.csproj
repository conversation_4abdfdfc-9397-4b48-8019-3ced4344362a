<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="EFCore.NamingConventions" Version="9.0.0" />
        <PackageReference Include="Microsoft.EntityFrameworkCore" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.EntityFrameworkCore.SqlServer" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
            <PrivateAssets>all</PrivateAssets>
            <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="System.Collections.Immutable" Version="9.0.8" />
        <PackageReference Include="System.Reflection.Metadata" Version="9.0.8" />
    </ItemGroup>

    <ItemGroup>
        <ProjectReference Include="..\..\..\libs\Commons\Commons.csproj" />
        <ProjectReference Include="..\..\..\libs\Languages\Languages.csproj" />
        <ProjectReference Include="..\WingCore.application\WingCore.application.csproj" />
    </ItemGroup>

</Project>
