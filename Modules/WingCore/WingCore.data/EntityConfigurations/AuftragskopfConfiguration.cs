using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace WingCore.data.EntityConfigurations;

public class AuftragskopfConfiguration : IEntityTypeConfiguration<Auftragskopf>
{
    public void Configure(EntityTypeBuilder<Auftragskopf> builder)
    {
        builder.HasKey(e => e.Id).HasName("Auftragskopf$ID");

        builder.ToTable("Auftragskopf", tb => tb.UseSqlOutputClause(false));

        builder.Property(e => e.Id).HasColumnName("ID");
        builder.Property(e => e.Auftragsnummer).HasColumnName("Auftragsnummer");
        builder.Property(e => e.KundenNrWE).HasColumnName("KundenNrWE").IsRequired(false);
        builder.Property(e => e.AStatus).HasColumnName("AStatus").HasMaxLength(10);
        builder.Property(e => e.Abholung).HasDefaultValue(false);
        builder.Property(e => e.AbrKennzeichen).HasMaxLength(1);
        builder.Property(e => e.Arvnummer).HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.Angebot).HasDefaultValue(false).IsRequired(false);
        builder.Property(e => e.Auftrag).HasDefaultValue(false).IsRequired(false);
        builder.Property(e => e.Auftragsdatum).HasColumnType("datetime");
        builder.Property(e => e.AuslansSchl).HasMaxLength(2).HasDefaultValue("0").IsRequired(false);
        builder.Property(e => e.AvisSend).HasDefaultValue(false).IsRequired(false);
        builder.Property(e => e.SpeditionsNr).HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.Bearbeiter).HasMaxLength(15).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Bestellnr).HasMaxLength(30);
        builder.Property(e => e.Bstrecke).HasColumnName("BStrecke");
        builder.Property(e => e.Disponiert).HasMaxLength(1).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Dkennung).HasColumnName("DKennung").HasDefaultValue((short)0).IsRequired(false);
        builder.Property(e => e.SiloNummer).HasColumnName("SiloNummer").HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.FilialNr).HasColumnName("FilialNr").HasDefaultValue(1).IsRequired(false);
        builder.Property(e => e.Eldatum).HasColumnType("datetime").HasColumnName("ELDatum");
        builder.Property(e => e.ElscheinNr).HasDefaultValue(0L).HasColumnName("ELScheinNr").IsRequired(false);
        builder.Property(e => e.ErstArtikel).HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.FahrNam).HasMaxLength(20).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.FahrerName).HasMaxLength(20).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.BruttoKennung).HasColumnName("BruttoKennung").HasDefaultValue((short)0).IsRequired(false);
        builder.Property(e => e.Preisliste).HasColumnName("Preisliste").HasDefaultValue((short)0).IsRequired(false);
        builder.Property(e => e.Fpreisliste).HasColumnName("FPreisliste").HasDefaultValue((short)0).IsRequired(false);
        builder.Property(e => e.FrachtPreis).HasColumnType("money").HasDefaultValue(0.0m).IsRequired(false);
        builder.Property(e => e.FrachtScheinNr).HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.NuetzelSend).HasDefaultValue((short)0).IsRequired(false);
        builder.Property(e => e.GegBlgNr).HasMaxLength(15);
        builder.Property(e => e.GesGew).HasColumnName("GesGew");
        builder.Property(e => e.KontraktNr).HasColumnName("KontraktNr").HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.StreckenNr).HasColumnName("StreckenNr").HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.LadeDatum).HasColumnType("datetime");
        builder.Property(e => e.LadeUhrzeit).HasMaxLength(10).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.LbisDatum).HasColumnType("datetime").HasColumnName("LBisDatum");
        builder.Property(e => e.Lfrestr1).HasMaxLength(70).HasColumnName("LFRestr1").HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Lfrestr2).HasMaxLength(70).HasColumnName("LFRestr2").HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Lfrestr3).HasMaxLength(70).HasColumnName("LFRestr3").HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Lfrestr4).HasMaxLength(70).HasColumnName("LFRestr4").HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Lieferrest).HasMaxLength(70).HasDefaultValue(string.Empty);
        builder.Property(e => e.Lieferschein).HasDefaultValue(false);
        builder.Property(e => e.LockLast).HasColumnType("datetime");
        builder.Property(e => e.LockStart).HasColumnType("datetime");
        builder.Property(e => e.LockUser).HasMaxLength(50).HasDefaultValue(string.Empty);
        builder.Property(e => e.LoeschKng).HasDefaultValue(false);
        builder.Property(e => e.LsabrKng).HasDefaultValue(false).HasColumnName("LSAbrKng");
        builder.Property(e => e.Lsanliefer).HasMaxLength(20).HasColumnName("LSAnliefer").HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Skontosatz).HasColumnName("Skontosatz").HasDefaultValue(0.0).IsRequired(false);
        builder.Property(e => e.LsdepotKng).HasColumnName("LSDepotKng").HasDefaultValue((short)0).IsRequired(false);
        builder.Property(e => e.LsdepotNr).HasColumnName("LSDepotNr").HasDefaultValue(0).IsRequired(false);
        builder.Property(e => e.TourNr).HasColumnName("TourNr").HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.LsfremdNr).HasMaxLength(255).HasColumnName("LSFremdNr").HasDefaultValue(string.Empty);
        builder.Property(e => e.LslfnrWe).HasMaxLength(20).HasColumnName("LSLFNrWE").HasDefaultValue(string.Empty);
        builder.Property(e => e.LvonDatum).HasColumnType("datetime").HasColumnName("LVonDatum");
        builder.Property(e => e.MalzKng).HasDefaultValue(false);
        builder.Property(e => e.Mischpalette).HasDefaultValue(false);
        builder.Property(e => e.MitHänger).HasDefaultValue(false).HasColumnName("mitHänger");
        builder.Property(e => e.NrtelefonRe).HasMaxLength(20).HasColumnName("NRTelefonRE").HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.NrtelefonWe).HasMaxLength(20).HasColumnName("NRTelefonWE").HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.OhneDisposition).HasDefaultValue(false);
        builder.Property(e => e.Packungsart).HasMaxLength(10).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.PalBez1).HasMaxLength(255);
        builder.Property(e => e.PalBez2).HasMaxLength(255);
        builder.Property(e => e.PalBez3).HasMaxLength(255);
        builder.Property(e => e.PalKlPck).HasDefaultValue(false);
        builder.Property(e => e.Palettenware).HasDefaultValue(false);
        builder.Property(e => e.PfadBitMap).HasMaxLength(100).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Planenzug).HasDefaultValue(false).IsRequired(false);
        builder.Property(e => e.RegioNummer).HasDefaultValue(0L).IsRequired(false);
        builder.Property(e => e.RegioName1).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.RegioOrt).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.RegioStatus).HasMaxLength(1).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Retoure).HasDefaultValue(false);
        builder.Property(e => e.Satlog).HasDefaultValue(false).HasColumnName("SATLog");
        builder.Property(e => e.Send).HasDefaultValue(false);
        builder.Property(e => e.SpedSend).HasDefaultValue(false);
        builder.Property(e => e.SysTime).HasColumnType("datetime");
        builder.Property(e => e.SysUser).HasMaxLength(50);
        builder.Property(e => e.Tankzug).HasDefaultValue(false);
        builder.Property(e => e.Transport).HasMaxLength(50).HasDefaultValue(string.Empty);
        builder.Property(e => e.Uhrzeit).HasMaxLength(50);
        builder.Property(e => e.UhrzeitTxt).HasMaxLength(50).HasDefaultValue(string.Empty);
        builder.Property(e => e.Bemerkung).HasMaxLength(50).HasDefaultValue(string.Empty);
        builder.Property(e => e.VvonrWe).HasMaxLength(20).HasColumnName("VVONrWE").HasDefaultValue(string.Empty);
        builder.Property(e => e.Wgskng).HasDefaultValue(false).HasColumnName("WGSKng");
        builder.Property(e => e.Xlsexp).HasDefaultValue(false).HasColumnName("XLSExp");
       
        builder.Property(e => e.ZahlZiel).HasMaxLength(60).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtBez1).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtBez2).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtBez3).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtBez4).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtBez5).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtBez6).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtInh1).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtInh2).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtInh3).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtInh4).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtInh5).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.ZusTxtInh6).HasMaxLength(30).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Zustellart).HasMaxLength(25).HasDefaultValue(string.Empty).IsRequired(false);
        builder.Property(e => e.Zustellart2).HasMaxLength(25).HasDefaultValue(string.Empty).IsRequired(false);
        
        builder.OwnsOne(e => e.InvoiceHolder, ih =>
        {
            ih.Property(p => p.Anrede).HasMaxLength(30).HasColumnName("AnredeRE").IsRequired(false);
            ih.Property(p => p.BundeslandNumber).HasColumnName("BLDRE").IsRequired(false);
            ih.Property(p => p.Fax).HasMaxLength(20).HasColumnName("FaxRE").IsRequired(false);
            ih.Property(p => p.Kdleh).HasMaxLength(30).HasColumnName("ILNNRRE").IsRequired(false);
            ih.Property(p => p.Number).HasColumnName("KundenNrRE").IsRequired(false);
            ih.Property(p => p.Country).HasMaxLength(30).HasColumnName("LandRE").IsRequired(false);
            ih.Property(p => p.MobilePhone).HasMaxLength(20).HasColumnName("MobilRE").IsRequired(false);
            ih.Property(p => p.Name).HasMaxLength(30).HasColumnName("Name1RE").IsRequired(false);
            ih.Property(p => p.Name2).HasMaxLength(30).HasColumnName("Name2RE").IsRequired(false);
            ih.Property(p => p.Name3).HasMaxLength(30).HasColumnName("Name3RE").IsRequired(false);
            ih.Property(p => p.Ort).HasMaxLength(30).HasColumnName("OrtRE").IsRequired(false);
            ih.Property(p => p.Postfach).HasMaxLength(10).HasColumnName("PFachRE").IsRequired(false);
            ih.Property(p => p.Plz).HasMaxLength(10).HasColumnName("PLZRE").IsRequired(false);
            ih.Property(p => p.Sbg).HasMaxLength(10).HasColumnName("SBGRE").IsRequired(false);
            ih.Property(p => p.Street).HasMaxLength(30).HasColumnName("StrasseRE").IsRequired(false);
            ih.Property(p => p.Phone).HasMaxLength(20).HasColumnName("TelefonRE").IsRequired(false);
            ih.Property(p => p.VertrNr).HasColumnName("VertrNrRE").IsRequired(false);

            ih.Ignore(p => p.DueDays);
            ih.Ignore(p => p.Email);
            ih.Ignore(p => p.IsForeigner);
            ih.Ignore(p => p.UstIdNr);
            ih.Ignore(p => p.Bundesland);
            ih.Ignore(p => p.EInvoiceEmail);
            ih.Ignore(p => p.PaymentTerm);
            ih.Ignore(p => p.LfNr);

        });

        builder.OwnsOne(e => e.WarenEmpfaenger, we =>
        {
            we.Property(p => p.Anrede).HasMaxLength(30).HasColumnName("AnredeWE").IsRequired(false);
            we.Property(p => p.BundeslandNumber).HasColumnName("BLDWE").IsRequired(false);
            we.Property(p => p.Fax).HasMaxLength(20).HasColumnName("FaxWE").IsRequired(false);
            we.Property(p => p.Kdleh).HasMaxLength(30).HasColumnName("ILNNRWE").IsRequired(false);
            we.Property(p => p.Number).HasColumnName("KundenNrWE").IsRequired(false);
            we.Property(p => p.Country).HasMaxLength(30).HasColumnName("LandWE").IsRequired(false);
            we.Property(p => p.MobilePhone).HasMaxLength(20).HasColumnName("MobilWE").IsRequired(false);
            we.Property(p => p.Name).HasMaxLength(30).HasColumnName("Name1WE").IsRequired(false);
            we.Property(p => p.Name2).HasMaxLength(30).HasColumnName("Name2WE").IsRequired(false);
            we.Property(p => p.Name3).HasMaxLength(30).HasColumnName("Name3WE").IsRequired(false);
            we.Property(p => p.Ort).HasMaxLength(30).HasColumnName("OrtWE").IsRequired(false);
            we.Property(p => p.Postfach).HasMaxLength(10).HasColumnName("PFachWE").IsRequired(false);
            we.Property(p => p.Plz).HasMaxLength(10).HasColumnName("PLZWE").IsRequired(false);
            we.Property(p => p.Sbg).HasMaxLength(10).HasColumnName("SBGWE").IsRequired(false);
            we.Property(p => p.Street).HasMaxLength(30).HasColumnName("StrasseWE").IsRequired(false);
            we.Property(p => p.Phone).HasMaxLength(20).HasColumnName("TelefonWE").IsRequired(false);
            we.Property(p => p.VertrNr).HasColumnName("VertrNrWE").IsRequired(false);
            
            we.Ignore(p => p.DueDays);
            we.Ignore(p => p.Email);
            we.Ignore(p => p.IsForeigner);
            we.Ignore(p => p.UstIdNr);
            we.Ignore(p => p.Bundesland);
            we.Ignore(p => p.EInvoiceEmail);
            we.Ignore(p => p.PaymentTerm);
            we.Ignore(p => p.LfNr);
        });
        
        builder.HasMany(e => e.Auftragspositions)
            .WithOne()
            .HasForeignKey(p => p.Auftragsnummer)
            .HasPrincipalKey(p => p.Auftragsnummer);
    }
}