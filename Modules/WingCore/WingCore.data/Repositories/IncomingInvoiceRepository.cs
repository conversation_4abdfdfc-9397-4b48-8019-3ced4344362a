using Languages.Resources;
using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.Helper;
using WingCore.application.Contract.Services;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.data.Repositories.Helper;
using WingCore.data.WaWiContext;

namespace WingCore.data.Repositories;

public class IncomingInvoiceRepository(RepositoryContext repositoryContext,
                                              ICountryService countryService,
                                              IStringLocalizer<Resource> localizer)
    : RepositoryBase<Erv>(repositoryContext), IIncomingInvoiceRepository
{
    public Task<Erv?> GetInvoiceHeaderDataAsync(bool trackChanges, long invoiceNumber) =>
        FindAll(trackChanges)
            .FirstOrDefaultAsync(i => i.Ernummer == invoiceNumber);

    public Task<Erv?> GetInvoiceCompleteData(bool trackChanges, long invoiceNumber) =>
        FindAll(trackChanges)
            .Include(i => i.Positionen)
            .Include(i => i.InvoiceSupplierCurrentData)
            .FirstOrDefaultAsync(i => i.Ernummer == invoiceNumber);

    public IEnumerable<Erv> GetAllInvoiceData(bool trackChanges, InvoiceSearchParam searchParam)
    {
        if (!searchParam.WithAllData)
        {
            return FindAll(trackChanges).SetTheInvoiceSearchParam(searchParam).OrderBy(c => c.Erdatum);
        }
        else
        {
            return FindAll(trackChanges)
                                      .SetTheInvoiceSearchParam(searchParam)
                                      .Include(i => i.Positionen)
                                      .Include(i => i.InvoiceSupplierCurrentData)
                                      .OrderBy(c => c.Erdatum);
        }
    }

    public async Task<IncomingInvoiceOverviewDto?> GetInvoiceDtoCompleteData(bool trackChanges, long invoiceNumber)
    {
        var allInvoice = await GetAllInvoiceDtoCompleteData(new InvoiceSearchParam(){ FromInvoiceNumber = invoiceNumber, ToInvoiceNumber = invoiceNumber} );
        return allInvoice.FirstOrDefault();
    }

    
    public async Task<IEnumerable<IncomingInvoiceOverviewDto>> GetAllInvoiceDtoCompleteData(InvoiceSearchParam searchParam)
    {
        var allInvoice = GetAllInvoiceData(false, searchParam);

        var allInvoiceList = allInvoice.ToList();
        if (allInvoiceList.Count == 0)
            return [];
        
        List<IncomingInvoiceOverviewDto> resultSearchDto = [];
        
        foreach (var invoiceDto in allInvoiceList.Select(invoice => (IncomingInvoiceOverviewDto?)invoice))
        {
            if (invoiceDto is null)
                return [];

            invoiceDto.Type = invoiceDto.Type with { Description = localizer[invoiceDto.Type.ToString()] };
            if (!string.IsNullOrWhiteSpace(invoiceDto.InvoiceSupplier?.Country))
                invoiceDto.InvoiceSupplier.CountryId = await countryService.GetCountryIdByName(invoiceDto.InvoiceSupplier.Country);
            if (!string.IsNullOrWhiteSpace(invoiceDto.InvoiceSupplierCurrent?.Country))
                invoiceDto.InvoiceSupplierCurrent.CountryId = await countryService.GetCountryIdByName(invoiceDto.InvoiceSupplierCurrent.Country);
            if (!string.IsNullOrWhiteSpace(invoiceDto.InvoiceHolder?.Country))
                invoiceDto.InvoiceHolder.CountryId = await countryService.GetCountryIdByName(invoiceDto.InvoiceHolder.Country);
            if (!string.IsNullOrWhiteSpace(invoiceDto.InvoiceHolderCurrent?.Country))
                invoiceDto.InvoiceHolderCurrent.CountryId = await countryService.GetCountryIdByName(invoiceDto.InvoiceHolderCurrent.Country);

            resultSearchDto.Add(invoiceDto);
        }
        
        return resultSearchDto;
    }
    
    public void SetAllInvoiceAddisonTseNitExportSuccess(List<long> invoiceIds)
    {
        if (invoiceIds.Count == 0)
            return;

        const int batchSize = 200;
        for (var i = 0; i < invoiceIds.Count; i += batchSize)
        {
            var batch = invoiceIds.Skip(i).Take(batchSize).ToList();
            foreach (var invoiceNumber in batch)
            {
                var allInvoice = FindAll(true)
                    .FirstOrDefault(i => i.Id == invoiceNumber);
                if(allInvoice is null)
                    continue;
                allInvoice.Erfibu2 = true;
                allInvoice.FlagsWorker.SetAddisonTseNitExportSuccess();
            }

            RepositoryContext.SaveChanges();
            RepositoryContext.ChangeTracker.Clear();
        }
    }
    
    public void SetAllInvoiceDatevExportSuccess(List<long> invoiceIds)
    {
        if (invoiceIds.Count == 0) return;

        const int batchSize = 200;
        for (var i = 0; i < invoiceIds.Count; i += batchSize)
        {
            var batch = invoiceIds.Skip(i).Take(batchSize).ToList();
            foreach (var invoiceNumber in batch)
            {
                var allInvoice = FindAll(true).FirstOrDefault(i => i.Id == invoiceNumber);
                if (allInvoice is null) continue;
                allInvoice.Erfibu2 = true;
                allInvoice.FlagsWorker.SetDatevExportSuccess();
            }

            RepositoryContext.SaveChanges();
            RepositoryContext.ChangeTracker.Clear();
        }
    }

    public void SetAllInvoiceEdifactExportSuccess(List<long> invoiceIds)
    {
        if (invoiceIds.Count == 0) return;

        const int batchSize = 200;
        for (var i = 0; i < invoiceIds.Count; i += batchSize)
        {
            var batch = invoiceIds.Skip(i).Take(batchSize).ToList();
            foreach (var invoiceNumber in batch)
            {
                var allInvoice = FindAll(true).FirstOrDefault(i => i.Id == invoiceNumber);
                if (allInvoice is null) continue;
                allInvoice.Erfibu2 = true;
                allInvoice.FlagsWorker.SetEdifactExportSuccess();
            }

            RepositoryContext.SaveChanges();
            RepositoryContext.ChangeTracker.Clear();
        }
    }
}