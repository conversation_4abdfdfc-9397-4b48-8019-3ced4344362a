using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.Helper;
using WingCore.data.Repositories.Helper;
using WingCore.data.Repositories.Mapper;
using WingCore.data.WaWiContext;

namespace WingCore.data.Repositories;

public class ArtikelRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Artikel>(repositoryContext), IArtikelRepository
{
    public ValueTask<Artikel?> GetArtikelById(long artikelnummer, bool trackBack) => ValueTask.FromResult(FindByCondition(c => c.Artikelnr.Equals(artikelnummer), trackBack)
                                                                                                            .FirstOrDefault());

    public IEnumerable<Artikel> GetAllArtikel(bool trackBack) => FindAll(trackBack)
                                                                    .Where(a => !string.IsNullOrEmpty(a.ArtBezText1) && 
                                                                                !string.IsNullOrEmpty(a.ArtSbg) && 
                                                                                !string.IsNullOrEmpty(a.ArtBezugsgr) && 
                                                                                a.ArtVkpreis.HasValue)
                                                                    .ToList();

    public IEnumerable<Artikel> GetAllArtikelsByMainArtikelNumber(long mainArtikelNumBer, bool trackBack) => FindByCondition(a => a.Hartikel.Equals(mainArtikelNumBer), trackBack).ToList();

    public IEnumerable<Artikel> GetMainArtikels(bool trackBack)
    {
        var query = $"""
                         SELECT * 
                         FROM Artikel
                         WHERE Artikelnr IN (SELECT Hartikel FROM Artikel WHERE Hartikel IS NOT NULL And Hartikel > 0)
                     """;

        return trackBack 
                ? RepositoryContext.Artikels.FromSqlRaw(query).ToList()
                : RepositoryContext.Artikels.FromSqlRaw(query).AsNoTracking().ToList();
    }

    public ValueTask<IEnumerable<Artikel>> GetOverSearchParam(bool trackChanges, ArticleSearchParam searchParam)
    {
        var allArticle= RepositoryContext.Artikels
            .SetSearchParam(searchParam)
            .OrderBy(a => a.Artikelnr);

        return ValueTask.FromResult<IEnumerable<Artikel>>(
            trackChanges ? allArticle : allArticle.AsNoTracking());
    }

    public async Task<Dictionary<long, int?>> GetArticleRefNumber(List<long> articleNumbers)
    {
        var allArticle = await RepositoryContext.Artikels
                .SetSearchParam(new ArticleSearchParam(){ArticleNumbers = articleNumbers})
                .AsNoTracking()
                .MapToEdifactArticle()
                .ToListAsync();
            
        return allArticle
            .DistinctBy(a => a.Artikelnr)
            .ToDictionary(a => a.Artikelnr, a => a.ArtRefNr);
    }

    public Task<Artikel?> GetByEan(string ean)
    {
        return Task.FromResult(RepositoryContext.Set<Artikel>().FirstOrDefault(a => (a.ArtEan != null) && a.ArtEan.Equals(ean)));
    }

    public Task<long> GetMaxArticleNumber()
    {
        return Task.FromResult(RepositoryContext.Set<Artikel>().Max(a => a.Artikelnr));
    }
    
    public async Task CreateArtikel(Artikel artikel)
    {
        Create(artikel);
        await SaveChangesAsync();
        
        repositoryContext.Entry(artikel).State = EntityState.Detached;
    }

    public async Task UpdateArtikel(Artikel artikel)
    {
        Update(artikel);
        await SaveChangesAsync();
        
        repositoryContext.Entry(artikel).State = EntityState.Detached;
    }

    public async Task DeleteArtikel(Artikel artikel)
    {
        Delete(artikel);
        await SaveChangesAsync();
    }
}