using Microsoft.EntityFrameworkCore;
using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using WingCore.domain.Common;

namespace WingCore.data.Repositories;

public class RapeSettlementInvoicePostingsRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Rgbvhpt>(repositoryContext), IRapeSettlementInvoicePostingsRepository
{
    public IEnumerable<Rgbvhpt> GetAllInvoicePostingsFromInvoice(bool trackChanges,
                                                                long invoiceNumber,
                                                                PostingsSchema? schema)
    {
        if(schema is null)
        {
            return FindAll(trackChanges)
                 .Where(p => p.Gbnr == invoiceNumber);
        }
        
        if(schema.IsPo)
        {
            return FindAll(trackChanges)
                             .Where(p => p.Gbnr == invoiceNumber && (p.GbartNr != null && p.GbartNr != 0) ); 
        }
        else
        {
            return FindAll(trackChanges)
                             .Where(p => p.Gbnr == invoiceNumber && (p.GbartNr != null && p.GbartNr == 0) );
        }
    }
    
    public async Task<bool> ArticleNumberExistsInPosition(long articleNumber)
    {
        return await RepositoryContext.Rgbvhpts
            .AsNoTracking()
            .AnyAsync(r => 
                r.GbartNr == articleNumber
                || r.Gbhartikel == articleNumber
                ); 
    }
}