using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.IModels.Helper;
using WingCore.data.Repositories.Helper;
using WingCore.data.WaWiContext;

namespace WingCore.data.Repositories;

public class KundenRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Kunden>(repositoryContext), IKundenRepository
{
    public IEnumerable<Kunden> GetAllCostumers(bool trackChanges) =>
        FindAll(trackChanges)
            .OrderBy(c => c.Kdname1)
            .ToList();

    public async ValueTask<Kunden?> GetCostumer(bool trackChanges,long id)=>
          await FindAll(trackChanges).FirstOrDefaultAsync(c => c.Id == id);

    public async ValueTask<Kunden?> GetCustomerOverNumber(bool trackChanges, long number) =>
        await FindAll(trackChanges).FirstOrDefaultAsync(c => c.Kdnummer == number);

    public ValueTask<IEnumerable<Kunden>> GetCustomerOverSearchParam(bool trackChanges, CustomerSearchParam customerSearchParam) => 
        ValueTask.FromResult<IEnumerable<Kunden>>(RepositoryContext.Kundens.SetTheCustomerSearchParam(customerSearchParam).OrderBy(c => c.Kdnummer));
   
    public async ValueTask UpdateCustomer(bool trackChanges, Kunden kunde)
    {
        Update(kunde);
        await SaveChangesAsync();
       
        if (!trackChanges)
            repositoryContext.Entry(kunde).State = EntityState.Detached;
    }
    
    public async Task WriteOrderToDb(bool trackChanges, Kunden kunde)
    {
        if (trackChanges)
        {
            Update(kunde);
        }
        else
        {
            Create(kunde);
        }

        await SaveChangesAsync();
    }

}