using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using Microsoft.EntityFrameworkCore;

namespace WingCore.data.Repositories;

public class LieferantenRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Lieferanten>(repositoryContext), ILieferantenRepository
{
    public async Task<IEnumerable<Lieferanten>> GetAllSuppliers(bool trackChanges)
    {
        return await FindAll(trackChanges).ToListAsync();
    }

    public async Task<IEnumerable<Lieferanten>> GetSupplier(bool trackChanges, long id)
    {
        return await FindByCondition(s => s.Id == id, trackChanges).ToListAsync();
    }

    public async Task<IEnumerable<Lieferanten>> GetSupplierOverNumber(bool trackChanges, long number)
    {
        return await FindByCondition(s => s.Lfnummer.Equals(number) , trackChanges).ToListAsync();
    }

    public async Task<IEnumerable<Lieferanten>> GetSupplierOverSearchParam(bool trackChanges, string supplierSearchParam)
    {
        return await FindByCondition(s => s.Lfsbg.Contains(supplierSearchParam), trackChanges).ToListAsync();
    }

    public async Task<ICollection<Lieferanten>> GetSupplierOverLastChange(DateTime lastChange)
    {
        return await FindByCondition(s => s.SysTime > lastChange, false).ToListAsync();
    }
}