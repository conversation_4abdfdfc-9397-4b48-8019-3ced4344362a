using Microsoft.EntityFrameworkCore;
using WingCore.domain.Models;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using WingCore.domain.Common;

namespace WingCore.data.Repositories;

public class ArhauptdateiRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Arhauptdatei>(repositoryContext), IOutgoingInvoicePostingsRepository
{
    public IEnumerable<Arhauptdatei> GetAllInvoicePostingsFromInvoice(bool trackChanges,
                                                                      long invoiceNumber,
                                                                      PostingsSchema? schema) =>
                FindAll(trackChanges)
                        .Where(p => p.Renummer == invoiceNumber && 
                                    (schema == null || p.ErfSchema == schema));

    public async Task<bool> ArticleNumberExistsInPosition(long articleNumber)
    {
        return await RepositoryContext.Arhauptdateis
            .AsNoTracking()
            .AnyAsync(a => a.ArtNr == articleNumber); 
    }
}