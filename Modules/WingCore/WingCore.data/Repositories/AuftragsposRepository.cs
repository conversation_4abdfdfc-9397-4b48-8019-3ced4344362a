using WingCore.domain.Models;
using Microsoft.EntityFrameworkCore;
using WingCore.application.Contract.IModels;
using WingCore.data.WaWiContext;
using WingCore.domain.Common.Flags;

namespace WingCore.data.Repositories;

public class AuftragsposRepository(RepositoryContext repositoryContext)
    : RepositoryBase<Auftragspo>(repositoryContext), IAuftragsposRepository
{
    public async Task<ICollection<Auftragspo>> GetAll(bool trackChanges)
    {
        var allAuftragsposList = await FindAll(trackChanges).ToListAsync();
        if (allAuftragsposList is null)
            return [];
        return allAuftragsposList;
    }

    public async Task<Auftragspo?> GetAuftragsposById(bool trackChanges, long orderId)
    {
        return await FindByCondition(c => c.Id.Equals(orderId), trackChanges)
            .FirstOrDefaultAsync();
    }

    public async Task<ICollection<Auftragspo>> GetAuftragsposByOrderNumber(bool trackChanges, long orderId)
    {
        return await FindByCondition(c => c.Auftragsnummer.Equals(orderId) && c.ArtikelNummer > 0, trackChanges).ToListAsync();
    }

    public IEnumerable<Auftragspo> GetAuftragsposById(bool trackChanges, List<long> posIds)
    {
        var posIdsString = string.Join(",", posIds);
    
        var query = $@"
        SELECT ap.* 
        FROM Auftragspos ap
        INNER JOIN Auftragskopf ak ON ap.Id = ak.Id
        WHERE ak.Auftrag = 1
        AND ap.Id IN ({posIdsString})
    ";

        return RepositoryContext.Auftragspos.FromSqlRaw(query).ToList();
    }
    
    public IEnumerable<Auftragspo> GetAuftragsposByArtikels(bool trackChanges, List<long> artikelNumbers)
    {
        var artikelNumbersString = string.Join(",", artikelNumbers);
    
        var query = $@"
                SELECT ap.* 
                FROM Auftragspos ap
                INNER JOIN Auftragskopf ak ON ap.Auftragsnummer = ak.Auftragsnummer
                WHERE ak.Auftrag = 1
                AND ap.ArtikelNummer IN ({artikelNumbersString})
            ";

        return RepositoryContext.Auftragspos.FromSqlRaw(query).ToList();
    }

    public async Task<bool> ArticleNumberExistsInAuftragsPos(long articleNumber)
    {
        return await RepositoryContext.Auftragspos.AnyAsync(a => a.ArtikelNummer == articleNumber);
    }

    public void WriteOrderItemToDb(bool trackChanges, Auftragspo item)
    {
        if (trackChanges)
        {
            RepositoryContext.Auftragspos.Update(item);
        }
        else
        {
            RepositoryContext.Auftragspos.Add(item);
        }

        RepositoryContext.SaveChanges();
    }

    public async Task<IEnumerable<Auftragspo>> GetOrderPosWithArticlesHaveSameMainArticleAsync(long mainArticleNumber, OrderPosFlags withOutflag, bool palettenware)
    {
        var query = $@"
                        SELECT ap.*
                        FROM Auftragspos ap
                        INNER JOIN Auftragskopf ak ON ap.Auftragsnummer = ak.Auftragsnummer
                        WHERE ap.ArtikelNummer IN (
                            SELECT Artikelnr
                            FROM Artikel
                            WHERE Hartikel = {mainArticleNumber}                      
                        )
                        AND ap.Flags & {(long)withOutflag} = 0
                        AND Palettenware = {(palettenware ? 1 : 0)};
                    ";

        return await RepositoryContext.Auftragspos.FromSqlRaw(query).ToListAsync();
    }

    public async Task SetFlagAsync(List<long> posIds, OrderPosFlags flag)
    {
        var posIdsString = string.Join(",", posIds);
        var flagValue = (long)flag;

        var query = $"UPDATE Auftragspos SET Flags = Flags | {flagValue} WHERE Id IN ({posIdsString})";

        await RepositoryContext.Database.ExecuteSqlRawAsync(query);
        await SaveChangesAsync();
    }

    public async Task RemoveFlagAsync(List<long> posIds, OrderPosFlags flag)
    {
        if(posIds.Count == 0)
            return;
        var posIdsString = string.Join(",", posIds);
        var flagValue = (long)flag;

        var query = $"UPDATE Auftragspos SET Flags = Flags & ~{flagValue} WHERE Id IN ({posIdsString})";

        await RepositoryContext.Database.ExecuteSqlRawAsync(query);
        await SaveChangesAsync();
    }
}