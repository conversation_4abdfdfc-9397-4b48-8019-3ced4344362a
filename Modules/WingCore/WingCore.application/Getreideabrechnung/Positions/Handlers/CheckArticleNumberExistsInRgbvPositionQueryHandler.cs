using MediatR;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Getreideabrechnung.Positions.Handlers;

public class CheckArticleNumberExistsInRgbvPositionQueryHandler(IRapeSettlementInvoicePostingsRepository rgbvRepository) : IRequestHandler<CheckArticleNumberExistsInRgbvPositionQuery, bool>
{
    public async Task<bool> Handle(CheckArticleNumberExistsInRgbvPositionQuery request, CancellationToken cancellationToken)
    {
        return await rgbvRepository.ArticleNumberExistsInPosition(request.ArticleNumber);
    }
}