using MediatR;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Getreideabrechnung.Positions.Handlers;

public class CheckArticleNumberExistsInGbvPositionQueryHandler(IGrainSettlementInvoicePostingsRepository gbvhptRepository) : IRequestHandler<CheckArticleNumberExistsInGbvPositionQuery, bool>
{
    public async Task<bool> Handle(CheckArticleNumberExistsInGbvPositionQuery request, CancellationToken cancellationToken)
    {
        return await gbvhptRepository.ArticleNumberExistsInPosition(request.ArticleNumber);
    }
}