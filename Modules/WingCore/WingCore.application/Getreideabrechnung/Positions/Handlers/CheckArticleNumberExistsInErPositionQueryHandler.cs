using MediatR;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Getreideabrechnung.Positions.Handlers;

public class CheckArticleNumberExistsInErPositionQueryHandler(IIncomingInvoicePostingsRepository erRepository) : IRequestHandler<CheckArticleNumberExistsInErPositionQuery, bool>
{
    public async Task<bool> Handle(CheckArticleNumberExistsInErPositionQuery request, CancellationToken cancellationToken)
    {
        return await erRepository.ArticleNumberExistsInPosition(request.ArticleNumber);
    }
}