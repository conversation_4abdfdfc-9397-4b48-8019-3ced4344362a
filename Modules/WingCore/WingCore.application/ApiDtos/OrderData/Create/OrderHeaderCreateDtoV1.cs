using System.Text.Json.Serialization;

namespace WingCore.application.ApiDtos.OrderData.Create;

public record OrderHeaderCreateDtoV1()
{
    public long CustomerNumber { get; set; } = 0;
    public DateTime DeliveryDate { get; set; } = DateTime.MinValue;
    public string Category { get; set; }
    public List<OrderLineCreateDtoV1> OrderLineList { get; set; } = [];
}

public static class OrderCategories
{
    public const string TankerTruck = "Tanker Truck";
    public const string TarpaulinTruck = "Tarpaulin Truck";
    public const string PalletizedGoods = "Palletized Goods";

    public static IList<string> All = [TankerTruck, TarpaulinTruck, PalletizedGoods];
}