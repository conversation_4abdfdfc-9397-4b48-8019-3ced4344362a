using System.Runtime.Serialization;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace WingCore.application.ApiDtos.OrderData;

public record OrderLineDtoV1()
{
    public long Id { get; set; }
    public long OrderNumber { get; set; }
    public int GridPos { get; set; }
    public long ArtikelNumber { get; set; }
    public string Name { get; set; } = string.Empty;
    public int QuantityRequested { get; set; }
    public decimal QuantityLoaded { get; set; }
    public long RouteNumber { get; set; }
    public string TruckCompartmentNumber { get; set; } = string.Empty;
    public string Status { get; set; }
}

public static class OrderLineDtoV1Status
{
    public const string LieferungString = "L";
    public const string AuftragString = "A";
    public const string BestellungString = "B";
    
    public static IList<string> All = [LieferungString, AuftragString, BestellungString];
}