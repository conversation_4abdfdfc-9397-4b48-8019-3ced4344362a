using System.Text.Json.Serialization;
using WingCore.domain.Models;

namespace WingCore.application.ApiDtos.MasterData.V1;

public record CustomerMasterDataDtoV1
{
    public long Number { get; init; }
    public string? Matchcode { get; init; }
    public string? Name { get; init; }
    public string? Name2 { get; init; }
    public string? Name3 { get; init; }
    public AdressV1? Address { get; init; }
    public long? InvoiceHolderNumber { get; init; }
    public string? Email { get; init; }
    public string? MobileNumber { get; init; }
    public string? PhoneNumber { get; init; }
    public string? PhoneNumber2 { get; init; }
    public string? DeliveryRestriction { get; init; }
    public string? DeliveryRestriction2 { get; init; }
    public string? DeliveryRestriction3 { get; init; }
    public long? InvoiceRecipient { get; init; }
    public string? VvvoNumber { get; init; }
    public DateTime? LastChange { get; init; }
    public long? RepresentativenNumber { get; init; }
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]

    public List<PriceListDtoV1> PriceList { get; set; } = [];
    [JsonIgnore(Condition = JsonIgnoreCondition.WhenWritingDefault)]
    public List<ContractDtoV1> Contracts { get; set; } = [];

    public static implicit operator CustomerMasterDataDtoV1(Kunden k)
    {
        return new CustomerMasterDataDtoV1
        {
            Number = k.Kdnummer,
            Matchcode = k.Kdsbg,
            Name = k.Kdname1,
            Name2 = k.Kdname2,
            Name3 = k.Kdname3,
            Address = new AdressV1
            {
                Street = k.Kdstrasse,
                ZipCode = k.Kdplz,
                City = k.Kdort,
                Country = k.Kdland
            },
            InvoiceRecipient = k.KdabrStelleNr,
            InvoiceHolderNumber = k.KdabrStelleNr ?? 0,
            Email = k.Kdemail,
            MobileNumber = k.Kdmobil,
            PhoneNumber = k.Kdtelefon1,
            PhoneNumber2 = k.Kdtelefon2,
            DeliveryRestriction = k.Kdlfr1,
            DeliveryRestriction2 = k.Kdlfr2,
            DeliveryRestriction3 = k.Kdlfr3,
            VvvoNumber = k.Vvonr,
            LastChange = k.KdletztAend,
            RepresentativenNumber = k.KdvertrNr,
            
            
        };
    }
}