using MediatR;
using WingCore.application.ApiDtos.MasterData.V1;
using WingCore.application.ApiDtos.OrderData;
using WingCore.application.ApiDtos.OrderData.Create;
using WingCore.domain.Common.CustomerAndSuppliers;
using WingCore.domain.Models;

namespace WingCore.application.ApiDtos;

public static class DtoExtensions
{
    public static SupplierDtoV1 ToSupplierDtoV1(this Lieferanten lieferant)
    {
        return new SupplierDtoV1
        {
            Number = lieferant.Lfnummer,
            Matchcode = lieferant.Lfsbg,
            Name = lieferant.Lfname1,
            Name2 = lieferant.Lfname2,
            Name3 = lieferant.Lfname3,
            Address = new AdressV1
            {
                Street = lieferant.Lfstrasse,
                ZipCode = lieferant.Lfplz,
                City = lieferant.Lfort,
                Country = lieferant.Lfland
            },
            Email = lieferant.Lfemail,
            MobileNumber = lieferant.Lfmobil,
            PhoneNumber = lieferant.Lftelefon1,
            PhoneNumber2 = lieferant.Lftelefon2,
            LastChange = lieferant.SysTime,
            RepresentativenNumber = lieferant.LfvertrNr,
        };
    }
    public static OrderHeaderDtoV1 ToOrderHeaderDtoV1Async(this Auftragskopf kopf)
    {
        if(kopf.Auftragsnummer == null)
            throw new Exception("OrderNumber is null");
        var category = kopf switch
        {
            { Tankzug: true }      => OrderHeaderCategoryV1.TankerTruckString,
            { Planenzug: true }    => OrderHeaderCategoryV1.PlanzugString,
            { Palettenware: true } => OrderHeaderCategoryV1.PalletString,
            _ => "No Category found"
        };
        var status = kopf switch
        {
            { LoeschKng: true } => OrderHeaderStatusV1.DeletedString,
            { Lieferschein: true } => OrderHeaderStatusV1.DeliveryNoteString,
            { Auftrag: true } => OrderHeaderStatusV1.OrderString,
            _ =>"No Status found"
        };
        return new OrderHeaderDtoV1
        {
            OrderNumber = (long)kopf.Auftragsnummer,
            Category = category,
            Status = status,
            CustomerNumber = kopf.KundenNrWE ?? 0,
            Trucknumber = kopf.Zustellart ?? string.Empty,
            Send = kopf.Send ?? false,
        };
    }

    public static OrderLineDtoV1 ToOrderLineDtoV1(this Auftragspo position)
    {
        var status = position.Status switch
        {
            "L" => OrderLineDtoV1Status.LieferungString,
            "A" => OrderLineDtoV1Status.AuftragString,
            _ => OrderLineDtoV1Status.BestellungString
        };

        return new OrderLineDtoV1
        {
            Id = position.Id,
            OrderNumber = position.Auftragsnummer,
            GridPos = position.GridPos ?? 0,
            ArtikelNumber = position.ArtikelNummer ?? 0,
            Name = position.Bezeichnung ?? string.Empty,
            QuantityRequested = position.AvisAnzahl ?? 0,
            QuantityLoaded = position.GesMenge ?? 0,
            RouteNumber = position.StreckenNr ?? 0,
            TruckCompartmentNumber = position.Muehlenzelle ?? string.Empty,
            Status = status,
        };
    }

    public static Auftragskopf ToAuftragskopf(this OrderHeaderCreateDtoV1 orderHeaderCreate, long ordernumber)
    {
        

        var (tankzug, planenzug, palettenware) = orderHeaderCreate.Category switch
        {
            OrderCategories.TankerTruck => (true, false, false),
            OrderCategories.TarpaulinTruck => (false, true, false),
            OrderCategories.PalletizedGoods => (false, false, true),
            _ => throw new Exception("Category not found")
        };
        var creationParam = new CreationParamForAuftragskopf
        {
            Eldatum = orderHeaderCreate.DeliveryDate,
            WarenEmpfaenger = AuftragCustomerOrSupplierData.Empty with{Number = orderHeaderCreate.CustomerNumber},
            LbisDatum = DateTime.Now,
            LvonDatum = DateTime.Now,
            Auftragsdatum = DateTime.Now
        };
        var result = Auftragskopf.Create(creationParam);
        result.Auftragsnummer = ordernumber;
        result.KundenNrWE = orderHeaderCreate.CustomerNumber;
        result.Tankzug = tankzug;
        result.Planenzug = planenzug;
        result.Palettenware = palettenware;
        return result;

    }

    public static Auftragspo ToAuftragspos(this OrderLineCreateDtoV1 orderLine, long ordernumber)
    {
        return new Auftragspo
        {
            Auftragsnummer = ordernumber,
            ArtikelNummer = orderLine.ArticleNumber,
            GesMenge = orderLine.Quantity
        };
    }
}