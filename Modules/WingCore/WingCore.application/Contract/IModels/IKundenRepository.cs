using WingCore.domain.Models;
using WingCore.application.Contract.IModels.Helper;

namespace WingCore.application.Contract.IModels;

public interface IKundenRepository
{
    IEnumerable<Kunden> GetAllCostumers(bool trackChanges);
    ValueTask<Kunden?> GetCostumer(bool trackChanges,long id);
    ValueTask<Kunden?> GetCustomerOverNumber(bool trackChanges, long number);
    ValueTask<IEnumerable<Kunden>> GetCustomerOverSearchParam(bool trackChanges, CustomerSearchParam customerSearchParam);
    ValueTask UpdateCustomer(bool trackChanges, Kunden customer);
    void Update(Kunden customer);
    Task SaveChangesAsync();
    void ClearChangeTracker();
}