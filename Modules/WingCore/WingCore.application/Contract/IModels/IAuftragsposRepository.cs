using WingCore.domain.Models;
using WingCore.domain.Common.Flags;

namespace WingCore.application.Contract.IModels;

public interface IAuftragsposRepository : IRepositoryBas<Auftragspo>
{
    Task<ICollection<Auftragspo>> GetAll(bool trackChanges);
    Task<Auftragspo?> GetAuftragsposById(bool trackChanges, long orderId);
    Task<ICollection<Auftragspo>> GetAuftragsposByOrderNumber(bool trackChanges, long orderId);
    IEnumerable<Auftragspo> GetAuftragsposByArtikels(bool trackChanges, List<long> artikelNumbers);
    Task<bool> ArticleNumberExistsInAuftragsPos(long articleNumber);
    void WriteOrderItemToDb(bool trackChanges, Auftragspo item);
    Task<IEnumerable<Auftragspo>> GetOrderPosWithArticlesHaveSameMainArticleAsync(long mainArticleNumber, OrderPosFlags withOutflag, bool palettenware);
    Task SetFlagAsync(List<long> posIds, OrderPosFlags flag);
    Task RemoveFlagAsync(List<long> posIds, OrderPosFlags flag);
}