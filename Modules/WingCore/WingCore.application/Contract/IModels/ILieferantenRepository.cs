using WingCore.domain.Models;

namespace WingCore.application.Contract.IModels;

public interface ILieferantenRepository
{
    Task<IEnumerable<Lieferanten>> GetAllSuppliers(bool trackChanges);
    Task<IEnumerable<Lieferanten>> GetSupplier(bool trackChanges, long id);
    Task<IEnumerable<Lieferanten>> GetSupplierOverNumber(bool trackChanges, long number);
    Task<IEnumerable<Lieferanten>> GetSupplierOverSearchParam(bool trackChanges, string supplierSearchParam);
    Task<ICollection<Lieferanten>> GetSupplierOverLastChange(DateTime lastChange);
}