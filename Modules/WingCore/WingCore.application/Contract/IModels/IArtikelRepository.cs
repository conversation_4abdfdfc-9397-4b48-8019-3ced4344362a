using WingCore.domain.Models;
using WingCore.application.Contract.IModels.Helper;

namespace WingCore.application.Contract.IModels;

public interface IArtikelRepository
{
    ValueTask<Artikel?> GetArtikelById(long artikelnummer, bool trackBack);
    
    IEnumerable<Artikel> GetAllArtikel(bool trackBack);
    IEnumerable<Artikel> GetMainArtikels(bool trackBack);
    ValueTask<IEnumerable<Artikel>> GetOverSearchParam(bool trackChanges, ArticleSearchParam searchParam);
    Task CreateArtikel(Artikel artikel);
    Task UpdateArtikel(Artikel artikel);
    Task DeleteArtikel(Artikel artikel);
    Task SaveChangesAsync();
    void ClearChangeTracker();
    Task<Dictionary<long, int?>> GetArticleRefNumber(List<long> articleNumbers);
    Task<long> GetMaxArticleNumber();
    Task<Artikel?> GetByEan(string ean);
}