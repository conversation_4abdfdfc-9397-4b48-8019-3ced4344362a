using WingCore.domain.Models;
using WingCore.domain.Common;

namespace WingCore.application.Contract.IModels;

public interface IOutgoingInvoicePostingsRepository : IBaseInvoicePostingsRepository<Arhauptdatei>;
public interface IIncomingInvoicePostingsRepository : IBaseInvoicePostingsRepository<Erhauptdatei>;
public interface IGrainSettlementInvoicePostingsRepository : IBaseInvoicePostingsRepository<Gbvhpt>;
public interface IRapeSettlementInvoicePostingsRepository : IBaseInvoicePostingsRepository<Rgbvhpt>;

public interface IBaseInvoicePostingsRepository<T>
{
    IEnumerable<T> GetAllInvoicePostingsFromInvoice(bool trackChanges, long invoiceNumber, PostingsSchema? schema);
    Task<bool> ArticleNumberExistsInPosition(long articleNumber);
}