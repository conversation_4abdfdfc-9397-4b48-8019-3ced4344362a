using WingCore.domain.Models;
using WingCore.application.Contract.IModels.Helper;

namespace WingCore.application.Contract.Services;

public interface IArticleService
{
    IEnumerable<Artikel> GetAll(bool trackChanges);
    Task<IEnumerable<Artikel>> GetAllArticlesOverMainArticleNumberAsync(bool trackChanges,long mainArticleNumber);
    IEnumerable<Artikel> GetMainArtikels(bool trackChanges);
    ValueTask<Artikel?> Get(bool trackChanges,long id);
    ValueTask<Artikel?> GetOverNumber(bool trackChanges,long number);
    Task Create(Artikel updatedObject);
    ValueTask<IEnumerable<Artikel>> Get(bool trackChanges, ArticleSearchParam searchParam);
    Task Update(Artikel updatedObject);
    Task<Dictionary<long, int?>> GetArticleRefNumber(List<long> articleNumbers);
    public Task<bool> CanBeDeleted(long articleNumber);
    Task<long> GetNextArticleNumberFromDb();
}