using MediatR;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Aufträge.Auftragspositionen.Handler;

public class CheckArticleNumberExistsInAuftragsPosQueryHandler(IAuftragsposRepository repository) : IRequestHandler<CheckArticleNumberExistsInAuftragsPosQuery, bool>
{
    public Task<bool> Handle(CheckArticleNumberExistsInAuftragsPosQuery request, CancellationToken cancellationToken)
    {
        return repository.ArticleNumberExistsInAuftragsPos(request.ArticleNumber);
    }
}