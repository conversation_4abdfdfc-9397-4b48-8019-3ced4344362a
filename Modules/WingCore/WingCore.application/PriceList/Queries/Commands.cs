using MediatR;
using WingCore.application.ApiDtos.MasterData.V1;

namespace WingCore.application.PriceList.Queries;

public record AddPriceListHeaderCommand(PriceListHeaderDtoV1 PriceListHeader) : IRequest<PriceListHeaderDtoV1>;
public record UpdatePriceListHeaderCommand(PriceListHeaderDtoV1 PriceListHeader) : IRequest<PriceListHeaderDtoV1>;
public record DeletePriceListHeaderCommand(PriceListHeaderDtoV1 PriceListHeader) : IRequest;

public record AddPriceListCommand(PriceListDtoV1 PriceList) : IRequest<PriceListDtoV1>;
public record UpdatePriceListCommand(PriceListDtoV1 PriceList) : IRequest<PriceListDtoV1>;
public record DeletePriceListCommand(PriceListDtoV1 PriceList) : IRequest;

public record UpdateCustomerPriceListsCommand(
    long CustomerId,
    long? PriceListNr1,
    long? PriceLIstNr2,
    long? PriceListNr3,
    long? PriceListNr4,
    long? PriceListNr5,
    long? PricelistNr6): IRequest;
