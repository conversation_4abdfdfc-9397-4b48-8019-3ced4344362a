using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.application.PriceList.Queries;

namespace WingCore.application.PriceList.Handlers;

public class UpdateCustomerPriceListsCommandHandler(IKundenRepository kundenRepository) : IRequestHandler<UpdateCustomerPriceListsCommand>
{
    public async Task Handle(UpdateCustomerPriceListsCommand request, CancellationToken cancellationToken)
    {
        var customer = await kundenRepository.GetCostumer(false, request.CustomerId);
        
        if (customer is null)
            throw new Exception("Customer not found");

        customer.KdpreislNr1 = request.PriceListNr1;
        customer.KdpreislNr2 = request.PriceLIstNr2;
        customer.KdpreislNr3 = request.PriceListNr3;
        customer.KdpreislNr4 = request.PriceListNr4;
        customer.KdpreislNr5 = request.PriceListNr5;
        customer.KdpreislNr6 = request.PricelistNr6;
        
        await kundenRepository.UpdateCustomer(false, customer);
    }
} 