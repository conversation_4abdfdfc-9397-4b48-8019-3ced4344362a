using AdginityRestApi;
using WingCore.application.DataTransferObjects.Invoices;
using WingCore.domain.Mappers;

namespace WingCore.application.Invoices.OutgoingInvoice.Mapper;

public static class CompaniesRequests
{
    public static _v3_companies_INPUT_ITEM Mapper(InvoiceSupplierOrHolderDto invoiceSupplierOrHolderDto,
        InvoiceSupplierOrHolderDto? currentInvoiceSupplierOrHolderDto)
    {
        var countryId = invoiceSupplierOrHolderDto.CountryId;
        if (string.IsNullOrWhiteSpace(countryId))
        {
            countryId = invoiceSupplierOrHolderDto.UstIdNr.Length >= 2 && 
                        invoiceSupplierOrHolderDto.UstIdNr[..2].All(char.IsUpper)
                ? invoiceSupplierOrHolderDto.UstIdNr[..2] 
                : string.Empty;
        }
        
        return new _v3_companies_INPUT_ITEM
        {
            Number = (int)(invoiceSupplierOrHolderDto.Number ?? 0),
            Shortname = invoiceSupplierOrHolderDto.Sbg,
            Name = invoiceSupplierOrHolderDto.FullName.TrimEnd(),
            Name2 = invoiceSupplierOrHolderDto.Name2?.TrimEnd() ?? string.Empty,
            Address = invoiceSupplierOrHolderDto.Street,
            CityCode = string.Empty,
            ZipCode = invoiceSupplierOrHolderDto.Plz,
            CityName = invoiceSupplierOrHolderDto.Ort,
            CountryCode = countryId,
            Language = AdfinityCountryLanguageMapper.CountryToLanguageMap.ContainsKey(countryId) 
                ? AdfinityCountryLanguageMapper.CountryToLanguageMap[countryId] 
                : countryId,
            Email = invoiceSupplierOrHolderDto.Email,
            Phone = invoiceSupplierOrHolderDto.Phone,
            ConsolidationNumber = (int)(invoiceSupplierOrHolderDto.Number ?? 0),
            
            VatCountry = countryId,
            VatNumber = invoiceSupplierOrHolderDto.UstIdNr,
            // 1 belgeri 2 europer
            VatType = countryId.Equals("BE",StringComparison.CurrentCultureIgnoreCase) ? "1" : "2",
            PSMFlag = 1,
            AccountingFlag = 1,
            Profile1 = string.IsNullOrWhiteSpace(currentInvoiceSupplierOrHolderDto?.PaymentTerm) ? "S-30I-ISS-EUR" : currentInvoiceSupplierOrHolderDto.PaymentTerm,
            Profile2 = string.Empty 
        };
    }
}