using MediatR;
using WingCore.application.DataTransferObjects.Wgs;
using WingCore.domain.Models;

namespace WingCore.application.Wiegeschein;


public record AllOpenWgsQuery(long? SupplierId) : IRequest<IEnumerable<Wg>>;
public record WgsToSendEasiAdfinityQuery() : IRequest<IEnumerable<WgAdfinityJobDto>>;
public record WgsWithDataQuery(long WgsNumber, bool WithAllData = false) : IRequest<WgsWithLabDto?>;
public record WgsByNumbersQuery(IEnumerable<long> WgsNumbers) : IRequest<IEnumerable<Wg>>;

// WGS Email Queries
public record WgsToSendEmailAutomaticallyQuery() : IRequest<IEnumerable<WgsWithLabDto>>;
public record AllWgsForManualEmailSendQuery() : IRequest<IEnumerable<WgsWithLabDto>>;
