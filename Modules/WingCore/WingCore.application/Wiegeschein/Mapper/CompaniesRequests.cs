using AdginityRestApi;
using WingCore.domain.Mappers;
using WingCore.domain.Models;

namespace WingCore.application.Wiegeschein.Mapper;

public static class CompaniesRequests
{
    public static _v3_companies_INPUT_ITEM MapperToCompaniesRequest(
        this Lieferanten lieferanten,
        bool isInvoiceHolder,
        string countryId)
    {
        if (string.IsNullOrWhiteSpace(countryId))
        {
            countryId = lieferanten.LfustIdNr?.Length >= 2 && 
                        lieferanten.LfustIdNr[..2].All(char.IsUpper)
                ? lieferanten.LfustIdNr[..2] 
                : string.Empty;
        }

        return new _v3_companies_INPUT_ITEM
        {
            Number = (int)lieferanten.Lfnummer,
            Shortname = lieferanten.Lfsbg,
            Name = lieferanten.Lfname1?.TrimEnd(),
            Name2 = lieferanten.Lfname2?.TrimEnd() ?? string.Empty,
            Address = lieferanten.Lfstrasse,
            CityCode = string.Empty,
            ZipCode = lieferanten.Lfplz,
            CityName = lieferanten.Lfort,
            CountryCode = countryId,
            Language = AdfinityCountryLanguageMapper.CountryToLanguageMap.ContainsKey(countryId) 
                ? AdfinityCountryLanguageMapper.CountryToLanguageMap[countryId] 
                : countryId,
            Email = lieferanten.Lfemail,
            Phone = lieferanten.Lftelefon1,
            ConsolidationNumber = (int)lieferanten.Lfnummer,
            
            VatCountry = countryId,
            VatNumber = lieferanten.LfustIdNr,
            // 1 belgeri 2 europer
            VatType = countryId.Equals("BE",StringComparison.CurrentCultureIgnoreCase) ? "1" : "2",
            PSMFlag = 1,
            AccountingFlag = 1,
            Profile1 = isInvoiceHolder ? "C-30I-ISS-EUR" : string.Empty,
            Profile2 = string.Empty
        };
    }
}