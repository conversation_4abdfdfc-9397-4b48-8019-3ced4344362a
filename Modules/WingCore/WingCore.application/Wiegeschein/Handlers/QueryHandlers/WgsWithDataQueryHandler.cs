using MediatR;
using WingCore.application.Contract.IModels;
using WingCore.application.DataTransferObjects.Wgs;
using WingCore.domain.Common.Flags;
using WingCore.domain.Models;

namespace WingCore.application.Wiegeschein.Handlers.QueryHandlers;

public class WgsWithDataQueryHandler(IWgsRepository wgsRepository, IWgslabRepository wgslabRepository) : IRequestHandler<WgsWithDataQuery, WgsWithLabDto?>
{
    public async Task<WgsWithLabDto?> Handle(WgsWithDataQuery query, CancellationToken cancellationToken)
    {
        var wgs = await wgsRepository.GetWgsByNumbers([query.WgsNumber]);
        var wg = wgs.FirstOrDefault();

        if (wg is null)
            return null;

        // Get the corresponding Wgslab data
        var wgslab = await wgslabRepository.GetWgslabByWgsnr(wg.Wgsnr);

        return new WgsWithLabDto(wg, wgslab);
    }
}

public class WgsByNumbersQueryHandler(IWgsRepository wgsRepository) : IRequestHandler<WgsByNumbersQuery, IEnumerable<Wg>>
{
    public async Task<IEnumerable<Wg>> Handle(WgsByNumbersQuery query, CancellationToken cancellationToken)
    {
        return await wgsRepository.GetWgsByNumbers(query.WgsNumbers);
    }
}

// Email Query Handlers
public class WgsToSendEmailAutomaticallyQueryHandler(IWgsRepository wgsRepository, IWgslabRepository wgslabRepository) : IRequestHandler<WgsToSendEmailAutomaticallyQuery, IEnumerable<WgsWithLabDto>>
{
    public async Task<IEnumerable<WgsWithLabDto>> Handle(WgsToSendEmailAutomaticallyQuery query, CancellationToken cancellationToken)
    {
        // Get all WGS that don't have the WgsMailSendSuccess flag set (filtered at database level)
        var wgsToSend = await wgsRepository.GetWgsForEmailSending(false, null);
        var wgsToSendList = wgsToSend.ToList();

        if (!wgsToSendList.Any())
            return [];

        // Get corresponding Wgslab data for all WGS
        var wgsnrs = wgsToSendList.Select(w => w.Wgsnr).ToList();
        var wgslabs = await wgslabRepository.GetWgslabsByWgsnrs(wgsnrs);
        var wgslabDict = wgslabs.ToDictionary(w => w.Wgsnr, w => w);

        // Combine WGS and Wgslab data
        return wgsToSendList.Select(wg => new WgsWithLabDto(wg, wgslabDict.GetValueOrDefault(wg.Wgsnr))).ToList();
    }
}

public class AllWgsForManualEmailSendQueryHandler(IWgsRepository wgsRepository, IWgslabRepository wgslabRepository) : IRequestHandler<AllWgsForManualEmailSendQuery, IEnumerable<WgsWithLabDto>>
{
    public async Task<IEnumerable<WgsWithLabDto>> Handle(AllWgsForManualEmailSendQuery query, CancellationToken cancellationToken)
    {
        // Get all open WGS (regardless of email send status) for manual selection
        var allOpenWgs = await wgsRepository.GetOpenWgs(false, null);
        var wgsList = allOpenWgs.ToList();

        if (!wgsList.Any())
            return [];

        // Get corresponding Wgslab data for all WGS
        var wgsnrs = wgsList.Select(w => w.Wgsnr).ToList();
        var wgslabs = await wgslabRepository.GetWgslabsByWgsnrs(wgsnrs);
        var wgslabDict = wgslabs.ToDictionary(w => w.Wgsnr, w => w);

        // Combine WGS and Wgslab data
        return wgsList.Select(wg => new WgsWithLabDto(wg, wgslabDict.GetValueOrDefault(wg.Wgsnr))).ToList();
    }
}
