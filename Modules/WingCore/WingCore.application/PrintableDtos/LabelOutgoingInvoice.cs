using PrinterService;
using WingCore.application.DataTransferObjects.Invoices;

namespace WingCore.application.PrintableDtos;

public class LabelOutgoingInvoice : IPrintObject
{
    public string PrintType() => "AR";
    public string ObjName() => "LabelOutgoingInvoice";

        public static LabelOutgoingInvoice Create(OutgoingInvoiceOverviewDto outgoingInvoice)
        {
            var newObj = new LabelOutgoingInvoice()
                    {
                        ARNummer = outgoingInvoice.InvoiceNumber,
                        ARAltNr = outgoingInvoice.CancelOrCreditNoteInvoiceNumber,
                        ARDatum = outgoingInvoice.InvoiceDate.ToString("d"),
                        ARGesBrutto = outgoingInvoice.TotalGross,
                        ARGesNetto = outgoingInvoice.TotalNet,
                        ARBestellNr = outgoingInvoice.OrderNumber,
                        ARGBLNr = outgoingInvoice.GblNr ?? string.Empty,
                        ARFbzgKng = outgoingInvoice.GblNr ?? string.Empty,
                        WertGut = outgoingInvoice.WertGut ?? false,
                        
                        ARAnredeRE = outgoingInvoice.InvoiceHolder?.Anrede ?? string.Empty,
                        ARBLDRE = outgoingInvoice.InvoiceHolder?.BundeslandNumber ?? 0,
                        ARKDNrRE = outgoingInvoice.InvoiceHolder?.Number ?? 0,
                        ARLandRE = outgoingInvoice.InvoiceHolder?.Country ?? string.Empty,
                        ARName1RE = outgoingInvoice.InvoiceHolder?.Name ?? string.Empty,
                        ARName2RE = outgoingInvoice.InvoiceHolder?.Name2 ?? string.Empty,
                        ARName3RE = outgoingInvoice.InvoiceHolder?.Name3 ?? string.Empty,
                        ARName4RE = outgoingInvoice.InvoiceHolder?.Name4 ?? string.Empty,
                        AROrtRE = outgoingInvoice.InvoiceHolder?.Ort ?? string.Empty,
                        ARPLZRE = outgoingInvoice.InvoiceHolder?.Plz ?? string.Empty,
                        ARStrasseRE = outgoingInvoice.InvoiceHolder?.Street ?? string.Empty,
                        ARUstIdNrRE = outgoingInvoice.InvoiceHolder?.UstIdNr ?? string.Empty,
                        
                        ARAnredeWE = outgoingInvoice.GoodsRecipients?.Anrede ?? string.Empty,
                        ARKDNrWE = outgoingInvoice.GoodsRecipients?.Number ?? 0,
                        ARName1WE = outgoingInvoice.GoodsRecipients?.Name ?? string.Empty,
                        ARName2WE = outgoingInvoice.GoodsRecipients?.Name2 ?? string.Empty,
                        ARName3WE = outgoingInvoice.GoodsRecipients?.Name2 ?? string.Empty,
                        ARName4WE = outgoingInvoice.GoodsRecipients?.Name2 ?? string.Empty,
                        AROrtWE = outgoingInvoice.GoodsRecipients?.Ort ?? string.Empty,
                        ARPLZWE = outgoingInvoice.GoodsRecipients?.Plz ?? string.Empty,
                        ARStrasseWE = outgoingInvoice.GoodsRecipients?.Street ?? string.Empty,

                        ARVertrNr = outgoingInvoice.RepresentativeNumber,
                        ARZhlDruck = outgoingInvoice.AdditionalLinesForPrint,
                        ARZhlDruck02 = outgoingInvoice.AdditionalLinesForPrint2,
                        ARZhlDruck03 = outgoingInvoice.AdditionalLinesForPrint3,

                        ARLkw = outgoingInvoice.LkwInformation ?? string.Empty,

                        ARBank = outgoingInvoice.BankInfo?.BankName ?? string.Empty,
                        ARBearbeiter = outgoingInvoice.CreationUser ?? string.Empty,
                        ARZahlZiel = outgoingInvoice.InvoiceHolder?.PaymentTerm ?? string.Empty,
                        ARLFNrWE = outgoingInvoice.GoodsRecipients?.LfNr ?? string.Empty,
                    };

            if (outgoingInvoice.ListTax.Count >= 1)
            {
                newObj.ARGesBrutto1 = outgoingInvoice.ListTax[0].GrossAmount;
                newObj.ARNetto1 = outgoingInvoice.ListTax[0].NetAmount;
                newObj.ARMwSt1Proz = outgoingInvoice.ListTax[0].Vat;
                newObj.ARMwst1 = outgoingInvoice.ListTax[0].VatAmount;
            }
            
            if (outgoingInvoice.ListTax.Count >= 2)
            {
                newObj.ARGesBrutto2 = outgoingInvoice.ListTax[1].GrossAmount;
                newObj.ARNetto2 = outgoingInvoice.ListTax[1].NetAmount;
                newObj.ARMwSt2Proz = outgoingInvoice.ListTax[1].Vat;
                newObj.ARMwst2 = outgoingInvoice.ListTax[1].VatAmount;
            }
            
            if (outgoingInvoice.ListTax.Count >= 3)
            {
                newObj.ARGesBrutto3 = outgoingInvoice.ListTax[2].GrossAmount;
                newObj.ARNetto3 = outgoingInvoice.ListTax[2].NetAmount;
                newObj.ARMwSt3Proz = outgoingInvoice.ListTax[2].Vat;
                newObj.ARMwst3 = outgoingInvoice.ListTax[2].VatAmount;
            }
            
            newObj.Position = outgoingInvoice.ListAllPostings.Select(p => new Position()
            {
                Anzahl = p.Quantity,
                ArtNr = p.Number,
                Bezeichnung = p.Description,
                BzgGr = p.ReferenceSize,
                Einzelpreis = p.UnitPrice,
                Gesamtnetto = p.TotalNetto,
                Zn = p.TotalNettoWithDiscount,
                Gesamtmenge = p.TotalQuantity,
                MwstProz = p.Tax.Vat,
                GridPosition = p.GridPosition,
                ArtEAN = p.ArticleEan
            });
            
            return newObj;
        }

        public static LabelOutgoingInvoice Dummy()
        {
            var newObj = new LabelOutgoingInvoice
            {
                ARAnredeRE = "Herr",
                ARName1RE = "Dummy Name 1",
                ARName2RE = "Dummy Name 2",
                ARName3RE = "Dummy Name 3",
                ARName4RE = "Dummy Name 4",
                ARNummer = 99999999,
                ARAltNr = 99,
                ARDatum = DateTime.Now.ToString("d"),
                ARGesBrutto = 226,
                ARGesNetto = 200,
                ARMwst1 = 19,
                ARNetto1 = 100,
                ARGesBrutto1 = 119,
                ARMwSt1Proz = 19,
                ARMwst2 = 7,
                ARNetto2 = 100,
                ARGesBrutto2 = 107,
                ARMwSt2Proz = 7,
                Position =
                [
                    new Position()
                    {
                        Bezeichnung = "Dummy Position 1",
                        Anzahl = 1,
                        ArtNr = 1111111,
                        Einzelpreis = 119,
                        Gesamtmenge = 1,
                        Gesamtnetto = 100,
                        MwstProz = 19,
                        GridPosition = 1,
                        BzgGr = "Dummy"
                    },
                    new Position()
                    {
                        Bezeichnung = "Dummy Position 2",
                        Anzahl = 1,
                        ArtNr = 222222222,
                        Einzelpreis = 107,
                        Gesamtmenge = 1,
                        Gesamtnetto = 100,
                        MwstProz = 7,
                        GridPosition = 2,
                        BzgGr = "Dummy"
                    },
                ]
            };


            return newObj;
        }

        private string FileName { get; set; } = string.Empty;
        public string GetNameForCrationFile() => FileName;
        public string GetBeginNameFromFileList() => "AR";
        public string GetEndNameFromFileList() => ".lst";

        public long ARNummer { get; set; }
        public long ARAltNr { get; set; }
        public string ARDatum { get; set; } = string.Empty;
        public decimal ARGesBrutto { get; set; }
        public decimal ARGesNetto { get; set; }
        public string ARBestellNr { get; set; } = string.Empty;
        public string ARGBLNr { get; set; } = string.Empty;
        public string ARFbzgKng { get; set; } = string.Empty;
        public bool WertGut { get; set; }
        

        public decimal ARNetto1 { get; set; }
        public decimal ARGesBrutto1 { get; set; }
        public decimal ARMwSt1Proz { get; set; }
        public decimal ARMwst1 { get; set; }
        
        
        public decimal ARNetto2 { get; set; }
        public decimal ARGesBrutto2 { get; set; }
        public decimal ARMwSt2Proz { get; set; }
        public decimal ARMwst2 { get; set; }
        
        
        public decimal ARNetto3 { get; set; }
        public decimal ARGesBrutto3 { get; set; }
        public decimal ARMwSt3Proz { get; set; }
        public decimal ARMwst3 { get; set; }
        
        public long ARBLDRE { get; set; }
        public long ARKDNrRE { get; set; }
        public string ARAnredeRE { get; set; } = string.Empty;
        public string ARLandRE { get; set; } = string.Empty;
        public string ARName1RE { get; set; } = string.Empty;
        public string ARName2RE { get; set; } = string.Empty;
        public string ARName3RE { get; set; } = string.Empty;
        public string ARName4RE { get; set; } = string.Empty;
        public string AROrtRE { get; set; } = string.Empty;
        public string ARPLZRE { get; set; } = string.Empty;
        public string ARStrasseRE { get; set; } = string.Empty;
        public string ARUstIdNrRE { get; set; } = string.Empty;


        public long ARKDNrWE { get; set; }
        public string ARAnredeWE { get; set; } = string.Empty;
        public string ARName1WE { get; set; } = string.Empty;
        public string ARName2WE { get; set; } = string.Empty;
        public string ARName3WE { get; set; } = string.Empty;
        public string ARName4WE { get; set; } = string.Empty;
        public string AROrtWE { get; set; } = string.Empty;
        public string ARPLZWE { get; set; } = string.Empty;
        public string ARStrasseWE { get; set; } = string.Empty;
        public string VVONrWE { get; set; } = string.Empty;
        
        public long ARVertrNr { get; set; }
        public string ARZhlDruck { get; set; } = string.Empty;
        public string ARZhlDruck02 { get; set; } = string.Empty;
        public string ARZhlDruck03 { get; set; } = string.Empty;
        public string ARLkw { get; set; } = string.Empty;
        public string ARBank { get; set; } = string.Empty;
        public string ARBearbeiter { get; set; } = string.Empty;
        public string ARZahlZiel { get; set; } = string.Empty;
        public string ARLFNrWE { get; set; } = string.Empty;
        
        //Fields

        public IEnumerable<Position>? Position { get; set; }
        /*public string Bezeichnung { get; set; } = string.Empty;
        //public IEnumerable<string> Bezeichnung { get; set; } = ["a","b"];
        public int Anzahl { get; set; } = 0;
        public long ArtNr { get; set; }
        public string BzgGr { get; set; } = string.Empty;
        public decimal Einzelpreis { get; set; } = 0;
        public decimal Gesamtmenge { get; set; } = 0;
        public decimal Gesamtnetto { get; set; } = 0;
        public decimal MwstProz { get; set; } = 0; */
        //public ICollection<Position> Position { get; set; } = [new Position()];
        
        /*public IEnumerable<string> Bezeichnung { get; set; } = ["a","b","v"];
        //public ICollection<string> Bezeichnung { get; set; } = ["a","b","v"];
        public IEnumerable<long> Anzahl { get; set; } = [1,2,3];
        public IEnumerable<long> ArtNr { get; set; } = [12,22,2];
        public IEnumerable<string> BzgGr { get; set; } = ["R","q","d"];
        public IEnumerable<double> Einzelpreis { get; set; } = [9,9,9];
        public IEnumerable<double> Gesamtmenge { get; set; } = [9,9,9];
        public IEnumerable<double> Gesamtnetto { get; set; } = [9,9,9];
        public IEnumerable<double> MwstProz { get; set; } = [9,9,9];*/
}

public record Position()
{
    public string Bezeichnung { get; set; } = string.Empty;
    public int GridPosition { get; set; } = 0;
    public decimal Anzahl { get; set; } = 0;
    public long ArtNr { get; set; }
    public string BzgGr { get; set; } = string.Empty;
    public decimal Einzelpreis { get; set; } = 0;
    public decimal Gesamtmenge { get; set; } = 0;
    public decimal Gesamtnetto { get; set; } = 0;
    public decimal Zn { get; set; } = 0;
    public decimal MwstProz { get; set; } = 0;
    public string ArtEAN { get; set; } = string.Empty;
}
