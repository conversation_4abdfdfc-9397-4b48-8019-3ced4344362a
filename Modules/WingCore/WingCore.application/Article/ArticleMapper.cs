using WingCore.application.DataTransferObjects.Articles;
using WingCore.domain.Models;

namespace WingCore.application.Article;

public static class ArticleMapper
{
    public static ArticleDto MapArticleToDto(this Artikel article)
    {
        return new ArticleDto
        {
            Artikelnr = article.Artikelnr,
            ArtSbg = article.ArtSbg,
            ArtBezText1 = article.ArtBezText1,
            ArtBezText2 = article.ArtBezText2,
            ArtBezText3 = article.ArtBezText3,
            ArtBezText4 = article.ArtBezText4,
            ArtBezugsgr = article.ArtBezugsgr,
            ArtFaktor = article.ArtFaktor,
            ArtEkpreis = article.ArtEkpreis,
            ArtVkpreis = article.ArtVkpreis,
            ArtVkbrutto = article.ArtVkbrutto,
            NettoEk = article.NettoEk,
            EmpfBruttoVk = article.EmpfBruttoVk,
            ArtKassBrutto = article.ArtKassBrutto,
            ArtMwStSchl = article.ArtMwStSchl,
            ArtMwSt = article.ArtMwSt,
            ArtEan = article.ArtEan,
            ArtFrachtZuschlag = article.ArtFrachtZuschlag,
            ArtVerpacktr = article.ArtVerpacktr,
            ArtHaltbarkeit = article.ArtHaltbarkeit,
            ArtFrachtNr = article.ArtFrachtNr,
            Hartikel = article.Hartikel,
            ArtLagerStellen = article.ArtLagerStellen,
            ArtLagerstelle = article.ArtLagerstelle,
            ArtGruppenKennung = article.ArtGruppenKennung,
            ArtProfitwert = article.ArtProfitwert,
            KolliAnzPal = article.KolliAnzPal,
            ArtEkpreisKolli = article.ArtEkpreisKolli,
            ArtKolliInhalt = article.ArtKolliInhalt,
            ArtKonto1 = article.ArtKonto1,
            ArtKonto2 = article.ArtKonto2,
            ArtKonto3 = article.ArtKonto3,
            ArtKonto4 = article.ArtKonto4,
            ArtKonto5 = article.ArtKonto5,
            ArtKonto6 = article.ArtKonto6,
            ArtWnr = article.ArtWnr,
            InhFeld = article.InhFeld,
            WarenKng = article.WarenKng,
            VglMgeEh = article.VglMgeEh,
            VglPreis = article.VglPreis,
            AktPreis = article.AktPreis,
            AktDtVon = article.AktDtVon,
            AktDtBis = article.AktDtBis,
            BstRythm = article.BstRythm,
            Min = article.Min,
            Max = article.Max,
            ArtGrp1 = article.ArtGrp1,
            ArtGrp2 = article.ArtGrp2,
            ArtGrp3 = article.ArtGrp3,
            ArtGrp4 = article.ArtGrp4,
            ArtGrp5 = article.ArtGrp5,
            ArtGrp6 = article.ArtGrp6,
            ArtRefNr = article.ArtRefNr,
            ArtLkwtype = article.ArtLkwtype,
            ArtRabattfähig = article.ArtRabattfähig,
            ArtSkontierbar = article.ArtSkontierbar,
            Lgstatistik = article.Lgstatistik,
            Lgfaktor = article.Lgfaktor,
            ArtPfand = article.ArtPfand,
            ArtOptKas = article.ArtOptKas,
            Hlgewicht = article.Hlgewicht,
            ArtZbem1 = article.ArtZbem1,
            ArtZbem2 = article.ArtZbem2,
            ArtZbem3 = article.ArtZbem3,
            ArtZbem4 = article.ArtZbem4,
            ArtZbem5 = article.ArtZbem5,
            ArtLiefNr = article.ArtLiefNr,
            ArtBestellNr = article.ArtBestellNr,
            ArtChargenNr = article.ArtChargenNr,
            KostentraegerNr = article.KostentraegerNr,
            Kostenstelle = article.Kostenstelle,
            ArtPalettengewicht = article.ArtPalettengewicht,
            ArtText = article.ArtText,
            Zutaten = article.Zutaten,
            ArtPakGewicht = article.ArtPakGewicht,
            ArtKtoZug = article.ArtKtoZug,
            Send = article.Send,
            ArtSperr = article.ArtSperr,
            EnthEan = article.EnthEan,
            KonsEan = article.KonsEan,
            Palette = article.Palette,
            PalNr = article.PalNr,
            DisplayArtikel = article.DisplayArtikel,
            RezNr = article.RezNr,
            ArtLetzteÄnderung = article.ArtLetzteÄnderung,
            DruckFormSack = article.DruckFormSack,
            DruckForm = article.DruckForm,
            LieferantIln = article.LieferantIln,
            SysUser = article.SysUser,
            SysTime = article.SysTime,
            Id = article.Id,
            Bemerkung = article.Bemerkung,
            MindBestandMge = article.MindBestandMge,
            RezNrAbsMasch = article.RezNrAbsMasch,
            RezNrStretcher = article.RezNrStretcher,
            RezNrPalettierer = article.RezNrPalettierer,
            ArtikelIdent = article.ArtikelIdent,
            N = article.N,
            P = article.P,
            P2o5 = article.P2o5,
            K = article.K,
            ArtEinhVerp1 = article.ArtEinhVerp1,
            ArtEinhVp = article.ArtEinhVp,
            EtDruck = article.EtDruck,
            SchwerGetr = article.SchwerGetr
        };
    }
    
    public static Artikel MapArticleDtoToArtikel(this ArticleDto article)
    {
        return new Artikel 
        {
            Artikelnr = article.Artikelnr,
            ArtSbg = article.ArtSbg,
            ArtBezText1 = article.ArtBezText1,
            ArtBezText2 = article.ArtBezText2,
            ArtBezText3 = article.ArtBezText3,
            ArtBezText4 = article.ArtBezText4,
            ArtBezugsgr = article.ArtBezugsgr,
            ArtFaktor = article.ArtFaktor,
            ArtEkpreis = article.ArtEkpreis,
            ArtVkpreis = article.ArtVkpreis,
            ArtVkbrutto = article.ArtVkbrutto,
            NettoEk = article.NettoEk,
            EmpfBruttoVk = article.EmpfBruttoVk,
            ArtKassBrutto = article.ArtKassBrutto,
            ArtMwStSchl = article.ArtMwStSchl,
            ArtMwSt = article.ArtMwSt,
            ArtEan = article.ArtEan,
            ArtFrachtZuschlag = article.ArtFrachtZuschlag,
            ArtVerpacktr = article.ArtVerpacktr,
            ArtHaltbarkeit = article.ArtHaltbarkeit,
            ArtFrachtNr = article.ArtFrachtNr,
            Hartikel = article.Hartikel,
            ArtLagerStellen = article.ArtLagerStellen,
            ArtLagerstelle = article.ArtLagerstelle,
            ArtGruppenKennung = article.ArtGruppenKennung,
            ArtProfitwert = article.ArtProfitwert,
            KolliAnzPal = article.KolliAnzPal,
            ArtEkpreisKolli = article.ArtEkpreisKolli,
            ArtKolliInhalt = article.ArtKolliInhalt,
            ArtKonto1 = article.ArtKonto1,
            ArtKonto2 = article.ArtKonto2,
            ArtKonto3 = article.ArtKonto3,
            ArtKonto4 = article.ArtKonto4,
            ArtKonto5 = article.ArtKonto5,
            ArtKonto6 = article.ArtKonto6,
            ArtWnr = article.ArtWnr,
            InhFeld = article.InhFeld,
            WarenKng = article.WarenKng,
            VglMgeEh = article.VglMgeEh,
            VglPreis = article.VglPreis,
            AktPreis = article.AktPreis,
            AktDtVon = article.AktDtVon,
            AktDtBis = article.AktDtBis,
            BstRythm = article.BstRythm,
            Min = article.Min,
            Max = article.Max,
            ArtGrp1 = article.ArtGrp1,
            ArtGrp2 = article.ArtGrp2,
            ArtGrp3 = article.ArtGrp3,
            ArtGrp4 = article.ArtGrp4,
            ArtGrp5 = article.ArtGrp5,
            ArtGrp6 = article.ArtGrp6,
            ArtRefNr = article.ArtRefNr,
            ArtLkwtype = article.ArtLkwtype,
            ArtRabattfähig = article.ArtRabattfähig,
            ArtSkontierbar = article.ArtSkontierbar,
            Lgstatistik = article.Lgstatistik,
            Lgfaktor = article.Lgfaktor,
            ArtPfand = article.ArtPfand,
            ArtOptKas = article.ArtOptKas,
            Hlgewicht = article.Hlgewicht,
            ArtZbem1 = article.ArtZbem1,
            ArtZbem2 = article.ArtZbem2,
            ArtZbem3 = article.ArtZbem3,
            ArtZbem4 = article.ArtZbem4,
            ArtZbem5 = article.ArtZbem5,
            ArtLiefNr = article.ArtLiefNr,
            ArtBestellNr = article.ArtBestellNr,
            ArtChargenNr = article.ArtChargenNr,
            KostentraegerNr = article.KostentraegerNr,
            Kostenstelle = article.Kostenstelle,
            ArtPalettengewicht = article.ArtPalettengewicht,
            ArtText = article.ArtText,
            Zutaten = article.Zutaten,
            ArtPakGewicht = article.ArtPakGewicht,
            ArtKtoZug = article.ArtKtoZug,
            Send = article.Send,
            ArtSperr = article.ArtSperr,
            EnthEan = article.EnthEan,
            KonsEan = article.KonsEan,
            Palette = article.Palette,
            PalNr = article.PalNr,
            DisplayArtikel = article.DisplayArtikel,
            RezNr = article.RezNr,
            ArtLetzteÄnderung = article.ArtLetzteÄnderung,
            DruckFormSack = article.DruckFormSack,
            DruckForm = article.DruckForm,
            LieferantIln = article.LieferantIln,
            SysUser = article.SysUser,
            SysTime = article.SysTime,
            Id = article.Id,
            Bemerkung = article.Bemerkung,
            MindBestandMge = article.MindBestandMge,
            RezNrAbsMasch = article.RezNrAbsMasch,
            RezNrStretcher = article.RezNrStretcher,
            RezNrPalettierer = article.RezNrPalettierer,
            ArtikelIdent = article.ArtikelIdent,
            N = article.N,
            P = article.P,
            P2o5 = article.P2o5,
            K = article.K,
            ArtEinhVerp1 = article.ArtEinhVerp1,
            ArtEinhVp = article.ArtEinhVp,
            EtDruck = article.EtDruck,
            SchwerGetr = article.SchwerGetr
        };
    }
}