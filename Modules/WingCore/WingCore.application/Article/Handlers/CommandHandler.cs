using MediatR;
using WingCore.application.Article.Queries;
using WingCore.application.Contract.IModels;
using WingCore.application.Contract.Services;

namespace WingCore.application.Article.Handlers;

public class AddArticleCommandHandler(IArtikelRepository articleRepository, IArticleService articleService) : IRequestHandler<AddArticleCommand>
{
    public async Task Handle(AddArticleCommand request, CancellationToken cancellationToken)
    {
        var article = request.Article.MapArticleDtoToArtikel();
        
        article.Artikelnr = await articleService.GetNextArticleNumberFromDb(); 
        
        await articleRepository.CreateArtikel(article);
    }
}

public class UpdateArticleCommandHandler(IArtikelRepository articleRepository) : IRequestHandler<UpdateArticleCommand>
{
    public async Task Handle(UpdateArticleCommand request, CancellationToken cancellationToken)
    {
        var article = request.Article.MapArticleDtoToArtikel();
        await articleRepository.UpdateArtikel(article);
    }
}

public class DeleteArticleCommandHandler(IArtikelRepository articleRepository, IArticleService articleService) : IRequestHandler<DeleteArticleCommand, bool>
{
    public async Task<bool> Handle(DeleteArticleCommand request, CancellationToken cancellationToken)
    {
        var canBeDeleted = await articleService.CanBeDeleted(request.Article.Artikelnr);
        
        if (!canBeDeleted)
            return false;
        
        await articleRepository.DeleteArtikel(request.Article.MapArticleDtoToArtikel());
        
        return true;
    }
}
