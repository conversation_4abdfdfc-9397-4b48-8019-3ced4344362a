using MediatR;
using WingCore.application.Article.Queries;
using WingCore.application.Contract.IModels;
using WingCore.application.DataTransferObjects.Articles;

namespace WingCore.application.Article.Handlers;

public class GetAllArticlesQueryHandler(IArtikelRepository articleRepository) : IRequestHandler<GetAllArticlesQuery, IEnumerable<ArticleDto>>
{
    public Task<IEnumerable<ArticleDto>> Handle(GetAllArticlesQuery request, CancellationToken cancellationToken)
    {
        var articles = articleRepository.GetAllArtikel(false);
        
        return Task.FromResult(articles.Select(a => a.MapArticleToDto()));
    }
}

public class GetArticleByNumberQueryHandler(IArtikelRepository articleRepository) : IRequestHandler<GetArticleByNumberQuery, ArticleDto?>
{
    public async Task<ArticleDto?> Handle(GetArticleByNumberQuery request, CancellationToken cancellationToken)
    {
        var article = await articleRepository.GetArtikelById(request.Artikelnr, false);
       
        return article?.MapArticleToDto();
    }
}
