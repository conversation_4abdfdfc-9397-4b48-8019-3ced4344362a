using MediatR;
using WingCore.application.ARHauptdatei.Queries;
using WingCore.application.Contract.IModels;

namespace WingCore.application.ARHauptdatei.Handlers;

public class CheckArticleNumberExistsInPositionQueryHandler(IOutgoingInvoicePostingsRepository outgoingInvoiceRepository) : IRequestHandler<CheckArticleNumberExistsInPositionQuery, bool>
{
    public async Task<bool> Handle(CheckArticleNumberExistsInPositionQuery request, CancellationToken cancellationToken)
    {
        return await outgoingInvoiceRepository.ArticleNumberExistsInPosition(request.ArticleNumber);
    }
}
