using WingCore.domain.Models;

namespace WingCore.application.DataTransferObjects.Wgs;

public class WgsWithLabDto
{
    public Wg Wgs { get; set; } = null!;
    public Wgslab? Wgslab { get; set; }
    
    // Convenience properties for easy access
    public long Wgsnr => Wgs.Wgsnr;
    public long? Wgslfnr => Wgs.Wgslfnr;
    
    // Constructor
    public WgsWithLabDto(Wg wgs, Wgslab? wgslab = null)
    {
        Wgs = wgs;
        Wgslab = wgslab;
    }
    
    // Parameterless constructor for serialization
    public WgsWithLabDto() { }
}
