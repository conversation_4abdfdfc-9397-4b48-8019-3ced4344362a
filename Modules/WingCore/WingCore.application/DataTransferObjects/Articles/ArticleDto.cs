namespace WingCore.application.DataTransferObjects.Articles;

public record ArticleDto
{ 
    public long Artikelnr { get; set; }
    public string ArtikelnrAsString => Convert.ToString(Artikelnr);

    public string? ArtSbg { get; set; }

    public string? ArtBezText1 { get; set; }

    public string? ArtBezText2 { get; set; }

    public string? ArtBezText3 { get; set; }

    public string? ArtBezText4 { get; set; }

    public string? ArtBezugsgr { get; set; }

    public short? ArtFaktor { get; set; }

    public decimal? ArtEkpreis { get; set; }

    public decimal? ArtVkpreis { get; set; }

    public decimal? ArtVkbrutto { get; set; }

    public decimal? NettoEk { get; set; }

    public decimal? EmpfBruttoVk { get; set; }

    public decimal? ArtKassBrutto { get; set; }

    public short? ArtMwStSchl { get; set; }

    public decimal? ArtMwSt { get; set; }

    public string? ArtEan { get; set; }

    public decimal? ArtFrachtZuschlag { get; set; }

    public long? ArtVerpacktr { get; set; }

    public string? ArtHaltbarkeit { get; set; }

    public long? ArtFrachtNr { get; set; }

    public long? Hartikel { get; set; }

    public string? ArtLagerStellen { get; set; }

    public string? ArtLagerstelle { get; set; }

    public short? ArtGruppenKennung { get; set; }

    public int? ArtProfitwert { get; set; }

    public string? KolliAnzPal { get; set; }

    public decimal? ArtEkpreisKolli { get; set; }

    public long? ArtKolliInhalt { get; set; }

    public string? ArtKonto1 { get; set; }

    public string? ArtKonto2 { get; set; }

    public string? ArtKonto3 { get; set; }

    public string? ArtKonto4 { get; set; }

    public string? ArtKonto5 { get; set; }

    public string? ArtKonto6 { get; set; }

    public string? ArtWnr { get; set; }

    public string? InhFeld { get; set; }

    public short? WarenKng { get; set; }

    public string? VglMgeEh { get; set; }

    public decimal? VglPreis { get; set; }

    public decimal? AktPreis { get; set; }

    public DateTime? AktDtVon { get; set; }

    public DateTime? AktDtBis { get; set; }

    public short? BstRythm { get; set; }

    public double? Min { get; set; }

    public double? Max { get; set; }

    public short? ArtGrp1 { get; set; }

    public short? ArtGrp2 { get; set; }

    public short? ArtGrp3 { get; set; }

    public short? ArtGrp4 { get; set; }

    public short? ArtGrp5 { get; set; }

    public short? ArtGrp6 { get; set; }

    public int? ArtRefNr { get; set; }

    public short? ArtLkwtype { get; set; }

    public bool? ArtRabattfähig { get; set; }

    public bool? ArtSkontierbar { get; set; }

    public decimal? Lgstatistik { get; set; }

    public string? Lgfaktor { get; set; }

    public decimal? ArtPfand { get; set; }

    public short? ArtOptKas { get; set; }

    public float? Hlgewicht { get; set; }

    public string? ArtZbem1 { get; set; }

    public string? ArtZbem2 { get; set; }

    public string? ArtZbem3 { get; set; }

    public string? ArtZbem4 { get; set; }

    public string? ArtZbem5 { get; set; }

    public long? ArtLiefNr { get; set; }

    public string? ArtBestellNr { get; set; }

    public string? ArtChargenNr { get; set; }

    public long? KostentraegerNr { get; set; }

    public long? Kostenstelle { get; set; }

    public double? ArtPalettengewicht { get; set; }

    public string? ArtText { get; set; }

    public string? Zutaten { get; set; }

    public decimal? ArtPakGewicht { get; set; }

    public bool? ArtKtoZug { get; set; }

    public bool? Send { get; set; }

    public bool? ArtSperr { get; set; }

    public string? EnthEan { get; set; }

    public string? KonsEan { get; set; }

    public bool? Palette { get; set; }

    public short? PalNr { get; set; }

    public bool? DisplayArtikel { get; set; }

    public int? RezNr { get; set; }

    public DateTime? ArtLetzteÄnderung { get; set; }

    public string? DruckFormSack { get; set; }

    public string? DruckForm { get; set; }

    public string? LieferantIln { get; set; }

    public string? SysUser { get; set; }

    public DateTime? SysTime { get; set; }

    public long Id { get; set; }

    public string? Bemerkung { get; set; }

    public long? MindBestandMge { get; set; }

    public long? RezNrAbsMasch { get; set; }

    public long? RezNrStretcher { get; set; }

    public long? RezNrPalettierer { get; set; }

    public long? ArtikelIdent { get; set; }

    public decimal? N { get; set; }

    public decimal? P { get; set; }

    public decimal? P2o5 { get; set; }

    public decimal? K { get; set; }

    public decimal? ArtEinhVerp1 { get; set; }

    public decimal? ArtEinhVp { get; set; }

    public bool? EtDruck { get; set; }

    public bool? SchwerGetr { get; set; } 
}