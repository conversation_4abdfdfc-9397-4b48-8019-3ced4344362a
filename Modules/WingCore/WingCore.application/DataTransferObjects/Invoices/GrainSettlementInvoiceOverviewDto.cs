using WingCore.domain.Common;
using WingCore.domain.Models;

namespace WingCore.application.DataTransferObjects.Invoices;

public record GrainSettlementInvoiceOverviewDto : BaseInvoiceOverviewEntryDto
{
    public new IEnumerable<GrainAndRapeSettlementInvoicePostingDto> ListPostings { get; set; } = [];
    public new IEnumerable<GrainAndRapeSettlementInvoicePostingDto> ListAllPostings { get; set; } = [];
    public virtual bool Equals(GrainSettlementInvoiceOverviewDto? obj)
    {
        return Id == obj?.Id;
    } 
    
    public override int GetHashCode()
    {
        return HashCode.Combine(Id, Type);
    }
    
    private void InitBase(Gbvadr invoiceHeaderData)
    {
        Type = InvoiceType.GrainSettlementInvoice;
        Id = invoiceHeaderData.Id;
        InvoiceNumber = invoiceHeaderData.Gbnr;
        InvoiceDate = invoiceHeaderData.Gbdatum ?? DateTime.MinValue;
        ValutaDate = invoiceHeaderData.Vdatum ?? DateTime.MinValue;
        DueDays = 0;
        OrderNumber = string.Empty;
        TotalGross = invoiceHeaderData.Gbbrutto ?? 0;
        TotalNet = invoiceHeaderData.Gbnetto ?? 0;
        TotalVatAmount = invoiceHeaderData.Gbmwst ?? 0;
        IsCancellationInvoice = invoiceHeaderData.IsCancellationInvoice;
        WasCanceled = invoiceHeaderData.WasCanceled;
        CancelOrCreditNoteInvoiceNumber = invoiceHeaderData.Gbaltnr ?? 0;
        SysUser = invoiceHeaderData.SysUser ?? string.Empty;
        SysTime = invoiceHeaderData.SysTime;
        Fibu2 = invoiceHeaderData.Gbfibu2 ?? false;
        InvoiceSupplierCurrent = invoiceHeaderData.InvoiceSupplierCurrentData;
        InvoiceSupplier = new InvoiceSupplierOrHolderDto(
            invoiceHeaderData.Gblfnr,
            invoiceHeaderData.Gbsbg ?? string.Empty,
            invoiceHeaderData.Gbanrede ?? string.Empty,
            invoiceHeaderData.Gbname1,
            invoiceHeaderData.Gbname2,
            invoiceHeaderData.Gbname3,
             invoiceHeaderData.Gbstrasse ?? string.Empty,
             invoiceHeaderData.Gbplz ?? string.Empty,
             invoiceHeaderData.Gbort ?? string.Empty,
            invoiceHeaderData.Gbland ?? string.Empty,
            invoiceHeaderData.Gbustidnr ?? string.Empty,
            invoiceHeaderData.IsInvoiceHolderForeigner,
            invoiceHeaderData.Gbtelefon ?? string.Empty,
            InvoiceSupplierCurrent?.Email ?? string.Empty,
            InvoiceSupplierCurrent?.Kdleh ?? string.Empty,
            InvoiceSupplierCurrent?.DueDays ?? 0,
            invoiceHeaderData.Gblfnr.ToString() ?? string.Empty
            );
        GoodsRecipientsCurrent = null;
        GoodsRecipients = null;
        ListTax = 
        [
            new TaxDto(invoiceHeaderData.GbstSchl ?? 0, invoiceHeaderData.Gbmwst ?? 0, invoiceHeaderData.GbstProz ?? 0, invoiceHeaderData.Gbnetto ?? 0,  invoiceHeaderData.Gbnetto ?? 0 + invoiceHeaderData.Gbmwst ?? 0),
        ];
        BankInfo = new BankInfoDto(invoiceHeaderData.Gbbank ?? string.Empty,
                                   invoiceHeaderData.Gbblz ?? string.Empty,
                                   invoiceHeaderData.Gbkonto ?? string.Empty,
                                   invoiceHeaderData.Gbiban ?? string.Empty,
                                   invoiceHeaderData.Gbswift ?? string.Empty);
        ListPostings = invoiceHeaderData.Positionen.Where(x=> x.ErfSchema?.IsPo ?? true).Select(x => GrainAndRapeSettlementInvoicePostingDto.Create(x, invoiceHeaderData));
        ListAllPostings = invoiceHeaderData.Positionen.Select(x => GrainAndRapeSettlementInvoicePostingDto.Create(x, invoiceHeaderData));
    }

    public static implicit operator GrainSettlementInvoiceOverviewDto(Gbvadr invoiceHeaderData)
    {
        var newObj = new GrainSettlementInvoiceOverviewDto();
        newObj.InitBase(invoiceHeaderData);
        return newObj;
    }
}