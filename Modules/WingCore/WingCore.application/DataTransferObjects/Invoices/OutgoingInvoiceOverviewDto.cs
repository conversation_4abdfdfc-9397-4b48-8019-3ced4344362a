using WingCore.domain.Common;
using WingCore.domain.Models;

namespace WingCore.application.DataTransferObjects.Invoices;

public record OutgoingInvoiceOverviewDto : BaseInvoiceOverviewEntryDto
{
    public new IEnumerable<OutgoingInvoicePostingDto> ListPostings { get; set; } = [];
    public new IEnumerable<OutgoingInvoicePostingDto> ListAllPostings { get; set; } = [];
    public virtual bool Equals(OutgoingInvoiceOverviewDto? obj)
    {
        return Id == obj?.Id;
    }
    public override int GetHashCode()
    {
        return HashCode.Combine(Id, Type);
    }
    
    private void InitBase(Aradressdaten aadressdaten)
    {
        Type = InvoiceType.OutgoingInvoice;
        Id = aadressdaten.Id;
        InvoiceNumber = aadressdaten.Arnummer ?? 0;
        InvoiceDate = aadressdaten.Ardatum ?? DateTime.MinValue;
        ValutaDate = aadressdaten.Vdatum ?? DateTime.MinValue;
        DueDays = aadressdaten.ArnettoTage ?? 0;
        OrderNumber = aadressdaten.ArbestellNr ?? string.Empty;
        TotalGross = aadressdaten.ArgesBrutto ?? 0;
        TotalNet = aadressdaten.ArgesNetto ?? 0;
        TotalVatAmount = aadressdaten.ArgesMwst ?? 0;
        IsCancellationInvoice = aadressdaten.IsCancellationInvoice;
        WasCanceled = aadressdaten.WasCanceled;
        CancelOrCreditNoteInvoiceNumber = aadressdaten.AraltNr ?? 0;
        SysUser = aadressdaten.SysUser ?? string.Empty;
        SysTime = aadressdaten.SysTime;
        InvoiceSupplier = null;
        InvoiceSupplierCurrent = aadressdaten.InvoiceHolderCustomerData;
        GoodsRecipientsCurrent = aadressdaten.InvoiceHolderCustomerData;
        Fibu2 = aadressdaten.Arfibu2 ?? false;
        AdditionalLinesForPrint = aadressdaten.ArzhlDruck ?? string.Empty;
        AdditionalLinesForPrint2 = aadressdaten.ArzhlDruck02 ?? string.Empty;
        AdditionalLinesForPrint3 = aadressdaten.ArzhlDruck03 ?? string.Empty;;
        RepresentativeNumber = aadressdaten.ArvertrNr ?? 0;
        RepresentativeName = aadressdaten.ArvertrName ?? "";
        Flags = aadressdaten.FlagsWorker;
        GblNr = aadressdaten.Argblnr ?? string.Empty;
        FruehBezugKennung = aadressdaten.ArfbzgKng ?? string.Empty;
        WertGut = aadressdaten.WertGut;
        LkwInformation = aadressdaten.Arlkw;
        CreationUser = aadressdaten.Arbearbeiter;
        InvoiceHolder = new InvoiceSupplierOrHolderDto(
            aadressdaten.ArkdnrRe,
            aadressdaten.Arkdsbgre ?? string.Empty,
            aadressdaten.AranredeRe ?? string.Empty,
            aadressdaten.Arname1Re,
            aadressdaten.Arname2Re,
            aadressdaten.Arname3Re,
             aadressdaten.ArstrasseRe ?? string.Empty,
             aadressdaten.Arplzre ?? string.Empty,
            aadressdaten.ArortRe ?? string.Empty,
            aadressdaten.ArlandRe ?? string.Empty,
            aadressdaten.ArustIdNrRe ?? string.Empty,
            aadressdaten.IsInvoiceHolderForeigner,
            InvoiceSupplierCurrent.Phone ?? string.Empty,
            InvoiceSupplierCurrent.Email ?? string.Empty,
            InvoiceSupplierCurrent.Kdleh ?? string.Empty,
            InvoiceSupplierCurrent.DueDays,
            InvoiceSupplierCurrent.LfNr
            );
        GoodsRecipients = new InvoiceSupplierOrHolderDto(
            aadressdaten.ArkdnrWe,
            aadressdaten.Arkdsbgwe ?? string.Empty,
            aadressdaten.AranredeWe ?? string.Empty,
            aadressdaten.Arname1We,
            aadressdaten.Arname2We,
            aadressdaten.Arname3We,
            aadressdaten.ArstrasseWe ?? string.Empty,
            aadressdaten.Arplzwe ?? string.Empty,
            aadressdaten.ArortWe ?? string.Empty,
            aadressdaten.ArlandWe ?? string.Empty,
            string.Empty,
            !aadressdaten.ArlandWe?.Equals("Deutschland") ?? false,
            GoodsRecipientsCurrent.Phone ?? string.Empty,
            GoodsRecipientsCurrent.Email ?? string.Empty,
            GoodsRecipientsCurrent.Kdleh ?? string.Empty,
            GoodsRecipientsCurrent.DueDays,
            aadressdaten.ArlfnrWe ?? string.Empty
        );

        InvoiceHolder.EInvoiceEmail = aadressdaten.ReEInvoiceMail;
        InvoiceHolder.PaymentTerm = aadressdaten.ArzahlZiel;
        
        InvoiceHolderCurrent = aadressdaten.InvoiceHolderCustomerData;
        
        ListTax =
        [
            new TaxDto(0, aadressdaten.Armwst1 ?? 0, aadressdaten.ArmwSt1Proz ?? 0, aadressdaten.Arnetto1 ?? 0, aadressdaten.ArgesBrutto1 ?? 0),
            new TaxDto(1, aadressdaten.Armwst2 ?? 0, aadressdaten.ArmwSt2Proz ?? 0, aadressdaten.Arnetto2 ?? 0, aadressdaten.ArgesBrutto2 ?? 0),
            new TaxDto(2, aadressdaten.Armwst3 ?? 0, aadressdaten.ArmwSt3Proz ?? 0, aadressdaten.Arnetto3 ?? 0, aadressdaten.ArgesBrutto3 ?? 0)
        ];
        BankInfo = new BankInfoDto(aadressdaten.Arbank ?? string.Empty,
                                   aadressdaten.Arblz ?? string.Empty,
                                   aadressdaten.ArkontoNr ?? string.Empty,
                                   string.Empty,
                                   string.Empty);
        ListPostings = aadressdaten.Positionen.Where(x=> x.ErfSchema?.IsPo ?? true).Select(x => (OutgoingInvoicePostingDto)x);
        ListAllPostings = aadressdaten.Positionen.Select(x => (OutgoingInvoicePostingDto)x);
    }
    
    public static implicit operator OutgoingInvoiceOverviewDto(Aradressdaten aadressdaten)
    {
        var newObj = new OutgoingInvoiceOverviewDto();
        newObj.InitBase(aadressdaten);
        
        return newObj;
    }
    
    // public static explicit operator OutgoingInvoiceOverviewDto(Aradressdaten aadressdaten)
    // {
    //     var newObj = new OutgoingInvoiceOverviewDto();
    //     newObj.InitBase(aadressdaten);
    //     
    //     return newObj;
    // }
}
