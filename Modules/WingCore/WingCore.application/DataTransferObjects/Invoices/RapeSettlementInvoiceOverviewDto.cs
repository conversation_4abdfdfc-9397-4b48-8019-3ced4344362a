using WingCore.domain.Common;
using WingCore.domain.Models;

namespace WingCore.application.DataTransferObjects.Invoices;

public record RapeSettlementInvoiceOverviewDto : BaseInvoiceOverviewEntryDto
{
    public new IEnumerable<GrainAndRapeSettlementInvoicePostingDto> ListPostings { get; set; } = [];
    public new IEnumerable<GrainAndRapeSettlementInvoicePostingDto> ListAllPostings { get; set; } = [];
    public virtual bool Equals(GrainAndRapeSettlementInvoicePostingDto? obj)
    {
        return Id == obj?.Id;
    }
    
    private void InitBase(Rgbvadr invoiceHeaderData)
    {
        Type = InvoiceType.RapeSettlementInvoice;
        Id = invoiceHeaderData.Id;
        InvoiceNumber = invoiceHeaderData.Gbnr;
        InvoiceDate = invoiceHeaderData.Gbdatum ?? DateTime.MinValue;
        ValutaDate = invoiceHeaderData.Vdatum ?? DateTime.MinValue;
        DueDays = 0;
        OrderNumber = string.Empty;
        TotalGross = invoiceHeaderData.Gbbrutto ?? 0;
        TotalNet = invoiceHeaderData.Gbnetto ?? 0;
        TotalVatAmount = invoiceHeaderData.Gbmwst ?? 0;
        IsCancellationInvoice = invoiceHeaderData.IsCancellationInvoice;
        WasCanceled = invoiceHeaderData.WasCanceled;
        CancelOrCreditNoteInvoiceNumber = invoiceHeaderData.Gbaltnr ?? 0;
        SysUser = invoiceHeaderData.SysUser ?? string.Empty;
        SysTime = invoiceHeaderData.SysTime;
        InvoiceSupplierCurrent = invoiceHeaderData.InvoiceSupplierCurrentData;
        Fibu2 = invoiceHeaderData.Gbfibu2 ?? false;
        InvoiceSupplier = new InvoiceSupplierOrHolderDto(
            invoiceHeaderData.Gblfnr,
            invoiceHeaderData.Gbsbg ?? string.Empty,
            invoiceHeaderData.Gbanrede ?? string.Empty,
            invoiceHeaderData.Gbname1,
            invoiceHeaderData.Gbname2,
            invoiceHeaderData.Gbname3,
             invoiceHeaderData.Gbstrasse ?? string.Empty,
             invoiceHeaderData.Gbplz ?? string.Empty,
             invoiceHeaderData.Gbort ?? string.Empty,
            invoiceHeaderData.Gbland ?? string.Empty,
            invoiceHeaderData.Gbustidnr ?? string.Empty,
            invoiceHeaderData.IsInvoiceHolderForeigner,
            InvoiceSupplierCurrent?.Phone ?? string.Empty,
            InvoiceSupplierCurrent?.Email ?? string.Empty,
            InvoiceSupplierCurrent?.Kdleh ?? string.Empty,
            InvoiceSupplierCurrent?.DueDays ?? 0,
        invoiceHeaderData.Gblfnr.ToString() ?? string.Empty
            );
        GoodsRecipientsCurrent = null;
        GoodsRecipients = null;
        ListTax = [];
        BankInfo = new BankInfoDto(invoiceHeaderData.Gbbank ?? string.Empty,
                                   invoiceHeaderData.Gbblz ?? string.Empty,
                                   invoiceHeaderData.Gbkonto ?? string.Empty,
                                   invoiceHeaderData.Gbiban ?? string.Empty,
                                   invoiceHeaderData.Gbswift ?? string.Empty);
        ListPostings = invoiceHeaderData.Positionen.Where(x=> x.GbartNr!=0).Select(x => GrainAndRapeSettlementInvoicePostingDto.Create(x, invoiceHeaderData));
        ListAllPostings = invoiceHeaderData.Positionen.Select(x => GrainAndRapeSettlementInvoicePostingDto.Create(x, invoiceHeaderData));
    }
    
    public static implicit operator RapeSettlementInvoiceOverviewDto(Rgbvadr invoiceHeaderData)
    {
        var newObj = new RapeSettlementInvoiceOverviewDto();
        newObj.InitBase(invoiceHeaderData);
        return newObj;
    }
}