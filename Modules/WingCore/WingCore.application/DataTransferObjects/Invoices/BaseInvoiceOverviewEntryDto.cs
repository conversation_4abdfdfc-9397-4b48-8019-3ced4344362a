using WingCore.domain.Common;
using WingCore.domain.Common.CustomerAndSuppliers;
using WingCore.domain.Common.Flags;
using WingCore.domain.Models;
using WingCore.domain.Models.StammDbModels;

namespace WingCore.application.DataTransferObjects.Invoices;

public abstract record BaseInvoiceOverviewEntryDto
{
    public InvoiceType Type { get; set; } = InvoiceType.Empty;
    public string TypeAsString => Type.ToString();
    public long Id { get; set; }
    public long InvoiceNumber { get; set; }
    public DateTime InvoiceDate { get; set; }
    public DateTime ValutaDate { get; set; }
    public decimal TotalGross { get; set; }
    public decimal TotalNet { get; set; }
    public decimal TotalVatAmount { get; set; }
    public bool IsCancellationInvoice { get; set; }
    public bool WasCanceled { get; set; }
    public long CancelOrCreditNoteInvoiceNumber { get; set; }
    public long DueDays  { get; set; }
    public string OrderNumber  { get; set; } = string.Empty;
    public bool Fibu2  { get; set; }
    public string AdditionalLinesForPrint  { get; set; } = string.Empty;
    public string AdditionalLinesForPrint2  { get; set; } = string.Empty;
    public string AdditionalLinesForPrint3 { get; set; } = string.Empty;
    public long RepresentativeNumber { get; set; }
    public string? RepresentativeName { get; set; }
    public InvoiceFlagsWorker? Flags { get; set; }
    public string? GblNr { get; set; }
    public string? FruehBezugKennung { get; set; }
    public bool? WertGut { get; set; }
    public string? LkwInformation { get; set; }
    public string? CreationUser { get; set; }
    public InvoiceSupplierOrHolderDto? InvoiceHolder { get; set; }
    public InvoiceSupplierOrHolderDto? InvoiceHolderCurrent { get; set; }
    public InvoiceSupplierOrHolderDto? InvoiceSupplier { get; set; }
    public InvoiceSupplierOrHolderDto? InvoiceSupplierCurrent { get; set; }
    public InvoiceSupplierOrHolderDto? GoodsRecipients  { get; set; }
    public InvoiceSupplierOrHolderDto? GoodsRecipientsCurrent  { get; set; }
    public List<TaxDto> ListTax { get; set; } = [];
    public BankInfoDto? BankInfo { get; set; }
    
    public string SysUser { get; set; } = string.Empty;
    public DateTime? SysTime { get; set; }
        
    public string ExternalInvoiceNumber { get; set; } = string.Empty;
    public IEnumerable<BaseInvoicePostingDto> ListPostings { get; set; } = [];
    public IEnumerable<BaseInvoicePostingDto> ListAllPostings { get; set; } = [];
    
    public virtual bool Equals(BaseInvoiceOverviewEntryDto? obj)
    {
        return Id == obj?.Id && Type.Value == obj.Type.Value;
    }
    
    public override int GetHashCode()
    {
        return HashCode.Combine(Id, Type);
    }
}

public record InvoiceSupplierOrHolderDto
    : BaseCustomerOrSupplierData
{
    
    public InvoiceSupplierOrHolderDto
    (
        long? Number,
        string Sbg,
        string Anrede,
        string? Name,
        string? Name2,
        string? Name3,
        string Street,
        string Plz,
        string Ort,
        string Country,
        string UstIdNr,
        bool IsForeigner,
        string Phone,
        string Email,
        string Kdleh,
        long DueDays,
        string LfNr)
    : base(Number, Sbg, Anrede, Name, Name2, Name3, Street, Plz, Ort, Country, UstIdNr, IsForeigner, Phone, Email, Kdleh, DueDays,LfNr)
    {}
    
    public InvoiceSupplierOrHolderDto(Mandant mandant) :base (mandant){}
    public InvoiceSupplierOrHolderDto(Kunden kunden) :base (kunden){}
    public InvoiceSupplierOrHolderDto(Lieferanten lieferanten) :base (lieferanten){}
    public InvoiceSupplierOrHolderDto(CompanyMasterData companyMasterData) :base (companyMasterData){}
    
    public static implicit operator InvoiceSupplierOrHolderDto(Mandant mandant) => new(mandant);
    public static implicit operator InvoiceSupplierOrHolderDto(Kunden kunden) => new(kunden);
    public static implicit operator InvoiceSupplierOrHolderDto(Lieferanten lieferanten) => new(lieferanten);
    public static implicit operator InvoiceSupplierOrHolderDto(CompanyMasterData companyMasterData) => new(companyMasterData);
}

public record TaxKeyDto(short VatId, decimal Vat);

public record TaxDto(
    short VatId,
    decimal VatAmount,
    decimal Vat,
    decimal NetAmount,
    decimal GrossAmount
    )
{
    public static TaxDto Empty = new TaxDto(-1, 0, 0, 0,0);

    public bool IsAllAmountZero => VatAmount is decimal.Zero && NetAmount is decimal.Zero;
    public TaxKeyDto CompressKey => new TaxKeyDto(VatId, Vat);

    public static TaxDto operator +(TaxDto a, TaxDto b)
    {
        if (b.Vat != a.Vat)
            return a with {};

        return a with { VatAmount = a.VatAmount + b.VatAmount,
                        NetAmount = a.NetAmount + b.NetAmount };
    }
}

public record BankInfoDto(
    string BankName,
    string Blz,
    string KontoNumber,
    string Iban,
    string Swift);