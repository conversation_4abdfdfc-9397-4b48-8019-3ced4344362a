using MediatR;
using WingCore.application.ApiDtos.OrderData;
using WingCore.application.ApiDtos.OrderData.Create;
using WingCore.application.ApiDtos.OrderData.Put;

namespace WingCore.application.Api;

public record UpdateOderLineCommand(OrderLinePutDtoV1 OrderLineDtoV1) : IRequest;
public record UpdateOrderHeaderCommand(OrderHeaderPutDtoV1 OrderHeaderDtoV1) : IRequest;
public record CreateOrderHeaderCommand(OrderHeaderCreateDtoV1 OrderHeaderCreate) : IRequest<long>;