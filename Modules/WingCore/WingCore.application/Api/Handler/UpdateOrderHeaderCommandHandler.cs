using MediatR;
using WingCore.application.ApiDtos.OrderData;
using WingCore.application.Contract.IModels;
using WingCore.domain.Models;

namespace WingCore.application.Api.Handler;

public class UpdateOrderHeaderCommandHandler(IAuftragskopfRepository repository) : IRequestHandler<UpdateOrderHeaderCommand>
{
    public async Task Handle(UpdateOrderHeaderCommand request, CancellationToken cancellationToken)
    {
        var auftragsKopf = (await repository.GetAuftragskopfByOrderNumber(request.OrderHeaderDtoV1.OrderNumber, false) ?? []).FirstOrDefault();
        if(auftragsKopf is null)
            throw new Exception("OrderHeader not found");
        switch (request.OrderHeaderDtoV1.Status)
        {
            case OrderHeaderStatusV1.DeletedString:
                auftragsKopf.LoeschKng = true;
                break;
            case OrderHeaderStatusV1.DeliveryNoteString: 
                auftragsKopf.Lieferschein = true;
                auftragsKopf.Auftrag = false;
                break;
            case OrderHeaderStatusV1.OrderString:
                auftragsKopf.Auftrag = true;
                auftragsKopf.Lieferschein = false;
                break;
            default:
                throw new Exception("Status does not correspond to any of the specified values: Deleted, Deliverynote, Order");
        };
        await repository.UpdateWithoutTracking(auftragsKopf, "");
    }
}