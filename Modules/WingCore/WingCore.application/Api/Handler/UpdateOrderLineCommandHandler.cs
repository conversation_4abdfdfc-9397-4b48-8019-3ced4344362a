using MediatR;
using WingCore.application.Contract.IModels;

namespace WingCore.application.Api.Handler;

public class UpdateOrderLineCommandHandler(IAuftragsposRepository repository) : IRequestHandler<UpdateOderLineCommand>
{
    public async Task Handle(UpdateOderLineCommand request, CancellationToken cancellationToken)
    {
        var auftragspo = await repository.GetAuftragsposById(false, request.OrderLineDtoV1.Id);
        if(auftragspo is null)
            throw new Exception("OrderLine not found");
        auftragspo.Status = request.OrderLineDtoV1.Status;
        auftragspo.GesMenge = request.OrderLineDtoV1.QuantityLoaded;
        repository.Update(auftragspo);
        await repository.SaveChangesAsync();
    }
}