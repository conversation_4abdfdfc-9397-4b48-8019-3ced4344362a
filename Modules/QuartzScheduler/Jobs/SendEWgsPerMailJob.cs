using MediatR;
using Quartz;
using Microsoft.Extensions.Logging;
using ObjectDeAndSerialize;
using QuartzScheduler.Jobs.BaseJobs;
using WingCore.application.Wiegeschein;
using wingPrinterListLabel.application.Wgs;
using wingPrinterListLabel.application.Wgs.Helpers;

namespace QuartzScheduler.Jobs;

[DisallowConcurrentExecution]
[PersistJobDataAfterExecution]
public class SendEWgsPerMailJob(ILogger<SendEWgsPerMailJob> logger, ISender mediatr) : BaseJob(logger)
{
    protected override async Task<string> ExecuteJob(IJobExecutionContext context)
    {
        List<LogInfoForEWgs> logInformations = [];

        try
        {
            // Use the BulkWgsSendEmailAutomaticCommand which handles everything
            var result = await mediatr.Send(new BulkWgsSendEmailAutomaticCommand(""));

            // Convert each result to the expected log format
            foreach (var wgsResult in result.Results)
            {
                var logInformation = new LogInfoForEWgs
                {
                    WgsNumber = wgsResult.WgsNumber,
                    Success = wgsResult.Success,
                    Email = wgsResult.Email,
                    ErrMsg = wgsResult.ErrorMessage
                };

                logInformations.Add(logInformation);
            }

            // If no individual results, add a summary entry
            if (!result.Results.Any())
            {
                var summaryLog = new LogInfoForEWgs
                {
                    WgsNumber = 0, // Use 0 to indicate this is a summary entry
                    Success = true,
                    Email = $"No WGS records found to process.",
                    ErrMsg = null
                };

                logInformations.Add(summaryLog);
            }

            // Add global error message if present (e.g., missing EN configuration)
            if (!string.IsNullOrWhiteSpace(result.GlobalErrorMessage))
            {
                var globalErrorLog = new LogInfoForEWgs
                {
                    WgsNumber = 0, // Use 0 to indicate this is a global error entry
                    Success = false,
                    Email = "Configuration Error",
                    ErrMsg = result.GlobalErrorMessage
                };

                logInformations.Add(globalErrorLog);
                logger.LogError("Global configuration error: {ErrorMessage}", result.GlobalErrorMessage);
            }
        }
        catch (Exception e)
        {
            var logInformation = new LogInfoForEWgs
            {
                WgsNumber = 0, // Use 0 to indicate this is a summary entry
                Success = false,
                Email = string.Empty,
                ErrMsg = e.InnerException?.Message ?? e.Message
            };

            logInformations.Add(logInformation);
        }

        return logInformations.SerializeToJson();
    }
}
