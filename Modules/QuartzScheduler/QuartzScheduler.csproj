<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="Quartz" Version="3.15.0" />
      <PackageReference Include="Quartz.Extensions.DependencyInjection" Version="3.15.0" />
      <PackageReference Include="Quartz.Extensions.Hosting" Version="3.15.0" />
      <PackageReference Include="Quartz.Serialization.Json" Version="3.15.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\libs\ObjectDeAndSerialize\ObjectDeAndSerialize.csproj" />
      <ProjectReference Include="..\WingCore\WingCore.application\WingCore.application.csproj" />
      <ProjectReference Include="..\WingLager\wingLager.application\wingLager.application.csproj" />
      <ProjectReference Include="..\WingPrinterListLabel\wingPrinterListLabel.application\wingPrinterListLabel.application.csproj" />
    </ItemGroup>
    
    <ItemGroup>
        <Reference Include="PrinterService">
            <HintPath>..\..\PrinterService\PublishDlls\PrinterService.dll</HintPath>
            <Private>true</Private>
        </Reference>
        <Reference Include="combit.ListLabel30">
            <HintPath>..\..\PrinterService\PublishDlls\combit.ListLabel30.dll</HintPath>
            <Private>true</Private>
        </Reference>
    </ItemGroup>

</Project>
