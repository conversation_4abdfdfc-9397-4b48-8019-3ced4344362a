using Microsoft.Extensions.Configuration;
using Quartz;
using Quartz.Impl;
using Quartz.Impl.Triggers;
using QuartzScheduler.Jobs;

namespace QuartzScheduler;

public class SeedData(ISchedulerFactory schedulerFactory)
{
    public static string KeyJobDataObjDataAsString => "objDataAsString";
    public static string KeyLog => "logs";
    
    public async Task<bool> CheckAndCreateJobsData(string jobName,
                                                    string groupName,
                                                    string serverName,
                                                    TimeSpan timeIntervalInSeconds,
                                                    string jobClasseName)
    {
        return await CheckAndCreateJobsData(jobName, groupName, serverName, timeIntervalInSeconds, jobClasseName, string.Empty);
    }
    public async Task<bool> CheckAndCreateJobsData(string jobName,
                                                   string groupName,
                                                   string serverName,
                                                   TimeSpan timeIntervalInSeconds,
                                                   string jobClasseName,
                                                   string objDataAsString)
    {
        var scheduler = await schedulerFactory.GetScheduler();
        if(!scheduler.IsStarted)
            await scheduler.Start();
        
        groupName = groupName.Replace(" ", "_");
        
        if(!string.IsNullOrWhiteSpace(serverName))
            groupName = $"{serverName}_{groupName}";

        var jobKey = new JobKey(jobName, groupName);
        var triggerKey = new TriggerKey($"{jobName}Trigger", groupName);

        // check job exists
        var check = await scheduler.CheckExists(jobKey);
        if (check)
            return false;

        var result = PossibleJobs.TryGetValue(jobClasseName, out var type);
        if (!result || type is null)
        {
            return false;
        }

        var job = JobBuilder.Create(type)
        .WithIdentity(jobKey)
        .Build();

        // Trigger the job to run now, and then every 60 seconds
        var trigger = TriggerBuilder.Create()
                                    .WithIdentity(triggerKey)
                                    .StartNow()
                                    .UsingJobData(KeyJobDataObjDataAsString,objDataAsString)
                                    .WithSimpleSchedule(x => 
                                        x.WithInterval(timeIntervalInSeconds)
                                         .RepeatForever())
                                    .Build();

        //adds also db
        await scheduler.ScheduleJob(job, trigger);
        return true;
    }
    
    public async Task ExecuteNow(JobDetailImpl jobDetailImpl,
                                CancellationToken cancellationToken = default)
    {
        var scheduler = await schedulerFactory.GetScheduler(cancellationToken);
        if(!(await scheduler.CheckExists(jobDetailImpl.Key, cancellationToken)))
            return;
        
        var jobDetail = await scheduler.GetJobDetail(jobDetailImpl.Key, cancellationToken);
        if (jobDetail is null)
            return;
        
        IReadOnlyCollection<ITrigger?> triggers = await scheduler.GetTriggersOfJob(jobDetailImpl.Key, cancellationToken);
        var actualTrigger = triggers.FirstOrDefault();
        if (actualTrigger is not SimpleTriggerImpl oldTrigger)
            return;
        
        var newTrigger = (SimpleTriggerImpl)TriggerBuilder.Create()
            .WithIdentity(oldTrigger.Key)
            .StartAt(oldTrigger.StartTimeUtc)
            .UsingJobData(oldTrigger.JobDataMap)
            .WithSimpleSchedule(x =>
                x.WithInterval(oldTrigger.RepeatInterval)
                    .RepeatForever())
            .ForJob(jobDetail)
            .Build();
        newTrigger.TimesTriggered = oldTrigger.TimesTriggered;
        newTrigger.JobGroup = oldTrigger.JobGroup;
        newTrigger.JobName = oldTrigger.JobName;
        newTrigger.FireInstanceId = oldTrigger.FireInstanceId;
        newTrigger.SetNextFireTimeUtc(DateTimeOffset.UtcNow.AddSeconds(1));

        await scheduler.RescheduleJob(oldTrigger.Key,newTrigger, cancellationToken);
    }
    
    public static string JobsNameExportDatevJob => "ExportDatevJob";
    public static string JobsNameAlivenessCheckJob => "AlivenessCheckJob";
    public static string JobsNamePrintProcessOrderPdfJob => "PrintProcessOrderPdfJob";
    public static string JobsNameSendEInvoicePerMailJob => "SendEInvoicePerMailJob";
    public static string JobsNameSendEWgsPerMailJob => "SendEWgsPerMailJob";
    public static string JobsNamePaletTransferQueueJob => "LindeFahrerlosTransportSystemJob";
    public static string JobsSendInvoiceToEasiAdfinityRestApiJob => "SendInvoiceToEasiAdfinityRestApiJob";
    public static string JobsSendWgsToEasiAdfinityRestApiJob => "SendWgsToEasiAdfinityRestApiJob";
    
    public Dictionary<string, Type> PossibleJobs = new Dictionary<string, Type>
    {
        { JobsNameExportDatevJob, typeof(ExportDatevJob) },
        { JobsNamePrintProcessOrderPdfJob, typeof(PrintProcessOrderPdfJob) },
        { JobsNameSendEInvoicePerMailJob, typeof(SendEInvoicePerMailJob) },
        { JobsNameSendEWgsPerMailJob, typeof(SendEWgsPerMailJob) },
        { JobsNamePaletTransferQueueJob, typeof(PaletTransferQueueJob) },
        { JobsSendInvoiceToEasiAdfinityRestApiJob, typeof(SendInvoiceToEasiAdfinityRestApiJob) },
        { JobsSendWgsToEasiAdfinityRestApiJob, typeof(SendWgsToEasiAdfinityRestApiJob) }
        // Fügen Sie hier weitere Jobs hinzu...
    };

    public bool ExistJobInSystem(string groupName)
    {
        if (string.IsNullOrWhiteSpace(groupName))
            return false;

        var idx = groupName.IndexOf('_');
        var pureJobName = idx >= 0 ? groupName[(idx + 1)..] : groupName;

        return PossibleJobs.ContainsKey(pureJobName);
    }
}
