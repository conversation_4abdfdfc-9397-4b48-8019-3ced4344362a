<Project Sdk="Microsoft.NET.Sdk.Web">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <Nullable>enable</Nullable>
        <ImplicitUsings>enable</ImplicitUsings>
        <UserSecretsId>aspnet-WingNg.LicenseManager-79085C7B-D43B-463F-A4A8-4FE8820653BA</UserSecretsId>
    </PropertyGroup>

    <ItemGroup>
        <PackageReference Include="Microsoft.AspNetCore.Diagnostics.EntityFrameworkCore" Version="9.0.8" />
        <PackageReference Include="Microsoft.AspNetCore.Identity.EntityFrameworkCore" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Sqlite" Version="9.0.8" />
        <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.8">
          <PrivateAssets>all</PrivateAssets>
          <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
        </PackageReference>
        <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="9.0.8" />
        <PackageReference Include="Microsoft.Extensions.Hosting" Version="9.0.8" />
        <PackageReference Include="Microsoft.Extensions.Hosting.WindowsServices" Version="9.0.8" />
        <PackageReference Include="UUIDNext" Version="4.1.2" />
        <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components" Version="4.12.1" />
        <PackageReference Include="Microsoft.FluentUI.AspNetCore.Components.Icons" Version="4.12.1" />
    </ItemGroup>
     <ItemGroup>
      <ProjectReference Include="..\..\libs\ObjectDeAndSerialize\ObjectDeAndSerialize.csproj" />
      <ProjectReference Include="..\..\Modules\Licenses\licenses.ioc\licenses.ioc.csproj" />
    </ItemGroup>
    <ItemGroup>
      <None Update="Cert\cert.crt">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
      <None Update="Cert\cert.key">
        <CopyToOutputDirectory>Never</CopyToOutputDirectory>
      </None>
      <None Update="Cert\vas.software.pfx">
        <CopyToOutputDirectory>Always</CopyToOutputDirectory>
      </None>
    </ItemGroup>
</Project>
