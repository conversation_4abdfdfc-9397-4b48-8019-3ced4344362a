<RadzenStack class="rz-p-0 rz-p-lg-12" Orientation="Orientation.Vertical">
    <RadzenCard Style="width:100%">
        <RadzenHeading Size="H1" style="display: inline-block" Text="@HeaderText"/>
    </RadzenCard>
    <RadzenRow>
        <RadzenColumn Size="9" class="card">
            @LeftColumn
        </RadzenColumn>
        <RadzenColumn Size="3" class="card">
            @RightColumn
        </RadzenColumn>
    </RadzenRow>
</RadzenStack>

@code{
    [Parameter] public string HeaderText { get; set; } = string.Empty;
    
    [Parameter] public required RenderFragment LeftColumn { get; set; }
    [Parameter] public required RenderFragment RightColumn { get; set; }
}