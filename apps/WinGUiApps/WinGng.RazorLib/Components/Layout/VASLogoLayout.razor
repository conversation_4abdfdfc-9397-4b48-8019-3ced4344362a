@using System.Diagnostics.CodeAnalysis
<RadzenRow style="font-size: large; text-align: center;">
    @if (OnButtonClickGoToSettings.HasDelegate)
    {
        <RadzenButton ButtonType="ButtonType.Button"
                      Icon="settings"
                      Click="@OnButtonClickGoToSettings"
                      class="rz-mt-5"
                      Style="position: absolute; right: 2rem;"/>
    }
    <!-- Centered search box column using OffsetMD -->
    <RadzenColumn Size="9" SizeMD="4" OffsetMD="4">
        <RadzenCard class="rounded-card">
            <div class="search-box">
                <RadzenTextBox Placeholder="@SearchPlaceholderText"
                               Style="width: 100%"
                               @oninput="(e) => SearchTerm = e.Value?.ToString() ?? string.Empty"
                               @onkeydown="HandleKeyDown"/>
            </div>
        </RadzenCard>
    </RadzenColumn>
    <RadzenColumn Size="12">
        <RadzenStack Orientation="Orientation.Vertical"
                     JustifyContent="JustifyContent.Center"
                     AlignItems="AlignItems.Center">
            <div class="button-container">
                <!-- First row: first 2 buttons -->
                <div class="button-row">
                    <div class="grid-cell">
                        <!-- Light lavender button (12×12 rem) -->
                        <RadzenButton ButtonType="ButtonType.Button"
                                      Size="ButtonSize.Large"
                                      Icon="@GetIconFromMainModelButtons(MainButtons.TopLeft)"
                                      Text="@GetTextFromMainModelButtons(MainButtons.TopLeft)"
                                      Click="@GetEventFromMainModelButtons(MainButtons.TopLeft)"
                                      Disabled="!GetEventFromMainModelButtons(MainButtons.TopLeft).HasDelegate"
                                      Class="modern-button-1"/>
                    </div>
                    <div class="grid-cell">
                        <!-- Medium purple button (24×12 rem) -->
                        <RadzenButton ButtonType="ButtonType.Button"
                                      Size="ButtonSize.Large"
                                      Icon="@GetIconFromMainModelButtons(MainButtons.TopRight)"
                                      Text="@GetTextFromMainModelButtons(MainButtons.TopRight)"
                                      Click="@GetEventFromMainModelButtons(MainButtons.TopRight)"
                                      Disabled="!GetEventFromMainModelButtons(MainButtons.TopRight).HasDelegate"
                                      Class="modern-button-2"/>
                    </div>
                </div>

                <!-- Second row: next 2 buttons -->
                <div class="button-row">
                    <div class="grid-cell">
                        <!-- Dark purple button (24×24 rem) -->
                        <RadzenButton ButtonType="ButtonType.Button"
                                      Size="ButtonSize.Large"
                                      Icon="@GetIconFromMainModelButtons(MainButtons.BottomLeft)"
                                      Text="@GetTextFromMainModelButtons(MainButtons.BottomLeft)"
                                      Click="@GetEventFromMainModelButtons(MainButtons.BottomLeft)"
                                      Disabled="!GetEventFromMainModelButtons(MainButtons.BottomLeft).HasDelegate"
                                      Class="modern-button-3"/>
                    </div>
                    <div class="grid-cell">
                        <!-- Another medium purple button (12×24 rem) -->
                        <RadzenButton ButtonType="ButtonType.Button"
                                      Size="ButtonSize.Large"
                                      Icon="@GetIconFromMainModelButtons(MainButtons.BottomRight)"
                                      Text="@GetTextFromMainModelButtons(MainButtons.BottomRight)"
                                      Click="@GetEventFromMainModelButtons(MainButtons.BottomRight)"
                                      Disabled="!GetEventFromMainModelButtons(MainButtons.BottomRight).HasDelegate"
                                      Class="modern-button-4"/>
                    </div>
                </div>
            </div>
        </RadzenStack>
    </RadzenColumn>
</RadzenRow>

@code {
    public record ModelButton(string Icon, string Text, EventCallback<MouseEventArgs> OnButtonClick);
    [Parameter] public List<ModelButton> ButtonList { get; set; } = [];

    public enum MainButtons
    {
        TopLeft,
        TopRight,
        BottomLeft,
        BottomRight
    }
    
    private string SearchTerm { get; set; } = string.Empty;
    [Parameter] public string SearchPlaceholderText { get; set; } = string.Empty;
    
    [Parameter] public EventCallback OnButtonClickGoToSettings { get; set; }
    [Parameter] public EventCallback<string> OnKeyDown { get; set; }

    [SuppressMessage("ReSharper", "UnusedMember.Local")]
    private void HandleKeyDown(KeyboardEventArgs args)
    {
        if (args.Key.Equals("Enter", StringComparison.OrdinalIgnoreCase))
        {
            OnKeyDown.InvokeAsync(SearchTerm);
        }
    }

    private string GetIconFromMainModelButtons(MainButtons index) => (int)index < ButtonList.Count ? ButtonList[(int)index].Icon : string.Empty;
    private string GetTextFromMainModelButtons(MainButtons index) => (int)index < ButtonList.Count ? ButtonList[(int)index].Text : string.Empty;
    private EventCallback<MouseEventArgs> GetEventFromMainModelButtons(MainButtons index) => (int)index < ButtonList.Count ? ButtonList[(int)index].OnButtonClick : new EventCallback<MouseEventArgs>();
}