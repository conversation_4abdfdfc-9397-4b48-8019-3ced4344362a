@foreach (var section in Sections.Where(s => s.Visible))
{
    <RadzenRow AlignItems="AlignItems.Center" Class="@(IsFirstSection(section) ? null : "mt-2")">
        <RadzenColumn Size="10">
            <p>@(section.Name)</p>
        </RadzenColumn>
        <RadzenColumn Size="2" JustifyContent="JustifyContent.End">
            <RadzenButton
                Click=@(() => ToggleSection(section.Name))
                Icon="delete"
                Shade="Shade.Lighter"/>
        </RadzenColumn>
    </RadzenRow> 
        @GetSectionTemplateByIndex(section)
   
}
<RadzenRow Class="mt-2">
    @foreach (var section in Sections.Where(section => !section.Visible))
    {
        <RadzenButton
            Click=@(() => ToggleSection(section.Name))
            Text="@(section.Name)"
            Icon="add_circle"
            Variant="Variant.Text"
            ButtonStyle="ButtonStyle.Primary"/>
    }
</RadzenRow>

@code {
    [Parameter, EditorRequired] 
    public List<Section> Sections { get; set; } = [];

    [Parameter, EditorRequired] 
    public required List<RenderFragment<Section>> SectionTemplates { get; set; }
    
    private Task ToggleSection(string sectionName)
    {
        var sectionIndex = Sections.FindIndex(s => s.Name == sectionName);
        if (sectionIndex >= 0)
            Sections[sectionIndex] = Sections[sectionIndex].ToggleVisibility();
        
        return Task.CompletedTask;
    }

    private RenderFragment? GetSectionTemplateByIndex(Section section)
    {
        var sectionIndex = Sections.IndexOf(section);
        if (sectionIndex >= 0 && sectionIndex < SectionTemplates.Count)
            return SectionTemplates[sectionIndex](section);

        return null;
    }

    private bool IsFirstSection(Section section)
    {
        return Sections.IndexOf(section) == 0;
    }
}