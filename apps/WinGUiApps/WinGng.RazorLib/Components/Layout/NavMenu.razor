@* NavMenu.razor *@
@using Licencing

<CascadingAuthenticationState> @* Ensure this is present for AuthorizeView to work *@
    <RadzenPanelMenu DisplayStyle="@(SidebarExpanded ? MenuItemDisplayStyle.IconAndText : MenuItemDisplayStyle.Icon)" ShowArrow="false">
        @* Use inherit to take sidebar background color defined in CSS *@
        @* Removed inline style Style="background-color: #f1f1f1;" *@

        <AuthorizeView Policy="@LicenseClaimType.SuccessSigninValue">
            <NotAuthorized>
                <RadzenPanelMenuItem Icon="home" Path="home" Text="@Localizer["Home"]" Click="@CloseMenueBar"/>
            </NotAuthorized>
        </AuthorizeView>
        <AuthorizeView Policy="@LicenseClaimType.SuccessSigninValue">
            <Authorized Context="successSignin">
                <RadzenPanelMenuItem Icon="home" Path="home" Text="@Localizer["Home"]" Click="@CloseMenueBar"/>
                <RadzenPanelMenuItem Icon="manufacturing" Text="@Localizer["MasterData"]">
                    <NavMenuItem Policy="@LicenseClaimType.CustomerMasterData"
                                 Path="editCostumerMasterData" Text="@Localizer["CustomerData"]" OnClick="@CloseMenueBar" />
                    <NavMenuItem Path="CompanyMasterData" Text="@Localizer["CompanyMasterData"]" OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.WingLagerGroupValue"
                                 Path="ProcessArticleCreationPage" Text="@Localizer["ProcessArticles"]" OnClick="@CloseMenueBar" />
                    <NavMenuItem Policy="@LicenseClaimType.ArticleValue"
                                 Path="articleHomePage" Text="Artikel" OnClick="@CloseMenueBar"/>
                </RadzenPanelMenuItem>
                <RadzenPanelMenuItem Icon="description" Text="@Localizer["Invoicing"]">>
                    <NavMenuItem Path="invoiceOverView" Text="@Localizer["InvoiceOverview"]" OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.GrainAccounting"
                                 Path="grainAccountingHome" Text="@Localizer["GrainAccounting"]" OnClick="@CloseMenueBar"/>
                </RadzenPanelMenuItem>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@LicenseClaimType.WingLagerGroupValue">
            <Authorized Context="wingLagerGroup">
                <RadzenPanelMenuItem Icon="warehouse" Text="Lager">
                    <AuthorizeView Policy="@LicenseClaimType.ProcessOderHandlingValue">
                        <Authorized>
                            <RadzenPanelMenuItem Icon="donut_small" Text="Steuerung">
                                <RadzenPanelMenuItem Path="ProcessControllingSearch" Text="Prozessaufträge" Click="@CloseMenueBar"/>
                            </RadzenPanelMenuItem>
                            <RadzenPanelMenuItem Path="TestConsoleForLindeRobotManagerPage" Text="Robotertest" Click="@CloseMenueBar"/>
                            <RadzenPanelMenuItem Path="WareHouseManagement" Text="Lager" Click="@CloseMenueBar"/>
                        </Authorized>
                    </AuthorizeView>
                </RadzenPanelMenuItem>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@LicenseClaimType.ExportGroupValue">
            <Authorized Context="exportGroup">
                <RadzenPanelMenuItem Icon="outbox" Text=@Localizer["Export"]>
                    <NavMenuItem Policy="@LicenseClaimType.DatevExportValue"
                                 Path="datevexportcsv" Text="@Localizer["DATEV"]" OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.EdifactExportImportValue"
                                 Path="edifactExport" Text="@Localizer["EDIFACT"]" OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.AddisonTseNitExportValue"
                                 Path="addisonTseNitExport" Text="@Localizer["ADDISONAKTEtsenit"]" OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.AddisonPipelineExportValue"
                                 Path="addisonPipelineExport" Text="@Localizer["ADDISONPipeline"]" OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.EasiAdfinityRestApiInvoiceExportValue"
                                 Path="EasiAdfinityrestapi" Text="@Localizer["EasiAdfinity"]" OnClick="@CloseMenueBar"/>
                </RadzenPanelMenuItem>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@LicenseClaimType.ImportGroupValue">
            <Authorized Context="importGroup">
                <RadzenPanelMenuItem Icon="move_to_inbox" Text="@Localizer["Import"]">
                    <NavMenuItem Policy="@LicenseClaimType.EdifactExportImportValue"
                                 Path="edifactImport" Text="@Localizer["EDIFACT"]" OnClick="@CloseMenueBar"/>
                </RadzenPanelMenuItem>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@LicenseClaimType.SchedulerValue">
            <Authorized>
                <RadzenPanelMenuItem Icon="calendar_clock" Text=@Localizer["Tasks"]>
                    <RadzenPanelMenuItem Path="Jobs" Text=@Localizer["Jobs"] Click="@CloseMenueBar"/>
                </RadzenPanelMenuItem>
            </Authorized>
        </AuthorizeView>
        <AuthorizeView Policy="@LicenseClaimType.ConfigurationGroupValue">
            <Authorized Context="configurationGroup">
                <RadzenPanelMenuItem Icon="manufacturing" Text=@Localizer["Configuration"]>
                    <NavMenuItem Policy="@LicenseClaimType.WingLagerGroupValue"
                                 Path="ComboBoxConfigurationPage" Text=@Localizer["ComboBoxes"] OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.InvoiceOverViewExportZugferdValue"
                                 Path="ElectricInvoiceConfigurationPage" Text=@Localizer["ElectronicInvoice"] OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.InvoiceOverViewExportZugferdValue"
                                 Path="ElectricWgsConfigurationPage" Text=@Localizer["ElectronicWgs"] OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.PriceListValue"
                                 Path="PriceListHeaders" Text=@Localizer["PriceList"] OnClick="@CloseMenueBar"/>
                    <NavMenuItem Policy="@LicenseClaimType.OpenApi"
                                 Path="OpenApiConfiguration" Text="@Localizer["OpenAPI"]" OnClick="@CloseMenueBar"/>
                </RadzenPanelMenuItem>
            </Authorized>
        </AuthorizeView>

        <RadzenPanelMenuItem Icon="license" Text=@Localizer["License"] Path="license/addLicensesData" Click="@CloseMenueBar"/>
    </RadzenPanelMenu>
</CascadingAuthenticationState>

@code
{
    // Parameter to control display style (icon only or icon+text)
    [Parameter] public bool SidebarExpanded { get; set; } = true;

    // Parameter to notify the layout parent to potentially close/collapse the sidebar on item click
    [Parameter] public EventCallback<MenuItemEventArgs> CloseMenueBar { get; set; }
}