<RadzenStack class="rz-p-0 rz-p-lg-12" Orientation="Orientation.Vertical">
    <RadzenCard Style="width:100%">
        <RadzenHeading Size="H1" style="display: inline-block" Text="@HeaderText"/>
    </RadzenCard>
    <RadzenRow>
        <RadzenColumn Size="9" class="card">
            @LeftSection
        </RadzenColumn>
        <RadzenColumn Size="3">
            <RadzenRow style="height: 100%">
                <RadzenColumn Size="12" class="card">
                    @TopRightSection
                </RadzenColumn>
                <RadzenColumn Size="12" class="card">
                    @BottomRightSection
                </RadzenColumn>
            </RadzenRow>
        </RadzenColumn>
    </RadzenRow>
</RadzenStack>

@code{
    [Parameter] public string HeaderText { get; set; } = string.Empty;
    
    [Parameter] public required RenderFragment LeftSection { get; set; }
    [Parameter] public required RenderFragment TopRightSection { get; set; }
    [Parameter] public required RenderFragment BottomRightSection { get; set; }
}