@page "/Login"
@using WingCore.application
@using WingCore.application.Contract
@using WingCore.application.Contract.IModels.StammDb
@using WingCore.data.WaWiContext
@using WingCore.domain.Models.StammDbModels
@using WinGng.RazorLib.Components.Common.LoadingPage


@inject DialogService DialogService
@inject AuthenticationStateProvider AuthenticationStateProvider
@inject WingNgDbContextFactory WingNgDbContextFactory
@inject NavigationManager NavigationManager
@inject IStammDbContextAccessor StammDbContextAccessor
@inject IMandantRepository MandantRepository
@inject IWingNgContextAccessor WingNgContextAccessor
@inject ISelectedMandant SelectedMandant

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallbackWithArg="@(o => OnLogin((LoginArgs)o))"
                  Option="_options" DialogService="DialogService"/>


<RadzenStack Gap="0" class="rz-mx-auto rz-border-radius-6 rz-shadow-3" Style="width: 100%; max-width: 400px; overflow: hidden;">
    <RadzenCard class="rz-shadow-0 rz-p-6">
        <RadzenRow Style="width: 100%; margin-bottom: 20px;">
        <RadzenLabel Text="Mandant auswählen" Style="width: 100%;"></RadzenLabel>
        <RadzenDropDown @bind-Value=@StammDbParameter.MandantNumber
                        Data=@AllMandant
                        Style="width: 100%; max-width: 400px; "
                        Name="DropDownBindValue"
                        TextProperty="@nameof(Mandant.Name)"
                        ValueProperty="@nameof(Mandant.Mnr)"
                        Change=@(arg => SetWorkingMandant(arg.ToString()))/>

        </RadzenRow>
        <RadzenTemplateForm Data=@("SimpleLogin")>
            <RadzenLogin AllowRegister="false"
                         AllowResetPassword="true"
                         Login="@(args => OnLoginButton(args))"/>
        </RadzenTemplateForm>
    </RadzenCard>
</RadzenStack>


@code {
    LoadingIndicator? _loadingIndicator;
    private LoadingIndicatorOptions? _options; 
    
    public DbParameter StammDbParameter { get; set; } = new();
    private IEnumerable<Mandant> AllMandant { get; set; } = [];
    [Parameter] public DbParameter MandantDbParameter { get; set; } = new();
    
    protected override async Task OnInitializedAsync()
    {
        await LoadDbConnectionData();
        
        _options = new LoadingIndicatorOptions(false, false, false, true, 0, 1, Localizer["DatabaseIsBeingLoaded"]);
        //var task = await ((ExternalAuthStateProvider)AuthenticationStateProvider).GetAuthenticationStateAsync();
    }
    
    private async Task LoadDbConnectionData()
    {
        try
        {
            StammDbParameter = await StammDbContextAccessor.LoadConnectionObjectFromFile() as DbParameter ?? StammDbParameter;
            StateHasChanged();
            var (isSuccess, errMsg) = StammDbContextAccessor.TestConnection();
            if (isSuccess)
            {
                if(StammDbParameter.IsValid(true))
                    AllMandant = await MandantRepository.GetAll();    
            }
            
            if(!string.IsNullOrEmpty(errMsg))
                await DialogService.ShowError($"{errMsg}");
        }
        catch (Exception e)
        {
            await DialogService.ShowError($"Stammdatenbank ist nicht erreichbar: {e.Message}");
            return;
        }

        try
        {
            if(StammDbParameter.IsValid())
                MandantDbParameter = await WingNgContextAccessor.LoadConnectionObjectFromFile() as DbParameter ?? MandantDbParameter;
            StateHasChanged();
        }
        catch (Exception e)
        {
            await DialogService.ShowError($"Stammdatenbank ist nicht erreichbar: {e.Message}");
            return;
        }

        var selected = await SelectedMandant.GetSelectedMandant();
        if (StammDbParameter.IsValid() && selected is null && !MandantDbParameter.IsValid())
        {
            await DialogService.ShowError($"Für diesen Mandanten({StammDbParameter.MandantNumber}) existiert keine Datenbank.");
        }
    }
    
    private async Task<bool> CheckWorkingMandant(string? value)
    {
        if (string.IsNullOrEmpty(value))
            return false;

        var mandantDbParameter = await SelectedMandant.LoadConnectionObjectFromFile(value) as DbParameter;
        if (mandantDbParameter is null)
        {
            await DialogService.ShowError($"Für den Mandant {value} existiert keine Datenbank");
            return false;
        }

        return true;
    }
    
    private async void SetWorkingMandant(string? value)
    {
        if (string.IsNullOrEmpty(value))
            return;

        if (await SelectedMandant.LoadConnectionObjectFromFile(value) is DbParameter mandantDbParameter)
        {
            MandantDbParameter = mandantDbParameter;
        }
        
        await StammDbContextAccessor.SaveDbConnectionData(StammDbParameter);
        StammDbContextAccessor.SetConnectionString(StammDbParameter.DbPath, StammDbParameter.DbName, StammDbParameter.DbUsername, StammDbParameter.DbPassword);
    
        if (!string.IsNullOrEmpty(StammDbParameter.MandantNumber) && await CheckWorkingMandant(StammDbParameter.MandantNumber))
        {
            WingNgContextAccessor.SetConnectionString(MandantDbParameter.DbPath, MandantDbParameter.DbName, MandantDbParameter.DbUsername, MandantDbParameter.DbPassword);
            await WingNgContextAccessor.SaveDbConnectionData(MandantDbParameter);

            await LoadDbConnectionData();
            await ((ExternalAuthStateProvider)AuthenticationStateProvider).Logout();
        }
    }
    
    async Task OnLoginButton(LoginArgs args)
    {
        if(_loadingIndicator is not null)
            await _loadingIndicator.Run(args);
    }
    
    private async Task OnLogin(LoginArgs args)
    {
        try
        {
            await LoadDbConnectionData();
            //await ((ExternalAuthStateProvider)AuthenticationStateProvider).RegisterUserAsync(args.Username, args.Password);
            var task = await ((ExternalAuthStateProvider)AuthenticationStateProvider).LogInAsync(args.Username, args.Password);
        
            if (!string.IsNullOrWhiteSpace(task.Item2))
            {
                await DialogService.ShowError(task.Item2);
                return;
            }
            var result = Task.Run(() => WingNgDbContextFactory.CreateDbContext());
            while(!result.IsCompleted)
                await Task.Delay(50);
            
            NavigationManager.NavigateTo("/");
        }
        catch (Exception e)
        {
            DialogService.Close();

            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    void OnRegister(string name)
    {
    }

    void OnResetPassword(string value, string name)
    {
    }


}