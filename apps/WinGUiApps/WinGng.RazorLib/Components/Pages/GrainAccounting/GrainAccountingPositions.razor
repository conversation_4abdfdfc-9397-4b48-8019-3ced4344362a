@* --- MODIFIED: Removed loading-related code --- *@
@page "/grainAccountingPositions/{Id:long}/{Mode?}"

@using MediatR
@using WingCore.application.Getreideabrechnung.Header
@using WingCore.application.Getreideabrechnung.Positions
@using WingCore.application.Kontrakts
@using WingCore.application.Lieferant
@using wingPrinterListLabel.application.Contracts
@using WingCore.domain.Models
@inherits BasePage
@inject IMediator Mediator
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService
@inject IListLabelFactory ListLabelFactory
@inject DialogService DialogService
@inject IJSRuntime JSRuntime

<PageTitle>@Localizer["GrainAccounting"]</PageTitle>

<RadzenContent Container="main">
@if (supplier == null) // Check if critical data like supplier is missing
{
    <div class="sidebar-box">
        <p>@Localizer["AccountingInformationIsLoaded"]</p>
    </div>
}
else
{
    @if (IsReadOnly)
    {
    <div class="readonly-indicator">
        <RadzenIcon Icon="lock" Style="margin-right: 5px;"/> @Localizer["ViewModeReadOnly"] 
        @* Optional hint for discoverability: *@
        @if(string.IsNullOrEmpty(Mode)) {
            <span style="font-weight: normal; margin-left: auto; font-size: smaller; margin-right: 15px">@Localizer["GrainAccountingAlreadyEstablished"] </span> <span>
                <div class="tooltip-container">
                        <RadzenIcon Icon="info" Style="cursor: help; color: #777;" />
                        <div class="speech-bubble">@Localizer["YouMustDeleteTheEntryToChangeAnything"]</div>
                </div>
            </span>
        }
    </div>
    } else {
         <div class="readonly-indicator" style="background-color: #d1e7dd; color: #0f5132; border-color: #badbcc;">
            <RadzenIcon Icon="edit" Style="margin-right: 5px;"/> @Localizer["EditModeActive"] 
        </div>
    }

    <RadzenRow>
    <RadzenColumn Size="9">
        <div class="sidebar-box supplier-card">
            <div style="display: flex; justify-content: space-between; align-items: center;">
                <h2><b>@singleSupplier.Lfname1 @singleSupplier.Lfname2, @singleSupplier.Lfplz @singleSupplier.Lfort</b></h2>
                <RadzenButton Text="@(isExpanded ? Localizer["ShowLess"] : Localizer["ShowMore"])" Click="@ToggleExpand" ButtonStyle="ButtonStyle.Light" class="more-button"/>
            </div>

            @if (isExpanded)
            {
                <RadzenRow Style="margin-top: 20px;">
                    <RadzenColumn Size="5">
                        <h4><b>@Localizer["Supplier"]</b></h4>
                        <div class="data-row">
                            <span class="data-label">@Localizer["Name"]:</span>
                            <span>@singleSupplier.Lfname1 @singleSupplier.Lfname2</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">@Localizer["Number"]:</span>
                            <span>@(singleSupplier.Lfnummer)</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">@Localizer["Street"]:</span>
                            <span>@(singleSupplier.Lfstrasse ?? "N/A")</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">@Localizer["City"]:</span>
                            <span>@(singleSupplier.Lfplz ?? "N/A") @(singleSupplier.Lfort ?? "N/A")</span>
                        </div>
                    </RadzenColumn>
                    <RadzenColumn Size="2"></RadzenColumn>
                    <RadzenColumn Size="5">
                        <h4><b>@Localizer["InvoiceOffice"]</b></h4>
                        <div class="data-row">
                            <span class="data-label">@Localizer["Name"]:</span>
                            <span>@singleInvoiceCarrier.Lfname1 @singleInvoiceCarrier.Lfname2</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">@Localizer["Number"]:</span>
                            <span>@(singleInvoiceCarrier.Lfnummer)</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">@Localizer["Street"]:</span>
                            <span>@(singleInvoiceCarrier.Lfstrasse ?? "N/A")</span>
                        </div>
                        <div class="data-row">
                            <span class="data-label">@Localizer["City"]:</span>
                            <span>@(singleInvoiceCarrier.Lfplz ?? "N/A") @(singleInvoiceCarrier.Lfort ?? "N/A")</span>
                        </div>
                    </RadzenColumn>
                </RadzenRow>
            }
        </div>

        @for (int i = 0; i < wiegescheinGroups.Count; i++)
        {
            var group = wiegescheinGroups[i];
            <div class="sidebar-box wgs-card" id="@($"wgs-{group.Wiegeschein.GbwgsNr}")">
                <div class="wgs-card-header" @onclick="@(() => group.IsExpanded = !group.IsExpanded)">
                    <h4>
                        @Localizer["WeighingSlip"] @group.Wiegeschein.GbwgsNr, @singleSupplier.Lfname1
                    </h4>
                    @if (!IsReadOnly)
                    {
                        <div class="card-actions">
                            @if (currentlyEditingGroup == group)
                            {
                                <RadzenButton Text="@Localizer["Save"]" ButtonStyle="ButtonStyle.Light" class="secondary-button" Click="@SaveEdit" Style="margin-right: 8px;"/>
                                <RadzenButton Text="@Localizer["Cancel"]" ButtonStyle="ButtonStyle.Light" class="outline-button" Click="@CancelEdit" Style="margin-right: 8px;"/>
                            }
                            else
                            {
                                <RadzenButton Click="@(() => EditWiegeschein(group))" Text="@Localizer["Edit"]" ButtonStyle="ButtonStyle.Light" class="secondary-button" @onclick:stopPropagation/>
                            }
                            <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Light" class="icon-button" Click="@(() => DeleteWiegeschein(group))" Title="@Localizer["DeleteWeighingSlip"]"/>
                        </div>
                    }
                </div>

                @if (group.IsExpanded)
                {
                    <div style="padding: 16px;">
                         @* --- Kontrakt Section --- *@
                        <div class="contract-section" style="display: flex; align-items: center; margin-bottom: 10px;">
                            <span class="data-label" style="margin-right: 10px;">@Localizer["Contract"]:</span>
                            <div class="contract-display" style="display: flex; align-items: center;">
                                @{
                                    var kontraktNr = group.Wiegeschein.GbkontrNr;
                                    var displayKontrakt = (kontraktNr != null && kontraktNr > 0) ? kontraktNr.ToString() : Localizer["NoContract"];
                                }
                                <span class="kontrakt-value" style="margin-right: 5px;">@displayKontrakt</span>

                                @if (!IsReadOnly) // Edit mode - Add button to trigger selection
                                {
                                    <RadzenButton Icon="more_horiz"
                                                  ButtonStyle="ButtonStyle.Light"
                                                  class="icon-button select-kontrakt-button"/>
                                }
                            </div>
                        </div>

                        <table class="data-table">
                            <thead>
                                <tr>
                                    <th>@Localizer["Designation"]</th>
                                    <th>@Localizer["LaboratoryValue"]</th>
                                    <th>@Localizer["Weight"]</th>
                                    <th>@Localizer["UnitPrice"]</th>
                                    <th>@Localizer["TotalPrice"]</th>
                                    <th></th> </tr>
                            </thead>
                            <tbody>
                            @{
                                var tableRows = GetTableRows(group);
                                foreach (var row in tableRows)
                                {
                                    <tr class="@(row.IsMainItem ? "highlight-row" : "")">
                                        <td>
                                            @if (row.IsMainItem && currentlyEditingGroup == group && currentlyEditingMainItem && !IsReadOnly)
                                            {
                                                <RadzenTextBox @bind-Value="row.Bezeichnung" Style="width: 120px;" />
                                            }
                                            else
                                            {
                                                @row.Bezeichnung
                                            }
                                        </td>
                                        <td>@row.Labortwert</td>
                                        <td>@row.Gewicht</td>
                                        <td>@row.Einzelpreis</td>
                                        <td>@row.Gesamtpreis</td>
                                        <td>
                                            @if (!row.IsMainItem && !IsReadOnly)
                                            {
                                                <div class="action-buttons">
                                                    <RadzenButton Icon="history" ButtonStyle="ButtonStyle.Light" class="icon-button" Click="@(() => ResetModifier(group, row))" Title="@Localizer["ResetValue"]"/>
                                                    <RadzenButton Icon="edit" ButtonStyle="ButtonStyle.Light" class="icon-button" Click="@(() => EditModifier(group, row))" Title="@Localizer["EditValue"]"/>
                                                    <RadzenButton Icon="delete" ButtonStyle="ButtonStyle.Light" class="icon-button delete-button" Click="@(() => DeleteModifier(group, row))" Title="@Localizer["DeleteValue"]"/>
                                                </div>
                                            }
                                        </td>
                                    </tr>
                                }
                            }
                            </tbody>
                        </table>

                        <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 16px;">
                             @if (!IsReadOnly)
                            {
                                <RadzenButton Text="@Localizer["AddValue"]" Click="@(() => AddNewModifier(group))" ButtonStyle="ButtonStyle.Light" class="outline-button add-button">
                                    <RadzenIcon Icon="add"/>
                                </RadzenButton>
                            }
                            else
                            {
                                <div></div> }
                            <div class="total-row">
                                @Localizer["NetWeight"]: @(Convert.ToDouble(group.Wiegeschein.GbabrGew ?? 0m).ToString("F2")) kg &nbsp;&nbsp;&nbsp;
                                @Localizer["NetPrice"]: @(Convert.ToDouble(group.Wiegeschein.GbgesNetto ?? 0m).ToString("F2")) €
                            </div>
                        </div>
                    </div>
                }
            </div>
        }

        @if (!IsReadOnly)
        {
            <div style="margin-top: 20px;">
                <RadzenButton Text="@Localizer["AddWeighingSlips"]" Click="@AddNewWiegeschein" ButtonStyle="ButtonStyle.Light" class="outline-button add-button">
                    <RadzenIcon Icon="add"/>
                </RadzenButton>
            </div>
        }
    </RadzenColumn>

    <RadzenColumn Size="3">
        <div class="summary-sidebar">
            <div class="sidebar-box">
                <div class="summary-item">
                    <span>@Localizer["DeliveryWeight"]:</span>
                    <span>@(Convert.ToDouble(positions?.Sum(p => p?.GbanlGew ?? 0m) ?? 0m).ToString("F2")) kg</span>
                </div>

                <div class="summary-item">
                    <span>@Localizer["Deductions"]:</span>
                     <span>@(Convert.ToDouble((positions?.Where(p => p != null && p.GbabzGew < 0).Sum(p => p.GbabzGew) ?? 0m)).ToString("F2")) kg</span>
                </div>

                <div class="summary-item colored-value">
                    <span>@Localizer["AccountingWeight"]:</span>
                    <span>@(Convert.ToDouble(positions?.Where(p => p != null && p.GbanlGew > 0).Sum(p => p?.GbabrGew ?? 0m) ?? 0m).ToString("F2")) kg</span>
                </div>

                <hr style="margin: 10px 0; border-top: 1px solid #E5E5E5;"/>

                <div class="summary-item">
                    <span>@Localizer["CounterfoilNo"]:</span>
                     <RadzenTextBox Placeholder="@Localizer["EnterNo"]" Style="width: 120px;" Disabled="@IsReadOnly" Value="@(header?.FirstOrDefault()?.Gbaltnr.ToString())"/> @* Assuming a field exists *@
                </div>

                <div class="summary-item">
                    <span>@Localizer["ValueDate"]:</span>
                    <RadzenDatePicker  TValue="DateTime?" Style="width: 120px;" DateFormat="dd.MM.yyyy" Value="@(header?.FirstOrDefault()?.GbvalutaDatum)" Change="@((DateTime? newValue) => { var item = header?.FirstOrDefault(); if(item != null) item.GbvalutaDatum = newValue; StateHasChanged(); })" Disabled="@IsReadOnly"/>
                </div>

                <div class="summary-item">
                    <span>@Localizer["AccountingDate"]:</span>
                    <RadzenDatePicker  TValue="DateTime?" Style="width: 120px;" DateFormat="dd.MM.yyyy" Value="@(header?.FirstOrDefault()?.Gbdatum ?? DateTime.Now)" Change="@((DateTime? newValue) => { var item = header?.FirstOrDefault(); if(item != null) item.Gbdatum = newValue; StateHasChanged(); })" Disabled="@IsReadOnly"/> @* Assuming header has Gbdatum *@
                </div>

                <hr style="margin: 10px 0; border-top: 1px solid #E5E5E5;"/>

                <div class="summary-item">
                    <span>@Localizer["NetAmount"]:</span>
                    <span>@(Convert.ToDouble(positions?.Sum(p => p?.GbgesNetto ?? 0m) ?? 0m).ToString("F2")) €</span>
                </div>

                <div class="summary-item colored-value">
                    <span>@Localizer["GrossAmount"]:</span>
                    <span>@(Convert.ToDouble(header?.FirstOrDefault()?.Gbbrutto ?? 0m).ToString("F2")) €</span>
                </div>

                <div style="margin-top: 24px; display: flex; flex-direction: column; gap: 10px;">
                    <RadzenButton Text="@Localizer["PrintPreview"]" ButtonStyle="ButtonStyle.Light" class="outline-button" Style="width: 100%;" />
                     @if (!IsReadOnly)
                    {
                         <RadzenButton Text="@Localizer["Complete"]" ButtonStyle="ButtonStyle.Light" class="primary-button" Style="width: 100%;" /> @* Add appropriate Click handler *@
                    }
                </div>
            </div>

            <div class="sidebar-box">
                @* --- Connected Contracts --- *@
                @if (connectedContracts?.Any() == true)
                {
                    @foreach (var kontrakt in connectedContracts)
                    {
                        <div class="contract-item">
                            <div class="contract-title">
                                <strong>@Localizer["ContractNo"] @kontrakt.Ktnr</strong>
                                <span class="price-tag">@string.Format("{0:F3} kg", kontrakt.KtrestMenge ?? 0)</span>
                            </div>
                            <div class="contract-details">
                                <div style="margin-top: 5px;">
                                    <span>@kontrakt.Ktstart?.ToString("dd.MM.yyyy")</span>
                                    <RadzenIcon Icon="arrow_forward" Style="margin: 0 5px;" />
                                    <span>@kontrakt.Ktende?.ToString("dd.MM.yyyy")</span>
                                </div>
                                @if (!IsReadOnly)
                                {
                                    <RadzenButton Icon="add" class="icon-button" Style="float: right; margin-top: -25px;" Title="@Localizer["AddContract"]" />
                                }
                            </div>
                        </div>
                    }
                }
                else
                {
                    <div style="padding:10px">@Localizer["NoContractsConnected"]</div>
                }
            </div>
        </div>
    </RadzenColumn>
    </RadzenRow>
}
</RadzenContent>

@code {
    // --- Route Parameters ---
    [Parameter] public long Id { get; set; }
    [Parameter] public string? Mode { get; set; } // Optional mode parameter ("editable" or null/other for read-only)

    // --- Private Fields ---
    private long gbvNumber; // Internal reference, assigned from Id
    private long supplierNumber;

    private IEnumerable<Lieferanten?>? supplier;
    private IEnumerable<Lieferanten?>? invoiceCarrier;
    private IEnumerable<Gbvadr>? header;
    private IEnumerable<Gbvhpt?>? positions; // Nullable collection
    private Lieferanten? singleSupplier; // Nullable
    private Lieferanten? singleInvoiceCarrier; // Nullable
    private bool isExpanded = false;

    // --- ReadOnly State ---
    private bool IsReadOnly => Mode != "editable";

    private List<WiegescheinGroup> wiegescheinGroups = new List<WiegescheinGroup>();
    private List<Kontrakt> connectedContracts = new();

    // UI state tracking
    private WiegescheinGroup? currentlyEditingGroup = null; // Track which group is being edited
    private bool currentlyEditingMainItem = false; // Flag to track main item editing

    // --- Component Classes ---
    public class WiegescheinGroup
    {
        public required Gbvhpt Wiegeschein { get; set; } // Use required or ensure non-null in constructor/init
        public List<Gbvhpt> Modifiers { get; set; } = new List<Gbvhpt>();
        public Kontrakt? Kontrakt { get; set; }
        public RadzenDataGrid<TableRow>? Grid { get; set; } // Allow null if not always used
        public bool IsExpanded { get; set; }
    }

    public class TableRow
    {
        public required string Bezeichnung { get; set; } // Use required or ensure non-null
        public string Labortwert { get; set; } = string.Empty;
        public string Gewicht { get; set; } = string.Empty;
        public string Einzelpreis { get; set; } = string.Empty;
        public string Gesamtpreis { get; set; } = string.Empty;
        public bool IsMainItem { get; set; }
        public required Gbvhpt OriginalItem { get; set; } // Use required or ensure non-null
    }

    // --- Initialization ---
    protected override async Task OnInitializedAsync()
    {
        gbvNumber = Id; // Assign from parameter

        // Load Header Data
        try
        {
             header = await Mediator.Send(new GetGbvHeaderByNumberQuery(gbvNumber));
        }
        catch (Exception ex)
        {
             NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Error, Summary = Localizer["ErrorDuringLoading"], Detail = Localizer["ErrorRetrievingTheHeader", ex.Message], Duration = 6000 });
             // Decide how to proceed - maybe navigate away or show error state
             NavigationManager.NavigateTo("/grainAccountingHome"); // Example
             return;
        }

        if (header == null || !header.Any())
        {
            NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Warning, Summary = Localizer["NotFound"], Detail = Localizer["GrainAccountingWithIDNotFound", gbvNumber], Duration = 4000 });
            NavigationManager.NavigateTo("/grainAccountingHome");
            return; // Exit early
        }

        // Load Positions Data
        try
        {
            positions = await Mediator.Send(new GetGbvPositionsByNumberQuery(gbvNumber));
        }
         catch (Exception ex)
        {
             NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Error, Summary = Localizer["ErrorDuringLoading"], Detail = Localizer["ErrorWhenRetrievingThePositions", ex.Message], Duration = 6000 });
             positions = null; // Ensure positions is null or empty on error
        }

        // Load Supplier Data (only if header was found)
        var firstHeader = header.First(); // Safe because of check above
        supplierNumber = firstHeader.Gblfnr ?? 0; // Use null-coalescing if Gblfnr is nullable

        if(supplierNumber > 0)
        {
             try
             {
                 supplier = await Mediator.Send(new GetLieferantByNumberQuery(supplierNumber));
                 var supplierList = supplier?.ToList() ?? new List<Lieferanten?>();

                 if (!supplierList.Any() || supplierList.First() == null)
                 {
                     NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Warning, Summary = Localizer["NoSupplierFound"], Detail = Localizer["SupplierWithNumberNotFound", supplierNumber], Duration = 4000 });
                     singleSupplier = new Lieferanten { Lfnummer = supplierNumber, Lfname1 = "Unbekannt" }; // Placeholder
                     singleInvoiceCarrier = singleSupplier;
                 }
                 else
                 {
                     singleSupplier = supplierList.First()!;
                     if (singleSupplier.KtabrStelle != null && singleSupplier.KtabrStelle > 0)
                     {
                         invoiceCarrier = await Mediator.Send(new GetLieferantByNumberQuery(singleSupplier.KtabrStelle.Value));
                         singleInvoiceCarrier = invoiceCarrier?.FirstOrDefault() ?? singleSupplier;
                     }
                     else
                     {
                         singleInvoiceCarrier = singleSupplier;
                     }
                 }
             }
              catch (Exception ex)
             {
                 NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Error, Summary = Localizer["ErrorDuringLoading"], Detail = Localizer["ErrorRetrievingTheSupplier", ex.Message], Duration = 6000 });
                 // Set placeholder supplier on error
                 singleSupplier = new Lieferanten { Lfnummer = supplierNumber, Lfname1 = "Fehler beim Laden" };
                 singleInvoiceCarrier = singleSupplier;
             }
        }
        else
        {
             NotificationService.Notify(new NotificationMessage { Severity = NotificationSeverity.Error, Summary = Localizer["DataError"], Detail = Localizer["NoSupplierNumberFoundInHeader"], Duration = 5000 });
             singleSupplier = new Lieferanten { Lfname1 = "Keine LfNr" }; // Placeholder
             singleInvoiceCarrier = singleSupplier;
        }

        // Group Positions
        GroupPositions();

        // Initialize expansion state for groups
        if (wiegescheinGroups.Count > 0)
        {
            wiegescheinGroups[0].IsExpanded = true;
        }

        // Load contracts connected to groups
        await LoadContractsAsync();
    }

    // --- Data Processing Methods ---
    private async Task LoadContractsAsync()
    {
        connectedContracts.Clear();
        var kontraktNumbers = wiegescheinGroups
            .Select(g => g.Wiegeschein.GbkontrNr)
            .Where(n => n.HasValue && n.Value > 0)
            .Distinct();

        foreach (var nr in kontraktNumbers)
        {
            try
            {
                var kontrakt = await Mediator.Send(new GetKontraktByNumberQuery(nr!.Value));
                if (kontrakt != null)
                {
                    connectedContracts.Add(kontrakt);
                    foreach (var group in wiegescheinGroups.Where(g => g.Wiegeschein.GbkontrNr == nr))
                    {
                        group.Kontrakt = kontrakt;
                    }
                }
            }
            catch (Exception ex)
            {
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Error,
                    Summary = Localizer["ErrorDuringLoading"],
                    Detail = Localizer["ErrorWhenRetrievingTheContract", nr, ex.Message],
                    Duration = 6000
                });
            }
        }

        StateHasChanged();
    }

    private void GroupPositions()
    {
        wiegescheinGroups.Clear();

        // Use null-conditional operator and null-coalescing operator for safety
        var positionsList = positions?.Where(p => p != null).OrderBy(p => p!.GbgridPos).ToList() ?? new List<Gbvhpt>();

        if (!positionsList.Any())
        {
             // Add sample data if needed for demonstration
             var wgs1 = new Gbvhpt { GbwgsNr = 234, MaabrDatum = new DateTime(2025, 4, 23), Gbtext = "WEIZEN", GbanlGew = 27020m, Gbep = 200m, GbgesNetto = 5404m, GbabrGew = 25730m };
             var wgs2 = new Gbvhpt { GbwgsNr = 235, MaabrDatum = new DateTime(2025, 4, 24), Gbtext = "GERSTE", GbanlGew = 12500m, Gbep = 180m, GbgesNetto = 2250m, GbabrGew = 12000m };
             
             // Add to groups
             wiegescheinGroups.Add(new WiegescheinGroup { Wiegeschein = wgs1, IsExpanded = true });
             wiegescheinGroups.Add(new WiegescheinGroup { Wiegeschein = wgs2, IsExpanded = false });
             
             return; // Exit after adding sample data
        }

        // Removed the GroupBy and the outer foreach loop.
        // Process the sorted list directly.
        WiegescheinGroup? currentGroup = null;
        foreach (var pos in positionsList)
        {
             if (pos.GbanlGew != null && pos.GbanlGew > 0)
             {
                 // Finalize previous group if exists
                 if (currentGroup != null)
                 {
                     wiegescheinGroups.Add(currentGroup);
                 }
                 // Start new group
                 currentGroup = new WiegescheinGroup { Wiegeschein = pos };
             }
             else if (!string.IsNullOrEmpty(pos.Gbtext) && currentGroup != null)
             {
                 // Add modifier to current group
                 currentGroup.Modifiers.Add(pos);
             }
        }
        // Add the last group if it exists
        if (currentGroup != null)
        {
            wiegescheinGroups.Add(currentGroup);
        }
    }

    private List<TableRow> GetTableRows(WiegescheinGroup group)
    {
        var rows = new List<TableRow>();
        if (group.Wiegeschein == null) return rows; // Safety check

        // Main item row
        rows.Add(new TableRow
        {
            Bezeichnung = group.Wiegeschein.Gbtext ?? "N/A",
            Labortwert = "", // Main item typically doesn't have a single lab value here
            Gewicht = (group.Wiegeschein.GbanlGew.HasValue ? Convert.ToDouble(group.Wiegeschein.GbanlGew.Value).ToString("F2") : "0.00") + " kg",
            Einzelpreis = (group.Wiegeschein.Gbep.HasValue ? Convert.ToDouble(group.Wiegeschein.Gbep.Value).ToString("F2") : "0.00") + " €",
            Gesamtpreis = (group.Wiegeschein.GbgesNetto.HasValue ? Convert.ToDouble(group.Wiegeschein.GbgesNetto.Value).ToString("F2") : "0.00") + " €",
            IsMainItem = true,
            OriginalItem = group.Wiegeschein
        });

        // Modifier rows
        foreach (var modifier in group.Modifiers ?? new List<Gbvhpt>()) // Handle null Modifiers list
        {
             if (modifier == null) continue; // Skip null items in the list

            rows.Add(new TableRow
            {
                Bezeichnung = modifier.Gbtext ?? "N/A",
                Labortwert = modifier.Gblbwert.HasValue && modifier.Gblbwert != 0 ? Convert.ToDouble(modifier.Gblbwert.Value).ToString("F2") + " " + (modifier.Gblbeh ?? "").Trim() : string.Empty,
                Gewicht = modifier.GbabzGew.HasValue && modifier.GbabzGew != 0 ? Convert.ToDouble(modifier.GbabzGew.Value).ToString("F2") + " kg" : string.Empty,
                Einzelpreis = modifier.Gbep.HasValue && modifier.Gbep != 0 ? Convert.ToDouble(modifier.Gbep.Value).ToString("F2") + " €" : string.Empty,
                Gesamtpreis = modifier.GbgesNetto.HasValue && modifier.GbgesNetto != 0 ? Convert.ToDouble(modifier.GbgesNetto.Value).ToString("F2") + " €" : string.Empty,
                IsMainItem = false,
                OriginalItem = modifier
            });
        }

        return rows;
    }

    // --- UI Interaction Methods ---
    private void ToggleExpand()
    {
        isExpanded = !isExpanded;
        // StateHasChanged(); // Generally not needed if triggered by @onclick
    }

    
    private void EditWiegeschein(WiegescheinGroup group)
    {
        if (IsReadOnly) return;
        // Always expand the card when editing
        group.IsExpanded = true;
        // Set the currently editing group to show save/reset buttons
        currentlyEditingGroup = group;
        // Also enable editing of the main item (set a flag or handle as needed)
        currentlyEditingMainItem = true;
        NotificationService.Notify(NotificationSeverity.Info, Localizer["Action"], Localizer["EditingForWeighingSlipInvoked", group.Wiegeschein.GbwgsNr!]);
        // TODO: Implement actual edit logic (e.g., open dialog)
    }

    private void CancelEdit()
    {
        // Reset editing state
        currentlyEditingGroup = null;
        currentlyEditingMainItem = false;
        NotificationService.Notify(NotificationSeverity.Info, Localizer["Action"], Localizer["EditingCanceled"]);
    }

    private void SaveEdit()
    {
        if (currentlyEditingGroup != null)
        {
            NotificationService.Notify(NotificationSeverity.Success, Localizer["Action"], Localizer["ChangesForWeighingSlipSaved", currentlyEditingGroup.Wiegeschein.GbwgsNr!]);
            // TODO: Implement actual save logic
            currentlyEditingGroup = null; // Exit edit mode after saving
            currentlyEditingMainItem = false;
        }
    }

    private async Task DeleteWiegeschein(WiegescheinGroup group) // Example async for potential confirmation
    {
        if (IsReadOnly) return;
        
        var confirmed = await DialogService.Confirm(Localizer["AreYouSure"], Localizer["ReallyDeleteWeighingSlip"], new ConfirmOptions() { OkButtonText = Localizer["Yes"], CancelButtonText = Localizer["No"] });
        if (confirmed != true) return;

        // TODO: Add logic to delete data from backend via Mediator
        // await Mediator.Send(new DeleteWiegescheinCommand(group.Wiegeschein.Id)); // Assuming an Id exists

        wiegescheinGroups.Remove(group);
        NotificationService.Notify(NotificationSeverity.Success, Localizer["Success"], Localizer["WeighingSlipRemoved", group.Wiegeschein.GbwgsNr!]);
        StateHasChanged(); // Update UI after removal
    }

    private void ResetModifier(WiegescheinGroup group, TableRow row)
    {
        if (IsReadOnly) return;
        // Placeholder for reset logic
        // TODO: Implement logic to fetch original value or reset based on rules
         NotificationService.Notify(NotificationSeverity.Info, Localizer["Action"], Localizer["ResetForCalled", row.Bezeichnung]);
         // Example: Resetting display values (actual data reset needs backend call)
         // Find the original item and potentially update the 'row' object if it's being edited in-place
         StateHasChanged();
    }

    private async Task EditModifier(WiegescheinGroup group, TableRow row)
    {
        if (IsReadOnly) return;

        try
        {
            // Prepare data for dialog
            var dialogResult = await DialogService.OpenAsync<ModifierEditDialog>(Localizer["EditValue"],
                new Dictionary<string, object>
                {
                    { "CurrentModifier", row.OriginalItem },
                    { "ParentWgs", group.Wiegeschein }
                },
                new DialogOptions { Width = "500px", Height = "428px" });

            if (dialogResult != null)
            {
                // Update the modifier with dialog result
                var updatedModifier = (Gbvhpt)dialogResult;
                var result = await Mediator.Send(new EditGbvPositionQuery(gbvNumber, (long)row.OriginalItem.GbgridPos!, updatedModifier));

                if (result)
                {
                    NotificationService.Notify(NotificationSeverity.Success, Localizer["Success"], Localizer["ValueUpdated", row.Bezeichnung]);
                    // Update local data
                    row.OriginalItem = updatedModifier;
                    StateHasChanged();
                }
                else
                {
                    NotificationService.Notify(NotificationSeverity.Error, Localizer["Error"], Localizer["UpdateFailed"]);
                }
            }
        }
        catch (Exception ex)
        {
            NotificationService.Notify(NotificationSeverity.Error, Localizer["Error"],Localizer["ErrorDuringEditing", ex.Message]);
        }
    }

    private async Task DeleteModifier(WiegescheinGroup group, TableRow row) // Example async
    {
        if (IsReadOnly) return;
        
        var confirmed = await DialogService.Confirm(Localizer["Confirm"], Localizer["ReallyDeleteValue"], new ConfirmOptions() { OkButtonText = Localizer["Yes"], CancelButtonText = Localizer["No"] });
        if (confirmed != true) return;
        
        await Mediator.Send(new DeleteGbvPositionQuery((long)group.Wiegeschein.Gbnr!, (long)row.OriginalItem.GbgridPos!));

        group.Modifiers.Remove(row.OriginalItem);
        NotificationService.Notify(NotificationSeverity.Success, Localizer["Success"], Localizer["ValueRemoved", row.Bezeichnung]);
        StateHasChanged();
    }

    private void AddNewModifier(WiegescheinGroup group)
    {
        if (IsReadOnly) return;
        // Add a new placeholder modifier to the list
        var newModifier = new Gbvhpt
        {
             Gbnr = gbvNumber, // Link to the main accounting record
             GbwgsNr = group.Wiegeschein.GbwgsNr, // Link to the parent WGS
             Gbtext = Localizer["NewValue"] // Default text
             // Initialize other properties as needed (e.g., GbgridPos)
        };
        group.Modifiers.Add(newModifier);
        NotificationService.Notify(NotificationSeverity.Info, Localizer["Info"], Localizer["NewValueAddedPlaceholder"]);
        // TODO: Potentially open edit dialog immediately for the new item
        StateHasChanged();
    }

    private void AddNewWiegeschein()
    {
        if (IsReadOnly) return;
        // Add a new placeholder Wiegeschein group
        var newWgs = new Gbvhpt
        {
            Gbnr = gbvNumber,
            GbwgsNr = (wiegescheinGroups.Max(wg => wg.Wiegeschein?.GbwgsNr) ?? 0) + 1, // Basic way to get next WGS number
            MaabrDatum = DateTime.Now,
            Gbtext = "NEUER WGS",
            // Initialize weights/prices to 0 or defaults
            GbanlGew = 0m, Gbep = 0m, GbgesNetto = 0m, GbabrGew = 0m
        };
        wiegescheinGroups.Add(new WiegescheinGroup
        {
            Wiegeschein = newWgs,
            IsExpanded = true // Expand the new one by default
        });
         NotificationService.Notify(NotificationSeverity.Info, Localizer["Info"], Localizer["NewWeighingSlipAddedPlaceholder"]);
         // Scroll to the new WGS and set it up for editing
         var newIndex = wiegescheinGroups.Count - 1;
         if (newIndex >= 0)
         {
             // Set the current editing group to the new WGS
             currentlyEditingGroup = wiegescheinGroups[newIndex];
             
             // Use JS interop to scroll to the new element after the component has rendered
             StateHasChanged();
             InvokeAsync(async () => 
             {
                 await Task.Delay(100); // Small delay to ensure DOM is updated
                 await JSRuntime.InvokeVoidAsync("scrollToElement", $"wgs-{newWgs.GbwgsNr}");
             });
         }
        StateHasChanged();
    }
}