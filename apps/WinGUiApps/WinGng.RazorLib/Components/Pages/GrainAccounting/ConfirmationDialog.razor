@using <PERSON><PERSON><PERSON>
@inherits BasePage

<Dialog>
    <DialogTitle>@Title</DialogTitle>
    <DialogContent>
        <p>@Message</p>
    </DialogContent>
    <DialogActions>
        <RadzenButton Text="@Localizer["Ok"]" Click="@Close" />
    </DialogActions>
</Dialog>

@code {
    [Parameter] public string Title { get; set; }

    [Parameter] public string Message { get; set; }

    [CascadingParameter] public DialogService DialogService { get; set; }

    private void Close()
    {
        DialogService.Close();
    }
}