@using Microsoft.AspNetCore.Components
@using Microsoft.AspNetCore.Components.Web
@using WingCore.application.Lieferant
@using WingCore.domain.Models
@using MediatR
@using Radzen.Blazor

@inject DialogService DialogService
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService
@inject IMediator Mediator

<!-- Centering Container -->
<div style="display: flex; justify-content: center; align-items: center; height: 100%;">

    <!-- Customized Radzen Card -->
    <RadzenCard Style="width: 70vw; box-shadow: none; border: none;">
            <RadzenTextBox 
                @bind-Value="SearchTerm" 
                Placeholder="@(Localizer["EnterAccountingNumber"] + "...")"
                Style="width: 100%; border: none; box-shadow: none; text-align: center; font-size: 1.5rem;" 
                @oninput="(e) => SearchTerm = e.Value.ToString() ?? string.Empty"
            @onkeydown="HandleKeyDown" />

    </RadzenCard>
</div>

@code {
    private string SearchTerm { get; set; } = string.Empty;
    
    private async Task HandleKeyDown(KeyboardEventArgs args)
    {
        if (args.Key.Equals("Enter", StringComparison.OrdinalIgnoreCase))
        {
            // Ensure the latest value of SearchTerm is used
            await OnSearch();
        }
    }

    private Task OnSearch()
    {
        if (string.IsNullOrEmpty(SearchTerm))
        {
            NavigationManager.NavigateTo($"/grainAccountingMainList/0");
            DialogService.Close();
            return Task.CompletedTask;
        }
        
        if (SearchTerm.All(char.IsDigit))
        {
            Console.WriteLine("Search for Abrechnungsnummer: " + SearchTerm);
            NavigationManager.NavigateTo($"/grainAccountingPositions/{SearchTerm}/editable");
        }
        else
        {
            NotificationService.Notify(NotificationSeverity.Error, Localizer["Error"], Localizer["PleaseEnterAValidAccountingNumber"]);
        }

        
        DialogService.Close();
        return Task.CompletedTask;
    }
}