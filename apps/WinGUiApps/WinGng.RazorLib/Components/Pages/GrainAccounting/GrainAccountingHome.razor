@page "/grainAccountingHome"
@using MediatR
@using WingCore.application.Contract.Services
@using WingCore.application.Lieferant

@inherits BasePage

@inject DialogService DialogService
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService
@inject IWgsService WgsService
@inject IMediator Mediator

<PageTitle>@Localizer["GrainAccounting"]</PageTitle>

<RadzenContent Container="main">
    <RadzenRow style="font-size: large; text-align: center;">
        <!-- Centered search box column using OffsetMD -->
        <RadzenColumn Size="12" SizeMD="4" OffsetMD="4" Style="padding-top: 3rem">
            <RadzenCard class="rounded-card">
                <div class="search-box">
                    <RadzenTextBox Placeholder="@(Localizer["SearchForSuppliers"] +"...")" Style="width: 100%" @oninput="(e) => SearchTerm = e.Value.ToString() ?? string.Empty" @onkeydown="HandleKeyDown"/>
                </div>
            </RadzenCard>
        </RadzenColumn>
        <RadzenColumn Size="12">
            <RadzenStack Orientation="Orientation.Vertical"
                         JustifyContent="JustifyContent.Center"
                         AlignItems="AlignItems.Center">
                <div class="button-container">
                    <!-- First row: first 2 buttons -->
                    <div class="button-row">
                        <div class="grid-cell">
                            <!-- Light lavender button (12×12 rem) -->
                            <RadzenButton ButtonType="ButtonType.Button"
                                          Size="ButtonSize.Large"
                                          Icon="search"
                                          Text="@Localizer["SearchAccounting"]"
                                          Class="modern-button-1"
                                          Click="@LoadGrainAccounting"/>
                        </div>
                        <div class="grid-cell">
                            <!-- Medium purple button (24×12 rem) -->
                            <RadzenButton ButtonType="ButtonType.Button"
                                          Size="ButtonSize.Large"
                                          Icon="pageview"
                                          Text="@Localizer["LoadWeighingSlips"]"
                                          Class="modern-button-2"
                                          Click="@LoadOpenWGS"/>
                        </div>
                    </div>

                    <!-- Second row: next 2 buttons -->
                    <div class="button-row">
                        <div class="grid-cell">
                            <!-- Dark purple button (24×24 rem) -->
                            <RadzenButton ButtonType="ButtonType.Button"
                                          Size="ButtonSize.Large"
                                          Icon="add_circle_outline"
                                          Text="@Localizer["ContinueAccounting"]"
                                          Class="modern-button-3"
                                          Click="@EditAccounting"/>
                        </div>
                        <div class="grid-cell">
                            <!-- Another medium purple button (12×24 rem) -->
                            <RadzenButton ButtonType="ButtonType.Button"
                                          Size="ButtonSize.Large"
                                          Icon="settings"
                                          Text="@Localizer["Settings"]"
                                          Class="modern-button-4"
                                          Click="@ChangeParameters"/>
                        </div>
                    </div>
                </div>
            </RadzenStack>
        </RadzenColumn>
    </RadzenRow>
</RadzenContent>

@code {
    private long LfNummer;
    
    private string SearchTerm { get; set; } = string.Empty;
    
    private async Task HandleKeyDown(KeyboardEventArgs args)
    {
        if (args.Key.Equals("Enter", StringComparison.OrdinalIgnoreCase))
        {
            // Ensure the latest value of SearchTerm is used
            await OnSearch();
        }
    }
    
    private Task OnSearch()
    {
        if (string.IsNullOrEmpty(SearchTerm))
        {
            return Task.CompletedTask;
        }
        
        if (SearchTerm.All(char.IsDigit))
        {
            NavigationManager.NavigateTo($"/grainAccountingLsList/{SearchTerm}");
        }
        else
        {
            var lfNummer = Mediator.Send(new GetLieferantBySbgQuery(SearchTerm)).Result.FirstOrDefault()?.Lfnummer;
            if (lfNummer != 0 && lfNummer != null) 
            {
                NavigationManager.NavigateTo($"/grainAccountingLsList/{lfNummer}");
            }
            else
            {
                // Notify the user that no matching supplier was found
                NotificationService.Notify(new NotificationMessage
                {
                    Severity = NotificationSeverity.Warning,
                    Summary = Localizer["NoSupplierFound"],
                    Detail = Localizer["NoSupplierFoundForSearchTerm"],
                    Duration = 4000
                });
            }
        }
        return Task.CompletedTask;
    }
    
    private async Task LoadGrainAccounting(MouseEventArgs args)
    {
        await DialogService.OpenAsync<GrainAccountingSearchDialog>(
            null,
            null,
            new DialogOptions()
            {
                CloseDialogOnOverlayClick = true,
                ShowTitle = false,
                Height = "auto"
            });
    }

    private void LoadOpenWGS(MouseEventArgs args)
    {
        NavigationManager.NavigateTo("/grainAccountingLsList");
    }

    private async Task EditAccounting(MouseEventArgs args)
    {
        await DialogService.OpenAsync<GrainAccountingEditSearchDialog>(
            null,
            null,
            new DialogOptions()
            {
                CloseDialogOnOverlayClick = true,
                ShowTitle = false,
                Height = "auto"
            });
    }

    private void ChangeParameters(MouseEventArgs args)
    {
        NavigationManager.NavigateTo("/grainAccountingParameters");
    }

}