@page "/WareHouseManagement"


@inject NavigationManager NavigationManager

<RadzenStack class="rz-p-0 rz-p-lg-12" Orientation="Orientation.Vertical">
    <RadzenColumn Size="12">
            <RadzenStack Orientation="Orientation.Vertical"
                         JustifyContent="JustifyContent.Center"
                         AlignItems="AlignItems.Center">
                <div class="button-container">
                    <div class="button-row">
                        <div class="grid-cell">
                            <RadzenButton ButtonType="ButtonType.Button"
                                          Size="ButtonSize.Large"
                                          Text="Manuelles Einlagern"
                                          Class="modern-button-1"
                                          Click="@LoadManualStore"/>
                        </div>
                        <div class="grid-cell">
                            <RadzenButton ButtonType="ButtonType.Button"
                                          Size="ButtonSize.Large"
                                          Text="Bestandübersicht"
                                          Class="modern-button-2"
                                          Click="@LoadBestandSearch"/>
                        </div>
                    </div>
                    <div class="button-row">
                        <div class="grid-cell">
                            <RadzenButton ButtonType="ButtonType.Button"
                                          Size="ButtonSize.Large"
                                          Text="Kommissionierung"
                                          Class="modern-button-3"
                                          Click="@LoadCommissioning"/>
                        </div>
                        <div class="grid-cell">
                            <RadzenButton ButtonType="ButtonType.Button"
                                          Size="ButtonSize.Large"
                                          Text="Layout"
                                          Class="modern-button-4"
                                          Click="@LoadLagerplan"/>
                        </div>
                    </div>
                </div>
            </RadzenStack>
        </RadzenColumn>
</RadzenStack>

@code {
    private void LoadManualStore()
    {
        NavigationManager.NavigateTo("/ManualStore");
    }

    private void LoadBestandSearch()
    {
        NavigationManager.NavigateTo("/BestandSearch");
    }

    private void LoadCommissioning()
    {
        NavigationManager.NavigateTo("/Commissioning");
    }

    private void LoadLagerplan(MouseEventArgs obj)
    {
        NavigationManager.NavigateTo("/WareHousePlan");
    }

}