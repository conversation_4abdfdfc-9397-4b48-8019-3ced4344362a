@page "/ManualStore"
@using Commons.ComboBoxs
@using MediatR
@using wingLager.application.ComboBoxs
@using wingLager.application.LindeQueue.CreateQueueEntry
@using wingLager.application.PaletLagers
@using wingLager.application.WarehouseManagement.Commands
@using wingLager.application.WarehouseManagement.Queries
@using wingLager.domain.PaletLagers
@using wingLager.domain.WarehouseManagementModels
@using wingLager.domain.WarehouseManagementModels.Status

@inject ISender Sender
@inject NotificationService NotificationService
@inject DialogService DialogService

<RadzenFieldset AllowCollapse="true" Style="width: 100%">
    <HeaderTemplate>
        <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
            <RadzenIcon Icon="warehouse"/>
            <b>Manuell Einlagern</b>
        </RadzenStack>
    </HeaderTemplate>
    <ChildContent>
        <RadzenTemplateForm TItem="WmEinlagerung" Data="@Einlagerung" Submit="@OnSubmit">
            <RadzenStack Gap="2rem" class="rz-p-4 rz-p-md-12">
                <RadzenRow>
                    <RadzenColumn Size="12" Style="text-align:right;">
                        <RadzenButton ButtonType="ButtonType.Submit" Text="Einlagern" Icon="save"
                                      Class="rz-button-primary"/>
                    </RadzenColumn>
                </RadzenRow>
                @if (WithRobot)
                {
                    <RadzenRow>
                        <RadzenColumn>
                            <RadzenDropDown Name="Palettentyp" @bind-Value="@Einlagerung.PallettType"
                                            @bind-Value:after="@LoadFreeStellplaetze" Data="@PalletTypes"/>
                        </RadzenColumn>
                    </RadzenRow>
                }
                <RadzenRow RowGap="1rem">
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Stellplatz"/>
                        <RadzenDropDown Name="stellplatzpos" @bind-Value="@Einlagerung.WmStellplatzId"
                                        Data="@HallenPosInfos"
                                        TextProperty="@nameof(HallenPositionInfo.HallenPosition)"
                                        ValueProperty="@nameof(HallenPositionInfo.StellplatzId)"
                                        Placeholder="Bitte Stellplatz auswählen"
                                        Filter="true"
                                        Disabled="@(HallenPosInfos.Count == 0)"
                                        Style="width: 100%;"/>
                        <RadzenCustomValidator Component="stellplatzpos"
                                               Validator="@(() => Einlagerung.WmStellplatzId != Guid.Empty)"
                                               Text="Bitte Stellplatz auswählen"/>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Bezeichnung (optional)"/>
                        <RadzenTextBox Name="bezeichnung" @bind-Value="@Einlagerung.Bezeichnung"
                                       Style="width: 100%;" AutoFocus="true"
                                       Placeholder="Beschreibung eingeben"/>
                        <RadzenRequiredValidator Component="bezeichnung" Text="Bitte Bezeichnung eingeben"
                                                 Visible="@(string.IsNullOrWhiteSpace(Einlagerung.Ean) && string.IsNullOrWhiteSpace(Einlagerung.Nve))"/>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow RowGap="1rem">
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="EAN (optional)"/>
                        <RadzenTextBox Name="ean" @bind-Value="@Einlagerung.Ean" Style="width: 100%;"
                                       Placeholder="EAN eingeben"/>
                    </RadzenColumn>

                    <RadzenColumn Size="6">
                        <RadzenLabel Text="NVE (optional)"/>
                        <RadzenTextBox Name="nve" @bind-Value="@Einlagerung.Nve" Style="width: 100%;"
                                       Placeholder="NVE eingeben"/>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenRow RowGap="1rem">
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Referenznummer (optional)"/>
                        <RadzenTextBox Name="referenznummer" @bind-Value="@Einlagerung.Referenznummer"
                                       Style="width: 100%;" Placeholder="Referenznummer eingeben"/>
                    </RadzenColumn>
                    <RadzenColumn Size="6">
                        <RadzenLabel Text="Warentyp"/>
                        <RadzenDropDown Name="warentyp" @bind-Value="@Einlagerung.Warentyp"
                                        Data="@_warenTypen" TextProperty="@nameof(WmStellplatzLoadType.Value)"
                                        Placeholder="Warentyp auswählen"
                                        Style="width: 100%;"/>
                        <RadzenRequiredValidator Component="warentyp" Text="Bitte Warentyp auswählen"/>
                    </RadzenColumn>
                </RadzenRow>
            </RadzenStack>
        </RadzenTemplateForm>
    </ChildContent>
</RadzenFieldset>

@code {
    private ICollection<HallenPositionInfo> HallenPosInfos { get; set; } = [];
    private bool WithRobot { get; set; } = false;
    private WmEinlagerung Einlagerung { get; set; } = new();
    private readonly List<WmStellplatzLoadType> _warenTypen = WmStellplatzLoadType.GetAll();
    private List<string> PalletTypes { get; set; } = [];

    private const string ERROR_TITLE = "Fehler";
    private const string SUCCESS_TITLE = "Eingelagert";
    private const string NO_PRODUCTION_SPACE = "Es wurde kein Produktionsstelplatz angelegt";


    protected override async Task OnInitializedAsync()
    {
        PalletTypes = (await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.PalettenTyp))).Select(c => c.Key).ToList();
        await LoadFreeStellplaetze();
    }

    private async Task OnSubmit(WmEinlagerung einlagerung)
    {
        try
        {
            var paletLager = await Sender.Send(new CreateManualPaletLagerEntryCommand(einlagerung));
            if (WithRobot)
            {
                await HandleRobotStorage(paletLager);
            }
            else
            {
                await HandleManualStorage(einlagerung, paletLager);
            }
            await ResetStorageState();
        }
        catch (Exception e)
        {
            await HandleStorageError(e);
        }
    }

    private async Task HandleRobotStorage(PaletLager paletLager)
    {
        var startpos = await Sender.Send(new GetAllWepStellplatzQuery());
        if (startpos.Count != 1)
        {
            NotificationService.Notify(NotificationSeverity.Error, ERROR_TITLE, NO_PRODUCTION_SPACE);
            return;
        }
        var palletType = await Sender.Send(new GetPalletTypeOfPaletLagerQuery(paletLager.Id));
        var stellplatz = await GetValidStellplatz(palletType);
        var hallposition = await Sender.Send(new GetHallPositionOfStellplatzQuery(stellplatz.Id));
        await Sender.Send(new CreateStoringLindeQueueEntryCommand(paletLager, startpos.First().Description, hallposition));
    }

    private async Task<WmStellplatz> GetValidStellplatz(string palletType)
    {
        var stellplaetze = await Sender.Send(new GetEmptyStellplatzListByPalletTypeQuery(palletType, true));
        if (stellplaetze is null || !stellplaetze.Any())
        {
            throw new Exception($"Es konnte kein freier Stellplatz für den Palettentyp {palletType} gefunden werden, der vom Roboter angefahren werden kann");
        }

        return stellplaetze.First();
    }

    private async Task HandleManualStorage(WmEinlagerung einlagerung, PaletLager paletLager)
    {
        await Sender.Send(new StorePalletOnStellplatzCommand(
            einlagerung.WmStellplatzId,
            einlagerung.Ean,
            einlagerung.Nve,
            einlagerung.Bezeichnung,
            einlagerung.Referenznummer,
            einlagerung.Warentyp,
            paletLager.Id,
            einlagerung.PallettType));

        NotificationService.Notify(
            NotificationSeverity.Success,
            SUCCESS_TITLE,
            $"Eingelagert: {einlagerung.Bezeichnung}, {einlagerung.Warentyp.Value}");
    }

    private async Task ResetStorageState()
    {
        Einlagerung = new WmEinlagerung();
        HallenPosInfos = [];
        await LoadFreeStellplaetze();
    }

    private async Task HandleStorageError(Exception e)
    {
        DialogService.Close();
        await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
    }
    
    private async Task LoadFreeStellplaetze()
    {
        if (!WithRobot)
        {
            HallenPosInfos = await Sender.Send(new GetHallenPosInfoListOfEmptyStellplatzByPalletTypeQuery(string.Empty));
            return;
        }
        if (string.IsNullOrWhiteSpace(Einlagerung.PallettType))
            return;
        var hallenpos = await Sender.Send(new GetHallenPosInfoListOfEmptyStellplatzByPalletTypeQuery(Einlagerung.PallettType));
        if (hallenpos.Count == 0)
            NotificationService.Notify(NotificationSeverity.Error, "Kein Platz", $"Im Lager ist kein Platz mehr für Palletten vom Typ {Einlagerung.PallettType}");
        HallenPosInfos = hallenpos;
    }
}