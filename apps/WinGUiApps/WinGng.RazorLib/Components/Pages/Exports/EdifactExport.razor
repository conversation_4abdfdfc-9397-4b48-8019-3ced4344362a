@page "/edifactexport"

@using System.Diagnostics
@using CommunityToolkit.Maui.Storage
@using Licencing
@using ObjectDeAndSerialize
@using QuartzScheduler
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.domain.Common.Configurations
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Pages.Scheduler


@inject IConfigurationService ConfigurationService
@inject IEdifactExportImportService EdifactExportImportService
@inject SeedData SeedData
@inject DialogService DialogService
@inject IJSRuntime Js
@inject IApplicationPath ApplicationPath


<LoadingIndicator @ref="_loadingIndicatorLoadConfigData"
                  DoLoadDataCallback="LoadConfigData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="Exportieren"
                  Option="_options" DialogService="DialogService"/>
<RadzenContent Container="main">
    <ChildContent>
        <RadzenRow >
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text="@Localizer["EDIFACTExport"]">
            </RadzenHeading>

        </RadzenRow>

        <RadzenHeading Size="H2" style="display: inline-block" Text="@Localizer["ExportParameters"]">
        </RadzenHeading>
        <RadzenTabs RenderMode="TabRenderMode.Client" TabPosition="@TabPosition.Top">
            <Tabs>
                <RadzenTabsItem Text="@Localizer["SearchParameters"]">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["FromDate"]</RadzenLabel>
                                <RadzenDatePicker @bind-Value="@ExportParameter.FromDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>

                                <RadzenLabel>@Localizer["FromInvoiceNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@ExportParameter.FromInvoiceNumber InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["ToDate"]</RadzenLabel>
                                <RadzenDatePicker @bind-Value="@ExportParameter.ToDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>

                                <RadzenLabel>@Localizer["ToInvoiceNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@ExportParameter.ToInvoiceNumber InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenRow Gap="6px" Style="margin-top: 8px">
                                    <div></div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@ExportParameter.WithOutgoingInvoice Name="CheckBoxOutgoingInvoice"/>
                                        <RadzenLabel Text="@Localizer["OutgoingInvoice"]" Component="CheckBoxOutgoingInvoice" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@ExportParameter.WithExportedInvoices Name="CheckMitExportierteRechnungen" Style="margin-left: 8px"/>
                                        <RadzenLabel Text="@Localizer["ReExportExportedInvoices"]" Component="CheckMitExportierteRechnungen" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>

                                </RadzenRow>
                            </RadzenStack>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="outbox" Text="@Localizer["Export"]" Click="@ExportierenButton"/>
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["SaveConfiguration"]" Click="@SaveConfigData"/>

                        <CascadingAuthenticationState>
                            <AuthorizeView Policy="@LicenseClaimType.SchedulerValue">
                                <RadzenButton Icon="calendar_clock" Click="CreateScheduler" ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.Large"  Text="@Localizer["CreateJob"]"/>
                            </AuthorizeView>
                        </CascadingAuthenticationState>

                    </RadzenStack>
                </RadzenTabsItem>
                <RadzenTabsItem Text="@Localizer["Configuration"]">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                @* <RadzenLabel>Mandantennummer</RadzenLabel> *@
                                @* <RadzenNumeric @bind-Value=@ExportParameter.MandantenNummer InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/> *@

                                @* <RadzenLabel>Wirtschaftsjahresbeginn</RadzenLabel> *@
                                @* <RadzenDatePicker @bind-Value="@ExportParameter.Wirtschaftsjahresbeginn" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/> *@
                                
                                <RadzenLabel>@Localizer["BeginningExportDate"]</RadzenLabel>
                                <RadzenDatePicker @bind-Value="@ExportParameter.BeginingExportDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>

                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                @* <RadzenLabel>Beraternummer</RadzenLabel> *@
                                @* <RadzenNumeric @bind-Value=@ExportParameter.BeraterNummer InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/> *@

                                @* <RadzenLabel>Sachkontenlänge</RadzenLabel> *@
                                @* <RadzenNumeric @bind-Value=@ExportParameter.SachKontenLaenge InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/> *@
                                <RadzenLabel>@Localizer["SuffixForAccounts"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@ExportParameter.SuffixForKundenkonto InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel Text="@Localizer["FilePath"]"/>
                                <RadzenTextBox @bind-Value=@ExportParameter.SelectedFilePath/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["SaveConfiguration"]" Click="@SaveConfigData"/>
                    </RadzenStack>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </ChildContent>
</RadzenContent>


@code {
    [Parameter] public ConfigurationEdifact ExportParameter { get; set; } = new();
    readonly LoadingIndicatorOptions _options = new(false, false, false, false, 0, 6);
    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(false, false, false, false, 0, 6);
    LoadingIndicator? _loadingIndicator;
    LoadingIndicator? _loadingIndicatorLoadConfigData;
    bool _exportStart;

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender)
            return;
        await LoadConfigDataButton();
        StateHasChanged();
    }

    protected override bool ShouldRender()
    {
        return true;
    }

    private async Task ExportierenButton()
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run();
    }

    private async Task Exportieren()
    {
        if (_exportStart)
            return;

        _exportStart = true;

        try
        {
            _options.CurrentPercent = 1;
            
            var stopwatch = Stopwatch.StartNew();

            var result = Task.Run(async () => await ExportAndDownload());
            while (!result.IsCompleted)
                await Task.Delay(500);

            if (result.Result is null or { Length: 0 })
            {
                _exportStart = false;
                return;
            }

            stopwatch.Stop();

            if(EdifactExportImportService.NumberOfExportInvoices == 0)
            {
                await DialogService.ShowInfo(Localizer["NoInvoicesFound"]); 
                _exportStart = false;
                return;
            }
            
            ExportParameter.WithIncomingInvoice = false;
            ExportParameter.WithGrainSettlementInvoice = false;
            
            if(!string.IsNullOrWhiteSpace(ExportParameter.SelectedFilePath))
            {
                var path = Path.Combine(ExportParameter.SelectedFilePath, EdifactExportImportService.FileName);
                await File.WriteAllBytesAsync(path, result.Result.ToArray());
            }
            else
            {
                if (ApplicationPath.IAmBlazorServer())
                {
                    result.Result.Position = 0;
                    using var streamRef = new DotNetStreamReference(stream: result.Result);

                    await Js.InvokeVoidAsync("downloadFileFromStream", EdifactExportImportService.FileName, streamRef);                    
                }
                else
                {
                    var _ = await FileSaver.Default.SaveAsync(EdifactExportImportService.FileName, result.Result, CancellationToken.None);    
                }
            }

            
            await DialogService.ShowInfo(Localizer["ExportOfInvoicesSuccessfullyCompleted", EdifactExportImportService.NumberOfExportInvoices, stopwatch.Elapsed]);
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
            _exportStart = false;
        }

        _exportStart = false;
        StateHasChanged();
    }

    private async Task<MemoryStream?> ExportAndDownload()
    {
        var base64Zip = await EdifactExportImportService.ExportInvoicesAMemStreamAsync(ExportParameter, IEdifactExportImportService.ExportMode.StratEdi);

        return base64Zip;
    }

    private async Task LoadConfigDataButton()
    {
        if (_loadingIndicatorLoadConfigData is null)
            return;

        await _loadingIndicatorLoadConfigData.Run();
    }
    
    public async Task LoadConfigData()
    {
        try
        {
            ExportParameter = await ConfigurationService.GetConfigurationEdifactAsync();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    bool _saveConfigDataStart;
    
    private async Task SaveConfigData()
    {
        if (_saveConfigDataStart)
            return;

        _saveConfigDataStart = true;
        try
        {
            await ConfigurationService.SetConfigurationEdifactAsync(ExportParameter);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            _saveConfigDataStart = false;
        }
    }

    private async Task CreateScheduler()
    {
        try
        {
            var jobViewModel = new JobViewModel() with
            {
                Gruppe = "Export",
                SelectedJobClass = SeedData.JobsNameExportDatevJob
            };

            JobViewModel? data = await DialogService.OpenAsync<CreateJob>(Localizer["CreateJob"], new Dictionary<string, object>
            {
                { "JobViewModel", jobViewModel }
            });

            if (data?.TimeIntervallInSec is null)
                return;

            ExportParameter.SelectedFilePath = data.SelectedFilePath;
            var result = await SeedData.CheckAndCreateJobsData(
                data.Name,
                data.Gruppe,
                data.ServerName,
                data.TimeIntervallInSec.Value,
                data.SelectedJobClass,
                ExportParameter.SerializeToXml());
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.Message);
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
}