@page "/addisonTseNitExport"

@using System.Diagnostics
@using System.Text
@using CommunityToolkit.Maui.Storage
@using Licencing
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.domain.Common.Configurations
@using WinGng.RazorLib.Components.Common.LoadingPage


@inject IConfigurationService ConfigurationService
@inject IAddisonInvoiceExportService AddisonInvoiceExportService
@inject DialogService DialogService
@inject IJSRuntime Js
@inject IApplicationPath ApplicationPath


<LoadingIndicator @ref="_loadingIndicatorLoadConfigData"
                  DoLoadDataCallback="LoadConfigData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="Exportieren"
                  Option="_options" DialogService="DialogService"/>
<RadzenContent Container="main">
    <ChildContent>
        <RadzenRow >
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text="@Localizer["ADDISONAKTEtsenitExport"]">
            </RadzenHeading>
        </RadzenRow>

        <RadzenHeading Size="H2" style="display: inline-block" Text="@Localizer["ExportParameters"]">
        </RadzenHeading>
        <RadzenTabs RenderMode="TabRenderMode.Client" TabPosition="@TabPosition.Top">
            <Tabs>
                <RadzenTabsItem Text="@Localizer["SearchParameters"]">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                @if (AddisonTseNitExportParameter.UseSystemDateForSearch)
                                {
                                    <RadzenLabel>@Localizer["FromCreationDate"]</RadzenLabel>
                                    <RadzenDatePicker @bind-Value="@AddisonTseNitExportParameter.FromSystemDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>
                                }
                                else if (AddisonTseNitExportParameter.UseInvoiceDateForSearch)
                                {
                                    <RadzenLabel>@Localizer["FromInvoiceDate"]</RadzenLabel>
                                    <RadzenDatePicker @bind-Value="@AddisonTseNitExportParameter.FromDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>
                                }

                                <RadzenLabel>@Localizer["FromInvoiceNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@AddisonTseNitExportParameter.FromInvoiceNumber InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                @if (AddisonTseNitExportParameter.UseSystemDateForSearch)
                                {
                                    <RadzenLabel>@Localizer["UntilCreationDate"]</RadzenLabel>
                                    <RadzenDatePicker @bind-Value="@AddisonTseNitExportParameter.ToSystemDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>
                                }
                                else if (AddisonTseNitExportParameter.UseInvoiceDateForSearch)
                                {
                                    <RadzenLabel>@Localizer["UntilInvoiceDate"]</RadzenLabel>
                                    <RadzenDatePicker @bind-Value="@AddisonTseNitExportParameter.ToDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>
                                }

                                <RadzenLabel>@Localizer["UntilInvoiceNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@AddisonTseNitExportParameter.ToInvoiceNumber InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenRow Gap="6px" Style="margin-top: 8px">
                                    <div></div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@AddisonTseNitExportParameter.WithOutgoingInvoice Name="CheckBoxOutgoingInvoice"/>
                                        <RadzenLabel Text="@Localizer["OutgoingInvoice"]" Component="CheckBoxOutgoingInvoice" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@AddisonTseNitExportParameter.WithIncomingInvoice Name="CheckBoxWithIncomingInvoice" Style="margin-left: 8px"/>
                                        <RadzenLabel Text="@Localizer["IncomingInvoice"]" Component="CheckBoxWithIncomingInvoice" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@AddisonTseNitExportParameter.WithGrainSettlementInvoice Name="CheckBoxWithGrainSettlementInvoice" Style="margin-left: 8px"/>
                                        <RadzenLabel Text="@Localizer["GrainAccounting"]" Component="CheckBoxWithGrainSettlementInvoice" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@AddisonTseNitExportParameter.WithExportedInvoices Name="CheckMitExportierteRechnungen" Style="margin-left: 8px"/>
                                        <RadzenLabel Text="@Localizer["ReExportExportedInvoices"]" Component="CheckMitExportierteRechnungen" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                </RadzenRow>
                            </RadzenStack>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="outbox" Text="@Localizer["Export"]" Click="@ExportierenButton"/>
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["SaveConfiguration"]" Click="@SaveConfigData"/>

                        <CascadingAuthenticationState>
                            <AuthorizeView Policy="@LicenseClaimType.SchedulerValue">
                                @* <RadzenButton Icon="calendar_clock" Click="CreateScheduler" ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.Large"  Text="Job erstellen"/> *@
                            </AuthorizeView>
                        </CascadingAuthenticationState>

                    </RadzenStack>
                </RadzenTabsItem>
                <RadzenTabsItem Text="@Localizer["Configuration"]">
                    <RadzenRow Gap="1rem" style="font-size: large">
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["ADDISONClientNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@AddisonTseNitExportParameter.AddisonMandantNummer InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                                
                                <RadzenLabel>@Localizer["BeginningExportDate"]</RadzenLabel>
                                <RadzenDatePicker @bind-Value="@AddisonTseNitExportParameter.BeginingExportDate" DateFormat="dd.MM.yyyy" Name="DatePickerDateOnlyType"/>

                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel>@Localizer["ConsultantNumber"]</RadzenLabel>
                                <RadzenNumeric @bind-Value=@AddisonTseNitExportParameter.ExternMandantNummer InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "enter value" } })"/>
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeSM="6">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenLabel Text="@Localizer["FilePath"]"/>
                                <RadzenTextBox @bind-Value=@AddisonTseNitExportParameter.SelectedFilePath/>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                    <RadzenRow Gap="1rem" style="font-size: large">
                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                <RadzenRow Gap="6px" Style="margin-top: 8px">
                                    <div></div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@AddisonTseNitExportParameter.UseInvoiceDateForSearch Name="CheckBoxUseInvoiceDateForSearch"/>
                                        <RadzenLabel Text="@Localizer["UseInvoiceDateInsteadOfSystemDateForExport"]" Component="CheckBoxUseInvoiceDateForSearch" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    <div>
                                        <RadzenCheckBox @bind-Value=@AddisonTseNitExportParameter.MitFolgebuchung Name="CheckBoxMitFolgebuchung"/>
                                        <RadzenLabel Text="@Localizer["WithSubsequentBooking"]" Component="CheckBoxMitFolgebuchung" Style="margin-left: 8px; vertical-align: middle;"/>
                                    </div>
                                    @if (AddisonTseNitExportParameter.MitFolgebuchung)
                                    {
                                        <div>
                                            <RadzenCheckBox @bind-Value=@AddisonTseNitExportParameter.FolgebuchungenFuerUStCreateAutomatic Name="CheckBoxFolgebuchungenFuerUStCreateAutomatic"/>
                                            <RadzenLabel Text="@Localizer["SubsequentPostingsForVATAreGeneratedAutomatically"]" Component="CheckBoxFolgebuchungenFuerUStCreateAutomatic" Style="margin-left: 8px; vertical-align: middle;"/>
                                        </div>
                                    }
                                </RadzenRow>
                            </RadzenStack>
                    </RadzenRow>
                    <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                        <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="save" Text="@Localizer["SaveConfiguration"]" Click="@SaveConfigData"/>
                    </RadzenStack>
                </RadzenTabsItem>
            </Tabs>
        </RadzenTabs>
    </ChildContent>
</RadzenContent>


@code {
    [Parameter] public ConfigurationAddisonTseNit AddisonTseNitExportParameter { get; set; } = new();
    readonly LoadingIndicatorOptions _options = new(false, false, false, false, 0, 6);
    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(false, false, false, false, 0, 6);
    LoadingIndicator? _loadingIndicator;
    LoadingIndicator? _loadingIndicatorLoadConfigData;
    bool _exportStart;

    protected override async void OnAfterRender(bool firstRender)
    {
        if (!firstRender)
            return;
        await LoadConfigDataButton();
        StateHasChanged();
    }

    protected override bool ShouldRender()
    {
        return true;
    }

    private async Task ExportierenButton()
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run();
    }

    private async Task Exportieren()
    {
        if (_exportStart)
            return;

        _exportStart = true;

        try
        {
            _options.CurrentPercent = 1;
            
            var stopwatch = Stopwatch.StartNew();

            var result = Task.Run(async () => await ExportAndDownload());
            while (!result.IsCompleted)
                await Task.Delay(500);

            if (result.Result is null or { Length: 0 })
            {
                _exportStart = false;
                return;
            }

            stopwatch.Stop();

            if(AddisonInvoiceExportService.NumberOfExportInvoices == 0)
            {
                await DialogService.ShowInfo(Localizer["NoInvoicesFound"]); 
                _exportStart = false;
                return;
            }
            

            if(!string.IsNullOrWhiteSpace(AddisonTseNitExportParameter.SelectedFilePath))
            {
                var path = Path.Combine(AddisonTseNitExportParameter.SelectedFilePath, AddisonInvoiceExportService.FileName);
                await File.WriteAllTextAsync(path, result.Result,Encoding.GetEncoding("ISO-8859-1"));
            }
            else
            {
                if (ApplicationPath.IAmBlazorServer())
                {
                    var encoding = Encoding.GetEncoding("ISO-8859-1");
                    var byteArray = encoding.GetBytes(result.Result);
                    var memStream = new MemoryStream(byteArray);
                    using var streamRef = new DotNetStreamReference(stream: memStream);

                    await Js.InvokeVoidAsync("downloadFileFromStream", AddisonInvoiceExportService.FileName, streamRef);                    
                }
                else
                {
                    var folderPickerResult = await FolderPicker.PickAsync(CancellationToken.None);
                    var path = Path.Combine(folderPickerResult.Folder?.Path ?? "", AddisonInvoiceExportService.FileName);
                    await File.WriteAllTextAsync(path, result.Result,Encoding.GetEncoding("ISO-8859-1"));
                }
            }

            
            await DialogService.ShowInfo(  Localizer["ExportOfInvoicesSuccessfullyCompleted", AddisonInvoiceExportService.NumberOfExportInvoices, stopwatch.Elapsed]);
           }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
            _exportStart = false;
        }

        _exportStart = false;
        StateHasChanged();
    }

    private async Task<string?> ExportAndDownload()
    {
        if (AddisonTseNitExportParameter.UseSystemDateForSearch)
        {
            AddisonTseNitExportParameter.ToDate = null;
            AddisonTseNitExportParameter.FromDate = null;
            AddisonTseNitExportParameter.FromSystemDate = new DateTime(AddisonTseNitExportParameter.FromSystemDate!.Value.Date.Year, AddisonTseNitExportParameter.FromSystemDate!.Value.Date.Month, AddisonTseNitExportParameter.FromSystemDate!.Value.Date.Day, 0, 0, 0);
            AddisonTseNitExportParameter.ToSystemDate = new DateTime(AddisonTseNitExportParameter.ToSystemDate!.Value.Date.Year, AddisonTseNitExportParameter.ToSystemDate!.Value.Date.Month, AddisonTseNitExportParameter.ToSystemDate!.Value.Date.Day, 23, 59, 59);
        }
        else if (AddisonTseNitExportParameter.UseInvoiceDateForSearch)
        {
            AddisonTseNitExportParameter.ToSystemDate = null;
            AddisonTseNitExportParameter.FromSystemDate = null;
        }

        var contentAsString = await AddisonInvoiceExportService
            .ExportInvoicesAsString(AddisonTseNitExportParameter);

        return contentAsString;
    }

    private async Task LoadConfigDataButton()
    {
        if (_loadingIndicatorLoadConfigData is null)
            return;

        await _loadingIndicatorLoadConfigData.Run();
    }
    
    public async Task LoadConfigData()
    {
        try
        {
            AddisonTseNitExportParameter = await ConfigurationService.GetConfigurationAddisonTseNitAsync();
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    bool _saveConfigDataStart;
    
    private async Task SaveConfigData()
    {
        if (_saveConfigDataStart)
            return;

        _saveConfigDataStart = true;
        try
        {
            await ConfigurationService.SetConfigurationAddisonTseNitAsync(AddisonTseNitExportParameter);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            _saveConfigDataStart = false;
        }
    }
}