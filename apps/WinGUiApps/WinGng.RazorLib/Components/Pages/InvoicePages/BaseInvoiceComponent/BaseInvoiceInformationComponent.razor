@using System.Threading
@using CommunityToolkit.Maui.Storage
@using Licencing
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using WingCore.application.Extensions
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Pages.InvoicePages.BaseInvoiceComponent.Postings
@using WinGng.RazorLib.Components.Pages.InvoicePages.BaseInvoiceComponent.InvoiceHolderOrSuppliers
@inject DialogService DialogService

@inject NavigationManager NavigationManager
@inject IZugFeRdxRechnungInvoiceExport ZugFeRdxRechnungInvoiceExport
@inject IEdifactExportImportService EdifactExportImportService
@inject IJSRuntime Js
@inject IApplicationPath ApplicationPath
@if (BaseInvoiceOverviewDto is null)
{
    return;
}

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="ExportInvoice"
                  Option="_options" DialogService="DialogService"/>

@{ SetInvoiceObject(); }
<RadzenColumn SizeMD=12 Style="min-width: 90%">
    <RadzenTemplateForm TItem="BaseInvoiceOverviewEntryDto" Data="@BaseInvoiceOverviewDto" Visible="@(BaseInvoiceOverviewDto is not null)">
        <RadzenStack Gap="1rem" style="margin-bottom:1rem;" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Start">
            <RadzenButton Icon="arrow_back" Click="NavigatetoInvoiceOverView" ButtonStyle="ButtonStyle.Primary"/>
            <CascadingAuthenticationState>
                <AuthorizeView Policy="@LicenseClaimType.InvoiceExportOrImportGroupValue">
                    <Authorized Context="InvoiceExportOrImportGroupContext">
                        <RadzenSplitButton Icon="save" BusyText="@(Localizer["Export"] + "...")" IsBusy=@BusyExportFunction Click="@(args => OnExportClick(args))" Text="@Localizer["Export"]">
                            <ChildContent>
                                <AuthorizeView Policy="@LicenseClaimType.InvoiceOverViewExportZugferdValue">
                                    <Authorized Context="InvoiceOverViewExportZugferdContext">
                                        <RadzenSplitButtonItem Text="@Localizer["ZUGFeRD"]" Value="1"/>
                                        <RadzenSplitButtonItem Text="@Localizer["ZUGFeRDXRechnung"]" Value="2"/>
                                        <RadzenSplitButtonItem Text="@Localizer["XRechnung"]" Value="3"/>
                                    </Authorized>
                                </AuthorizeView>
                                <AuthorizeView Policy="@LicenseClaimType.EdifactExportImportValue">
                                    <Authorized Context="EdifactExportImportContext">
                                        <RadzenSplitButtonItem Text="@Localizer["EDIFACTTrueCommerce"]" Value="4"/>
                                        <RadzenSplitButtonItem Text="@Localizer["EDIFACTStradEdi"]" Value="5"/>
                                    </Authorized>
                                </AuthorizeView>
                            </ChildContent>
                        </RadzenSplitButton>
                    </Authorized>
                </AuthorizeView>
            </CascadingAuthenticationState>
        </RadzenStack>
        <RadzenStack>
            <RadzenRow>
                <RadzenColumn Size="12" SizeMD="6" Class="rz-p-4 rz-border-radius-1" Style="border: var(--rz-grid-cell-border)">
                    <RadzenText TextStyle="TextStyle.Subtitle1">@Titel</RadzenText>
                    <RadzenRow Gap="1rem">
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenStack Orientation="Orientation.Vertical">
                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["InvoiceNumber"]</RadzenText>
                                <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                    <b>@BaseInvoiceOverviewDto?.InvoiceNumber</b>
                                </RadzenText>
                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-mb-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["TotalNet"]</RadzenText>
                                <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                    <b>@BaseInvoiceOverviewDto?.TotalNet</b>
                                </RadzenText>
                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-mb-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["TotalVAT"]</RadzenText>
                                <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                    <b>@BaseInvoiceOverviewDto?.TotalVatAmount</b>
                                </RadzenText>
                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-mb-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["TotalGross"]</RadzenText>
                                <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                    <b>@BaseInvoiceOverviewDto?.TotalGross</b>
                                </RadzenText>
                                @if (BaseInvoiceOverviewDto is not null && BaseInvoiceOverviewDto.IsCancellationInvoice)
                                {
                                    <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["OriginalInvoiceNumber"]</RadzenText>
                                    <RadzenRow>
                                        <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                            <b>@BaseInvoiceOverviewDto?.CancelOrCreditNoteInvoiceNumber</b>
                                        </RadzenText>
                                        <RadzenButton Click=@OpenCancelInvoice Icon="open_in_new" Size="ButtonSize.ExtraSmall" ButtonStyle="ButtonStyle.Light"/>
                                    </RadzenRow>
                                }
                                @if (BaseInvoiceOverviewDto is not null && BaseInvoiceOverviewDto.WasCanceled)
                                {
                                    <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["CancellationInvoiceNumber"]</RadzenText>
                                    <RadzenRow>
                                        <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                            <b>@BaseInvoiceOverviewDto?.CancelOrCreditNoteInvoiceNumber</b>
                                        </RadzenText>
                                        <RadzenButton Click=@OpenCancelInvoice Icon="open_in_new" Size="ButtonSize.ExtraSmall" ButtonStyle="ButtonStyle.Light"/>
                                    </RadzenRow>
                                }
                            </RadzenStack>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6">
                            <RadzenStack Orientation="Orientation.Vertical">
                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["InvoiceDate"]</RadzenText>
                                <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                    <b>@BaseInvoiceOverviewDto?.InvoiceDate.ToString("d")</b>
                                </RadzenText>
                                @if (BaseInvoiceOverviewDto?.ValutaDate != DateTime.MinValue)
                                {
                                    <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["ValueDate"]</RadzenText>
                                    <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                        <b>@BaseInvoiceOverviewDto?.ValutaDate.ToString("d")</b>
                                    </RadzenText>
                                }
                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["Customer"]</RadzenText>
                                <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                    <b>@BaseInvoiceOverviewDto?.SysUser</b>
                                </RadzenText>
                                <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["CreationDate"]</RadzenText>
                                <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                    <b>@BaseInvoiceOverviewDto?.SysTime</b>
                                </RadzenText>
                            </RadzenStack>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenColumn>
                <RadzenColumn>
                    <RadzenStack Gap="1rem" Orientation="Orientation.Vertical">
                        @if (!OutgoingInvoiceOverviewDto.IsInvoiceHolderNull() ||
                             !IncomingInvoiceOverviewDto.IsInvoiceHolderNull() ||
                             !RapeSettlementInvoiceOverviewDto.IsInvoiceHolderNull() ||
                             !GrainSettlementInvoiceOverviewDto.IsInvoiceHolderNull())
                        {
                            <RadzenStack Orientation="Orientation.Vertical" Size="12" SizeMD="6" Class="rz-p-4 rz-border-radius-1" Style="border: var(--rz-grid-cell-border)">
                                <RadzenText TextStyle="TextStyle.Subtitle1">@Localizer["InvoiceHolder"]</RadzenText>
                                <OutgoingInvoiceHolderAndSupplierComponent OutgoingInvoiceOverviewDto="@OutgoingInvoiceOverviewDto" ShowInvoiceHolder="true"/>
                                <IncomingInvoiceHolderAndSupplierComponent IncomingInvoiceOverviewDto="@IncomingInvoiceOverviewDto" ShowInvoiceHolder="true"/>
                                <GrainSettlementInvoiceHolderAndSupplierComponent GrainSettlementInvoiceOverviewDto="@GrainSettlementInvoiceOverviewDto" ShowInvoiceHolder="true"/>
                                <RapeSettlementInvoiceHolderAndSupplierComponent RapeSettlementInvoiceOverviewDto="@RapeSettlementInvoiceOverviewDto" ShowInvoiceHolder="true"/>
                            </RadzenStack>
                        }
                        @if (!OutgoingInvoiceOverviewDto.IsInvoiceSupplierNull() ||
                             !IncomingInvoiceOverviewDto.IsInvoiceSupplierNull() ||
                             !RapeSettlementInvoiceOverviewDto.IsInvoiceSupplierNull() ||
                             !GrainSettlementInvoiceOverviewDto.IsInvoiceSupplierNull())
                        {
                            <RadzenStack Orientation="Orientation.Vertical" Size="12" SizeMD="6" Class="rz-p-4 rz-border-radius-1" Style="border: var(--rz-grid-cell-border)">
                                <RadzenText TextStyle="TextStyle.Subtitle1">@Localizer["InvoiceIssuer"]</RadzenText>
                                <OutgoingInvoiceHolderAndSupplierComponent OutgoingInvoiceOverviewDto="@OutgoingInvoiceOverviewDto" ShowInvoiceSupplier="true"/>
                                <IncomingInvoiceHolderAndSupplierComponent IncomingInvoiceOverviewDto="@IncomingInvoiceOverviewDto" ShowInvoiceSupplier="true"/>
                                <GrainSettlementInvoiceHolderAndSupplierComponent GrainSettlementInvoiceOverviewDto="@GrainSettlementInvoiceOverviewDto" ShowInvoiceSupplier="true"/>
                                <RapeSettlementInvoiceHolderAndSupplierComponent RapeSettlementInvoiceOverviewDto="@RapeSettlementInvoiceOverviewDto" ShowInvoiceSupplier="true"/>
                            </RadzenStack>
                        }
                    </RadzenStack>
                </RadzenColumn>
            </RadzenRow>

            <RadzenCard Variant="Variant.Filled">
                <RadzenText TextStyle="TextStyle.Subtitle1">@Localizer["VAT"]</RadzenText>
                <RadzenDataGrid AllowFiltering="true" AllowPaging="true" AllowSorting="true" FilterPopupRenderMode="PopupRenderMode.OnDemand" FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                Data="@BaseInvoiceOverviewDto?.ListTax" Density="Density.Compact" AllowAlternatingRows="false">
                    <Columns>
                        <RadzenDataGridColumn Property="@nameof(TaxDto.Vat)" Title="@Localizer["Percent"]" FormatString="{0:F0}">
                            <FooterTemplate>
                                <b>@Localizer["Total"]</b>
                            </FooterTemplate>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn Property="@nameof(TaxDto.VatAmount)" Title="@Localizer["VATAmount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                            <FooterTemplate>
                                <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", BaseInvoiceOverviewDto?.ListTax.Sum(o => o.VatAmount))</b>
                            </FooterTemplate>
                        </RadzenDataGridColumn>
                        <RadzenDataGridColumn Property="@nameof(TaxDto.NetAmount)" Title="@Localizer["NetAmount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                            <FooterTemplate>
                                <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", BaseInvoiceOverviewDto?.ListTax.Sum(o => o.NetAmount))</b>
                            </FooterTemplate>
                        </RadzenDataGridColumn>
                    </Columns>
                </RadzenDataGrid>
            </RadzenCard>
            <RadzenCard Variant="Variant.Filled">
                <RadzenText TextStyle="TextStyle.Subtitle1">@Localizer["Positions"]</RadzenText>

                <OutgoingInvoicePostingsListComponent OutgoingInvoiceOverviewDto="@OutgoingInvoiceOverviewDto"></OutgoingInvoicePostingsListComponent>
                <IncomingInvoicePostingsListComponent IncomingInvoiceOverviewDto="@IncomingInvoiceOverviewDto"/>
                <GrainSettlementInvoicePostingsListComponent GrainSettlementInvoiceOverviewDto="@GrainSettlementInvoiceOverviewDto"/>
                <RapeSettlementInvoicePostingsListComponent RapeSettlementInvoiceOverviewDto="@RapeSettlementInvoiceOverviewDto"/>
            </RadzenCard>
        </RadzenStack>
    </RadzenTemplateForm>
</RadzenColumn>

@code {
    [Parameter] public BaseInvoiceOverviewEntryDto? BaseInvoiceOverviewDto { get; set; }
    [Parameter] public string Titel { get; set; } = string.Empty;
    [Parameter] public EventCallback<long> ReloadInvoiceCallback { get; set; }


    private OutgoingInvoiceOverviewDto? OutgoingInvoiceOverviewDto { get; set; }
    private IncomingInvoiceOverviewDto? IncomingInvoiceOverviewDto { get; set; }
    private GrainSettlementInvoiceOverviewDto? GrainSettlementInvoiceOverviewDto { get; set; }
    private RapeSettlementInvoiceOverviewDto? RapeSettlementInvoiceOverviewDto { get; set; }

    LoadingIndicator? _loadingIndicator;
    readonly LoadingIndicatorOptions _options = new(false, false, false, false);

    private void NavigatetoInvoiceOverView() => NavigationManager.NavigateTo("/invoiceOverView");

    private async Task OpenCancelInvoice()
    {
        if (BaseInvoiceOverviewDto?.CancelOrCreditNoteInvoiceNumber is null) return;

        if (ReloadInvoiceCallback.HasDelegate)
        {
            await ReloadInvoiceCallback.InvokeAsync(BaseInvoiceOverviewDto.CancelOrCreditNoteInvoiceNumber);
        }

        StateHasChanged();
    }

    protected override async Task OnInitializedAsync()
    {
        NavigationManager.LocationChanged += (o, args) => LocationChanged(o, args);

        await base.OnInitializedAsync();
    }

    void LocationChanged(object? sender, LocationChangedEventArgs e)
    {
    }

    private void SetInvoiceObject()
    {
        OutgoingInvoiceOverviewDto = BaseInvoiceOverviewDto as OutgoingInvoiceOverviewDto;
        IncomingInvoiceOverviewDto = BaseInvoiceOverviewDto as IncomingInvoiceOverviewDto;
        GrainSettlementInvoiceOverviewDto = BaseInvoiceOverviewDto as GrainSettlementInvoiceOverviewDto;
        RapeSettlementInvoiceOverviewDto = BaseInvoiceOverviewDto as RapeSettlementInvoiceOverviewDto;
    }

    private bool BusyExportFunction { get; set; }

    private async void OnExportClick(RadzenSplitButtonItem? item)
    {
        if (item is null) return;
        BusyExportFunction = true;
        StateHasChanged();


        var task = item.Value switch
        {
            "1" => ExportInvoiceButton(IZugFeRdxRechnungInvoiceExport.ExportMode.ZugFerD),
            "2" => ExportInvoiceButton(IZugFeRdxRechnungInvoiceExport.ExportMode.ZugFerDProfileXRechnung),
            "3" => ExportInvoiceButton(IZugFeRdxRechnungInvoiceExport.ExportMode.XRechnung),
            "4" => ExportInvoiceEdifact(IEdifactExportImportService.ExportMode.TrueCommerce),
            "5" => ExportInvoiceEdifact(IEdifactExportImportService.ExportMode.StratEdi),
            _ => null
        };

        if (task is not null)
        {
            while (!task.IsCompleted)
                await Task.Delay(50);
        }

        BusyExportFunction = false;
        StateHasChanged();
    }

    private async Task ExportInvoiceButton(object? arg)
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run(arg);
    }


    async Task ExportInvoice(object o)
    {
        if (o is not IZugFeRdxRechnungInvoiceExport.ExportMode exportMode)
            return;
        try
        {
            var memStream = BaseInvoiceOverviewDto switch
            {
                OutgoingInvoiceOverviewDto invoice => await ZugFeRdxRechnungInvoiceExport.ExportInvoicesAsXmlAsync(invoice, exportMode),
                IncomingInvoiceOverviewDto invoice => await ZugFeRdxRechnungInvoiceExport.ExportInvoicesAsXmlAsync(invoice, exportMode),
                GrainSettlementInvoiceOverviewDto invoice => await ZugFeRdxRechnungInvoiceExport.ExportInvoicesAsXmlAsync(invoice, exportMode),
                RapeSettlementInvoiceOverviewDto invoice => await ZugFeRdxRechnungInvoiceExport.ExportInvoicesAsXmlAsync(invoice, exportMode),
                _ => null
            };

            if (memStream is not null)
                if (ApplicationPath.IAmBlazorServer())
                {
                    memStream.Position = 0;
                    using var streamRef = new DotNetStreamReference(stream: memStream);

                    await Js.InvokeVoidAsync("downloadFileFromStream", ZugFeRdxRechnungInvoiceExport.FileName, streamRef);
                }
                else
                {
                    var _ = await FileSaver.Default.SaveAsync(ZugFeRdxRechnungInvoiceExport.FileName, memStream, CancellationToken.None);
                }
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }
    }

    async Task ExportInvoiceEdifact(object o)
    {
        if (o is not IEdifactExportImportService.ExportMode exportMode)
            return;
        try
        {
            var memStream = BaseInvoiceOverviewDto switch
            {
                OutgoingInvoiceOverviewDto invoice => await EdifactExportImportService.ExportInvoicesAMemStreamAsync(invoice, exportMode),
                IncomingInvoiceOverviewDto invoice => await EdifactExportImportService.ExportInvoicesAMemStreamAsync(invoice, exportMode),
                GrainSettlementInvoiceOverviewDto invoice => await EdifactExportImportService.ExportInvoicesAMemStreamAsync(invoice, exportMode),
                RapeSettlementInvoiceOverviewDto invoice => await EdifactExportImportService.ExportInvoicesAMemStreamAsync(invoice, exportMode),
                _ => null
            };

            if (memStream is not null)
                if (ApplicationPath.IAmBlazorServer())
                {
                    memStream.Position = 0;
                    using var streamRef = new DotNetStreamReference(stream: memStream);

                    await Js.InvokeVoidAsync("downloadFileFromStream", EdifactExportImportService.FileName, streamRef);
                }
                else
                {
                    var _ = await FileSaver.Default.SaveAsync(EdifactExportImportService.FileName, memStream, CancellationToken.None);
                }
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }
    }

}