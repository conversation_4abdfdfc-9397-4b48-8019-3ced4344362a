@inject NavigationManager NavigationManager

@if (InvoiceSupplierOrHolderDto is null)
{
    return;
}

<RadzenRow Gap="1rem">
    <RadzenColumn Size="12" SizeMD="6">
        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["Number"]</RadzenText>
        <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
            <b>@InvoiceSupplierOrHolderDto?.Number</b>
            <RadzenButton Click=@OpenCustomer Icon="open_in_new" Size="ButtonSize.ExtraSmall" ButtonStyle="ButtonStyle.Light"/>
        </RadzenText>
        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-mb-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["Address"]</RadzenText>
        <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
            <b>@InvoiceSupplierOrHolderDto?.Street</b><br/>
            <b>@InvoiceSupplierOrHolderDto?.Plz @InvoiceSupplierOrHolderDto?.Ort</b><br/>
            <b>@InvoiceSupplierOrHolderDto?.Country</b>
        </RadzenText>
    </RadzenColumn>
    <RadzenColumn Size="12" SizeMD="6">
        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0" Style="color: var(--rz-text-tertiary-color);">@Localizer["Name"]</RadzenText>
        <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
            <b>@InvoiceSupplierOrHolderDto?.Name</b><br/>
            <b>@InvoiceSupplierOrHolderDto?.Name2</b>
        </RadzenText>
    </RadzenColumn>
</RadzenRow>


@code {
    [Parameter] public InvoiceSupplierOrHolderDto? InvoiceSupplierOrHolderDto { get; set; }

    private Task OpenCustomer()
    {
        if (InvoiceSupplierOrHolderDto?.Number is null) return Task.CompletedTask;

        NavigationManager.NavigateTo($"/editCostumerMasterData/{InvoiceSupplierOrHolderDto.Number}");

        StateHasChanged();
        return Task.CompletedTask;
    }

}