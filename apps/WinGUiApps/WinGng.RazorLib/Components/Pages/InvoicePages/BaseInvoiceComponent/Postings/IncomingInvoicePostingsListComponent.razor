
@if (IncomingInvoiceOverviewDto is not null)
{
    <RadzenDataGrid AllowFiltering="true"
                    AllowPaging="true"
                    AllowSorting="true"
                    FilterPopupRenderMode="PopupRenderMode.OnDemand"
                    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    PageSize="15"
                    PagerHorizontalAlign="HorizontalAlign.Center"
                    AllowColumnResize="true"
                    ShowPagingSummary="true"
                    PagingSummaryFormat="@Localizer["PagingSummaryFormat"]"
                    Data="@IncomingInvoiceOverviewDto?.ListAllPostings" Density="Density.Compact" AllowAlternatingRows="false">
        <Columns>
            <RadzenDataGridColumn Property="@nameof(IncomingInvoicePostingDto.GridPosition)" Title="@Localizer["Position"]">
                <FooterTemplate>
                    <b>Total</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(IncomingInvoicePostingDto.Schema)" Title="@Localizer["Type"]"/>
            <RadzenDataGridColumn Property="@nameof(IncomingInvoicePostingDto.Number)" Title="@Localizer["ArticleNumber"]"/>
            <RadzenDataGridColumn Property="@nameof(IncomingInvoicePostingDto.Description)" Title="@Localizer["Description"]"/>
            <RadzenDataGridColumn Property="@nameof(IncomingInvoicePostingDto.TotalNetto)" Title="@Localizer["TotalNet"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", IncomingInvoiceOverviewDto?.ListAllPostings?.Sum(o => o.TotalNetto))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            @if (IncomingInvoiceOverviewDto?.ListAllPostings.Sum(o => o.TotalDiscount) != decimal.Zero)
            {
                <RadzenDataGridColumn Title="@Localizer["TotalDiscount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                    <Template >
                        @if(context.Schema.IsPo)
                        {
                        @context.TotalDiscount
                        }
                        else
                        {
                        @string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", decimal.Zero)
                        }
                    </Template>
                    <FooterTemplate>
                        <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", IncomingInvoiceOverviewDto?.ListAllPostings.Where(o => o.Schema.IsPo).Sum(o => o.TotalDiscount))</b>
                    </FooterTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="@Localizer["TotalNetWithDiscount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right"> <Template >
                        @if(context.Schema.IsPo)
                        {
                        @context.TotalNettoWithDiscount
                        }
                        else
                        {
                        @string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", decimal.Zero)
                        }
                    </Template>
                    <FooterTemplate>
                        <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", IncomingInvoiceOverviewDto?.ListAllPostings.Where(o=> o.Schema.IsPo).Sum(o => o.TotalNettoWithDiscount))</b>
                    </FooterTemplate>
                </RadzenDataGridColumn>
            }
            <RadzenDataGridColumn Property="@nameof(IncomingInvoicePostingDto.TotalGross)" Title="@Localizer["TotalGross"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", IncomingInvoiceOverviewDto?.ListAllPostings?.Sum(o => o.TotalGross))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn  Title="@Localizer["Percent"]" FormatString="{0:F4}" TextAlign="TextAlign.Right" >
                <Template>
                    @context.Tax.Vat
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Title="@Localizer["TaxAmount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <Template>
                    @context.Tax.VatAmount
                </Template>
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", IncomingInvoiceOverviewDto?.ListAllPostings.Sum(o => o.Tax.VatAmount))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(IncomingInvoicePostingDto.FibuAccount)" Title="@Localizer["Account"]"/>
            <RadzenDataGridColumn Property="@nameof(IncomingInvoicePostingDto.LsNumber)" Title="@Localizer["DeliveryNoteNumber"]"/>
        </Columns>
    </RadzenDataGrid>
}

@code {
    [Parameter] public  IncomingInvoiceOverviewDto? IncomingInvoiceOverviewDto { get; set; }
}