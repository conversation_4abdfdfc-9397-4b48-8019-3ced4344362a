
@if (OutgoingInvoiceOverviewDto is not null)
{
    <RadzenDataGrid AllowFiltering="true"
                    AllowPaging="true"
                    AllowSorting="true"
                    FilterPopupRenderMode="PopupRenderMode.OnDemand"
                    FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                    PageSize="15"
                    AllowColumnResize="true"
                    PagerHorizontalAlign="HorizontalAlign.Center"
                    ShowPagingSummary="true"
                    PagingSummaryFormat="@Localizer["PagingSummaryFormat"]"
                    Data="@OutgoingInvoiceOverviewDto?.ListAllPostings" Density="Density.Compact" AllowAlternatingRows="false">
        <Columns>
            <RadzenDataGridColumn Property="@nameof(OutgoingInvoicePostingDto.GridPosition)" Title="@Localizer["Position"]">
                <FooterTemplate>
                    <b>Total</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(OutgoingInvoicePostingDto.Schema)" Title="@Localizer["Type"]"/>
            <RadzenDataGridColumn Property="@nameof(OutgoingInvoicePostingDto.Number)" Title="@Localizer["ArticleNumber"]"/>
            <RadzenDataGridColumn Property="@nameof(OutgoingInvoicePostingDto.Description)" Title="@Localizer["Description"]"/>
            <RadzenDataGridColumn Property="@nameof(OutgoingInvoicePostingDto.TotalNetto)" Title="@Localizer["TotalNet"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", OutgoingInvoiceOverviewDto?.ListAllPostings?.Sum(o => o.TotalNetto))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            @if (OutgoingInvoiceOverviewDto?.ListAllPostings.Count(o => o.TotalDiscount != decimal.Zero) > 0)
            {
                <RadzenDataGridColumn Title="@Localizer["TotalDiscount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                    <Template >
                        @if(context.Schema.IsPo)
                        {
                            @context.TotalDiscount
                        }
                        else
                        {
                            @string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", decimal.Zero)
                        }
                    </Template>
                    <FooterTemplate>
                        <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", OutgoingInvoiceOverviewDto?.ListAllPostings.Where(o => o.Schema.IsPo).Sum(o => o.TotalDiscount))</b>
                    </FooterTemplate>
                </RadzenDataGridColumn>
                <RadzenDataGridColumn Title="@Localizer["TotalNetWithDiscount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                    <Template >
                        @if (context.Schema.IsPo)
                        {
                            @context.TotalNettoWithDiscount
                        }
                        else
                        {
                            @string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", decimal.Zero)
                        }
                    </Template>
                    <FooterTemplate>
                        <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", OutgoingInvoiceOverviewDto?.ListAllPostings.Where(o => o.Schema.IsPo).Sum(o => o.TotalNettoWithDiscount))</b>
                    </FooterTemplate>
                </RadzenDataGridColumn>
            }
            <RadzenDataGridColumn Property="@nameof(OutgoingInvoicePostingDto.TotalGross)" Title="@Localizer["TotalGross"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", OutgoingInvoiceOverviewDto?.ListAllPostings.Sum(o => o.TotalGross))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn  Title="@Localizer["Percent"]" FormatString="{0:F4}" TextAlign="TextAlign.Right" >
                <Template>
                    @context.Tax.Vat
                </Template>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Title="@Localizer["TaxAmount"]" FormatString="{0:F4}" TextAlign="TextAlign.Right">
                <Template>
                    @context.Tax.VatAmount
                </Template>
                <FooterTemplate>
                    <b>@string.Format(new System.Globalization.CultureInfo("de-De"), "{0:F4}", OutgoingInvoiceOverviewDto?.ListAllPostings.Sum(o => o.Tax.VatAmount))</b>
                </FooterTemplate>
            </RadzenDataGridColumn>
            <RadzenDataGridColumn Property="@nameof(OutgoingInvoicePostingDto.FibuAccount)" Title="@Localizer["Account"]"/>
            <RadzenDataGridColumn Property="@nameof(OutgoingInvoicePostingDto.LsNumber)" Title="@Localizer["DeliveryNoteNumber"]"/>
        </Columns>
    </RadzenDataGrid>
}

@code {
    [Parameter] public  OutgoingInvoiceOverviewDto? OutgoingInvoiceOverviewDto { get; set; }
}