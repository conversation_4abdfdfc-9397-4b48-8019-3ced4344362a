@page "/ProcessOrderEdit/{ProcessOrderNumber:long}"

@using System.Collections.ObjectModel
@using Commons.ComboBoxs
@using MediatR
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using wingLager.application.ComboBoxs
@using wingLager.application.LindeQueue.CreateQueueEntry
@using wingLager.application.PaletLagers
@using wingLager.application.PrintableDtos
@using wingLager.application.ProcessArticles
@using wingLager.application.ProcessOrders.Headers
@using wingLager.application.SearchParams
@using wingLager.application.WarehouseManagement.Queries
@using wingLager.domain.ComboBoxs
@using wingLager.domain.PaletLagers
@using wingLager.domain.ProcessArticles
@using wingLager.domain.ProcessOrders
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Pages.Controlling.Components.Prints
@using WinGng.RazorLib.Components.Pages.Controlling.Dialogs
@using wingPrinterListLabel.application.Contracts
@using wingPrinterListLabel.application.Printers
@using wingPrinterListLabel.application.ProcessOrders.Positions
@using wingPrinterListLabel.application.RepositoryItems
@using DragEventArgs = Microsoft.AspNetCore.Components.Web.DragEventArgs
@using wingPrinterListLabel.domain

@inherits BasePage

@inject DialogService DialogService
@inject SearchParametersService SearchParametersService
@inject ISender Sender
@inject NavigationManager NavigationManager
@inject IJSRuntime JsRuntime
@inject IApplicationPath ApplicationPath

@inject NotificationService NotificationService
@inject IKundenService KundenService

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="SearchAllOrderPos"
                  Option="_options"
                  DialogService="DialogService"/>
<LoadingIndicator @ref="_loadingPrintIndicator"
                  DoLoadDataCallback="PrintPaletteOrSack"
                  Option="_printOptions"
                  DialogService="DialogService"/>
<LoadingIndicator @ref="_loadingPdfViewIndicator"
                  DoLoadDataCallback="ShowPrintView"
                  Option="_printPdfViewOptions"
                  DialogService="DialogService"/>
<LoadingIndicator @ref="_loadingDeleteOrder"
                  DoLoadDataCallback="Delete"
                  Option="_printPdfViewOptions"
                  DialogService="DialogService"/>
<LoadingIndicator @ref="_loadingSaveOrder"
                  DoLoadDataCallback="Update"
                  Option="_printPdfViewOptions"
                  DialogService="DialogService"/>

<RadzenContent Container="main">
    <ChildContent>

        <RadzenRow>
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text="Prozessauftrag">
            </RadzenHeading>

        </RadzenRow>

        <RadzenColumn SizeMD=12 Style="min-width: 90%">
            <RadzenTemplateForm TItem="ProcessOrderHeader" Data="@_processOrderHeader"
                                Visible="@(_processOrderHeader.Id is not 0)">
                <RadzenRow Gap="1rem" style="margin-bottom:1rem;" AlignItems="AlignItems.Center">
                    <RadzenColumn SizeMD="6">
                        <RadzenStack Gap="1rem" style="margin-bottom:1rem;" Orientation="Orientation.Horizontal"
                                     AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Start">
                            <RadzenButton Icon="arrow_back" Click="SearchParametersService.NavigateToReturn"
                                          ButtonStyle="ButtonStyle.Primary"/>
                            <RadzenButton Icon="save" Click="UpdateButton" ButtonStyle="ButtonStyle.Primary"/>
                            <RadzenButton Icon="refresh" Click="SearchAllOrderPosButton"
                                          ButtonStyle="ButtonStyle.Primary"/>
                        </RadzenStack>
                    </RadzenColumn>
                    <RadzenColumn SizeMD="6">
                        <RadzenStack Gap="1rem" style="margin-bottom:1rem; width:100%"
                                     Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center"
                                     JustifyContent="JustifyContent.End">
                            <RadzenButton Icon="delete" Click="DeleteButton" ButtonStyle="ButtonStyle.Primary"/>
                        </RadzenStack>
                    </RadzenColumn>
                </RadzenRow>
                <RadzenStack>
                    <RadzenRow>
                        <RadzenColumn Size="12" SizeMD="12" Class="rz-p-4 rz-border-radius-1"
                                      Style="border: var(--rz-grid-cell-border);background-color: white">
                            <RadzenText TextStyle="TextStyle.Subtitle1">Produktionsdaten</RadzenText>
                            <RadzenRow Gap="1rem">
                                <RadzenColumn>
                                    <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-2 rz-my-0"
                                                Style="color: var(--rz-text-tertiary-color);width: 100%">@Localizer["OrderNumber"] 
                                    </RadzenText>
                                    <RadzenText Style="width: 100%" TextStyle="TextStyle.Body1"
                                                Class="rz-text-truncate">
                                        <b>@_processOrderHeader.Number</b>
                                    </RadzenText>
                                </RadzenColumn>
                                <RadzenColumn>
                                    <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-mb-0"
                                                Style="color: var(--rz-text-tertiary-color);width: 100%">Beschreibung
                                    </RadzenText>
                                    <RadzenTextBox Style="width: 100%" Class="rz-text-truncate"
                                                   @bind-Value="_processOrderHeader.Description"
                                                   Placeholder="@_processOrderHeader.Description"/>
                                </RadzenColumn>
                            </RadzenRow>
                            <RadzenRow Gap="1rem">
                                <RadzenColumn Size="12" SizeMD="6">
                                    <RadzenStack Orientation="Orientation.Vertical">
                                        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-mb-0"
                                                    Style="color: var(--rz-text-tertiary-color);">Start
                                        </RadzenText>
                                        <RadzenDatePicker @bind-Value="_processOrderHeader.BeginnDateTime"
                                                          DateFormat="d" Class="rz-text-truncate"/>
                                        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-mb-0"
                                                    Style="color: var(--rz-text-tertiary-color);">Gesamt Gewicht (kg)
                                        </RadzenText>
                                        <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate"
                                                    Text="@TotalWeightFromAllPosition.ToString("N0")"/>
                                    </RadzenStack>
                                </RadzenColumn>
                                <RadzenColumn Size="12" SizeMD="6">
                                    <RadzenStack Orientation="Orientation.Vertical">
                                        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0"
                                                    Style="color: var(--rz-text-tertiary-color);">Chargennummer
                                        </RadzenText>
                                        <RadzenTextBox Style="width: 100%" Class="rz-text-truncate"
                                                       ReadOnly="OneWasPrinted"
                                                       @bind-Value="ChargeNummerTemplate"
                                                       Placeholder="@ChargeNummerTemplate"/>
                                        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0"
                                                    Style="color: var(--rz-text-tertiary-color);">Erstellungsdatum
                                        </RadzenText>
                                        <RadzenText TextStyle="TextStyle.Body1" Class="rz-text-truncate">
                                            <b>@_processOrderHeader.CreatedAt</b>
                                        </RadzenText>
                                    </RadzenStack>
                                </RadzenColumn>
                            </RadzenRow>
                            <RadzenRow Gap="1rem">
                                <RadzenColumn Size="12" SizeMD="6">
                                    <RadzenStack Orientation="Orientation.Vertical">
                                        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0"
                                                    Style="color: var(--rz-text-tertiary-color);"
                                                    Text="Drucker für Sack Etiketten"/>
                                        <RadzenDropDown
                                            @bind-Value="@SearchParametersService.ProcessOrderEditParam.SelectedPrinterForSack"
                                            Data="@_possiblePrinter" AllowClear="true"/>
                                    </RadzenStack>
                                </RadzenColumn>
                                <RadzenColumn Size="12" SizeMD="6">
                                    <RadzenStack Orientation="Orientation.Vertical">
                                        <RadzenText TextStyle="TextStyle.Overline" Class="rz-mt-4 rz-my-0"
                                                    Style="color: var(--rz-text-tertiary-color);"
                                                    Text="Drucker für Paletten Etiketten"/>
                                        <RadzenDropDown
                                            @bind-Value="@SearchParametersService.ProcessOrderEditParam.SelectedPrinterForPalett"
                                            Data="@_possiblePrinter" AllowClear="true"/>
                                    </RadzenStack>
                                </RadzenColumn>
                            </RadzenRow>
                        </RadzenColumn>
                    </RadzenRow>

                    <RadzenCard Variant="Variant.Filled">
                        <RadzenText TextStyle="TextStyle.Subtitle1">Positionen</RadzenText>
                        <RadzenDataGrid @ref="_grid"
                                        Data="@ProcessOrderPositions"
                                        TItem="ProcessOrderPosition"
                                        EditMode="DataGridEditMode.Single"
                                        CellClick="@OnCellClick"
                                        RowRender="@RowRender"
                                        AllowColumnResize="true"
                                        AllowAlternatingRows="false"
                                        AllowSorting="true"
                                        SelectionMode="DataGridSelectionMode.Single">
                            <Columns>
                                <RadzenDataGridColumn Width="15%" TItem="ProcessOrderPosition" Property="OrderNumber"
                                                      Title="Auftragsnummer"/>
                                <RadzenDataGridColumn TItem="ProcessOrderPosition"
                                                      Property="@nameof(ProcessOrderPosition.LayoutSet)"
                                                      Title="Drucklayout für Sacketikett" IsInEditMode="@IsEditing">
                                    <Template Context="orderPos">
                                        <RadzenStack Orientation="Orientation.Horizontal">
                                            <RadzenDropDown @bind-Value="@orderPos.LayoutSet"
                                                            Data="@_allLayoutCanUseKeyToDescription"
                                                            TextProperty="@nameof(CustomizedRepositoryItem.UIName)"
                                                            TValue="@string"
                                                            ValueProperty="InternalID"
                                                            AllowClear="true"
                                                            AllowFiltering="true"
                                                            InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Drucklayout auswählen" } })"
                                                            Placeholder="Drucklayout auswählen"
                                                            Style="width:90%"/>
                                            <RadzenButton Icon="preview"
                                                          Click="@(() => ShowPrintViewWithLoading(orderPos, false))"
                                                          ButtonStyle="ButtonStyle.Primary"
                                                          Visible="@(!string.IsNullOrWhiteSpace(orderPos.LayoutSet))"/>
                                        </RadzenStack>
                                    </Template>
                                </RadzenDataGridColumn>
                                <RadzenDataGridColumn TItem="ProcessOrderPosition"
                                                      Property="@nameof(ProcessOrderPosition.LayoutPet)"
                                                      Title="Druckformat Palettenetikett" IsInEditMode="@IsEditing">
                                    <Template Context="orderPos">
                                        <RadzenStack Orientation="Orientation.Horizontal">
                                            <RadzenDropDown ref="@_editor" @bind-Value="@orderPos.LayoutPet"
                                                            Data="@_allLayoutCanUseKeyToDescription"
                                                            TextProperty="@nameof(CustomizedRepositoryItem.UIName)"
                                                            TValue="@string"
                                                            ValueProperty="InternalID"
                                                            AllowClear="true"
                                                            AllowFiltering="true"
                                                            InputAttributes="@(new Dictionary<string, object>() { { "aria-label", "Drucklayout auswählen" } })"
                                                            Placeholder="Drucklayout auswählen"
                                                            Style="width:90%"/>
                                            <RadzenButton Icon="preview"
                                                          Click="@(() => ShowPrintViewWithLoading(orderPos, true))"
                                                          ButtonStyle="ButtonStyle.Primary"
                                                          Visible="@(!string.IsNullOrWhiteSpace(orderPos.LayoutPet))"/>
                                        </RadzenStack>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn Width="8%" TItem="ProcessOrderPosition" Title="Druck"
                                                      TextAlign="TextAlign.Center">
                                    <Template Context="processOrderPosition">
                                        <RadzenButton Icon="@GetButtonIconForPrintPos(processOrderPosition)"
                                                      ButtonStyle="@(GetButtonStyleForPrintPos(processOrderPosition))"
                                                      Click="@(() => PrintProcessPosOrSack(processOrderPosition, PrintModeForProcessPosition.All))"
                                                      Visible="@(!string.IsNullOrWhiteSpace(processOrderPosition.LayoutPet))"/>
                                    </Template>
                                </RadzenDataGridColumn>

                                <RadzenDataGridColumn Width="8%" TItem="ProcessOrderPosition" Title="Info"
                                                      TextAlign="TextAlign.Center">
                                    <Template Context="processOrderPosition">
                                        <RadzenButton Click="@(_ => TogglePopupForPosAsync(processOrderPosition))"
                                                      Icon="info"/>
                                    </Template>
                                </RadzenDataGridColumn>
                            </Columns>

                            <Template Context="processOrderPosition">
                                <RadzenDataGrid AllowFiltering="true"
                                                AllowPaging="false"
                                                TItem="PaletLager"
                                                AllowSorting="true"
                                                Data="@processOrderPosition.PaletLager">
                                    <Columns>
                                        <RadzenDataGridColumn TItem="PaletLager"
                                                              Property="NVENr"
                                                              Title="NVENr"/>
                                        <RadzenDataGridColumn TItem="PaletLager"
                                                              Property="AnzSack"
                                                              Title="Anzahl Sack"/>
                                        <RadzenDataGridColumn TItem="PaletLager"
                                                              Property="ChargNr"
                                                              Title="Chargennummer"/>
                                        <RadzenDataGridColumn TItem="PaletLager"
                                                              Property="JobDatum"
                                                              Title="JobDatum">
                                            <Template Context="data">
                                                @(data.JobDatum == DateTime.MinValue ? "" : data.JobDatum.ToString("dd.MM.yyyy HH:mm"))
                                            </Template>
                                        </RadzenDataGridColumn>
                                        <RadzenDataGridColumn TItem="PaletLager" Title="Druckfortschritt"
                                                              TextAlign="TextAlign.Left" Filterable="false">
                                            <Template Context="paletLager">
                                                <RadzenText>
                                                    Palette: @(paletLager.IsLagerSuccessPrinted() ? 100 : @paletLager.IsLagerStartedPrinted() ? 25 : 0) %<br>
                                                </RadzenText>
                                                <RadzenText>
                                                    Säcke: @paletLager.PercentAllSackPrinted() %
                                                </RadzenText>
                                            </Template>
                                        </RadzenDataGridColumn>
                                        <RadzenDataGridColumn TItem="PaletLager" Title="Druck" Width="10%"
                                                              TextAlign="TextAlign.Center" Filterable="false">
                                            <Template Context="paletLager">
                                                <RadzenButton Text="P"
                                                              ButtonStyle="@(GetButtonStyleForPrintPallet(paletLager))"
                                                              MouseEnter="@(args => ShowTooltip(args, "Palette Drucken", new TooltipOptions() { Position = TooltipPosition.Left }))"
                                                              Click="@(() => PrintPaletteOrSack(processOrderPosition, paletLager, PrintModeForProcessPosition.Palette))"
                                                              Visible="@(!string.IsNullOrWhiteSpace(processOrderPosition.LayoutPet))"/>
                                                <RadzenButton Text="S"
                                                              ButtonStyle="@(GetButtonStyleForPrintPallet(paletLager))"
                                                              MouseEnter="@(args => ShowTooltip(args, "Säcke Drucken", new TooltipOptions() { Position = TooltipPosition.Left }))"
                                                              Click="@(() => PrintPaletteOrSack(processOrderPosition, paletLager, PrintModeForProcessPosition.Sack))"
                                                              Visible="@(!string.IsNullOrWhiteSpace(processOrderPosition.LayoutPet))"/>

                                            </Template>
                                        </RadzenDataGridColumn>
                                        <RadzenDataGridColumn TItem="PaletLager" Title="Einlagern" Width="10%"
                                                              TextAlign="TextAlign.Center" Filterable="false">
                                            <Template Context="paletLager">
                                                <RadzenButton Text="E" Click="@(() => AddToLindeQueue(paletLager))"
                                                              ButtonStyle="@(GetButtonStyleForLindeQueue(paletLager))"
                                                              Visible="@(!string.IsNullOrWhiteSpace(processOrderPosition.LayoutPet))"/>
                                            </Template>
                                        </RadzenDataGridColumn>
                                    </Columns>
                                </RadzenDataGrid>
                            </Template>
                        </RadzenDataGrid>
                    </RadzenCard>
                </RadzenStack>
            </RadzenTemplateForm>
        </RadzenColumn>
    </ChildContent>
</RadzenContent>

@code
{
    [Parameter] public long ProcessOrderNumber { get; set; }

    readonly LoadingIndicatorOptions _options = new(true, false, false, false);
    readonly LoadingIndicatorOptions _printOptions = new(false, false, false, false);
    readonly LoadingIndicatorOptions _printPdfViewOptions = new(false, false, false, false);
    LoadingIndicator? _loadingIndicator = default!;
    LoadingIndicator? _loadingPrintIndicator = default!;
    LoadingIndicator? _loadingPdfViewIndicator = default!;
    LoadingIndicator? _loadingDeleteOrder = default!;
    LoadingIndicator? _loadingSaveOrder = default!;

    private ProcessOrderHeader _processOrderHeader = new();
    private IEnumerable<ProcessArticle> _allProcessArticles = [];
    private List<string> _possiblePrinter = [];
    ObservableCollection<ProcessOrderPosition> ProcessOrderPositions { get; set; } = new ObservableCollection<ProcessOrderPosition>();

    RadzenDataGrid<ProcessOrderPosition>? _grid;

    private decimal TotalWeightFromAllPosition { get; set; } = 0;
    private string ChargeNummerTemplate { get; set; } = string.Empty;
    private bool OneWasPrinted { get; set; } = false;

    IEnumerable<Combobox> _palettType { get; set; } = [];
    IEnumerable<Combobox> _etikettType { get; set; } = [];
    IEnumerable<Combobox> _palettenRezept { get; set; } = [];

    protected async Task SearchAllOrderPosButton()
    {
        if (_loadingIndicator is not null)
            await _loadingIndicator.Run();
    }

    protected async Task SearchAllOrderPos()
    {
        StateHasChanged();
        try
        {
            await LoadDataAsync();

            StateHasChanged();
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }

        if (!_editorFocused && _editor is not null)
        {
            _editorFocused = true;

            try
            {
                await _editor.FocusAsync();
            }
            catch
            {
                //
            }
        }
    }

    private async Task LoadDataAsync()
    {
        if (ProcessOrderNumber == 0)
            return;

        try
        {
            _possiblePrinter = await Sender.Send(new AllPrinterDescriptionQuery());
            SearchParametersService.ProcessOrderEditParam.SelectedPrinterForPalett ??= _possiblePrinter.FirstOrDefault();
            SearchParametersService.ProcessOrderEditParam.SelectedPrinterForSack ??= _possiblePrinter.FirstOrDefault();

            await Task.Delay(100);

            var processOrderHeaders = await Sender.Send(new ProcessOrdersHeadersQuery(new OrderProcessHeaderSearchParam() { Number = ProcessOrderNumber, WithAllData = true }), CancellationToken.None);

            _processOrderHeader = processOrderHeaders.FirstOrDefault() ?? new ProcessOrderHeader();

            var allUsedProcessArticleIds = _processOrderHeader.Positionen?.Select(p => p.ProcessArticleId)?.Distinct() ?? [];
            _allProcessArticles = await Sender.Send(new ProcessArticlesQuery(new ProcessArticleSearchParam() { Ids = allUsedProcessArticleIds })) ?? [];

            var processOrderPositions = _processOrderHeader.Positionen ?? new List<ProcessOrderPosition>();
            ProcessOrderPositions = new ObservableCollection<ProcessOrderPosition>(processOrderPositions);
            TotalWeightFromAllPosition = _processOrderHeader.Positionen?.Sum(p => p.WeightInKg) ?? 0;

            ChargeNummerTemplate = processOrderPositions.FirstOrDefault()?.PaletLager.FirstOrDefault()?.ChargNr ?? $"{DateTime.Now:yyMM}";
            OneWasPrinted = processOrderPositions.Any(p => p.PaletLager.Any(plager => !string.IsNullOrWhiteSpace(plager.ChargNr)));

            _etikettType = await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.EtikettTyp));
            _palettType = await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.PalettenTyp));
            _palettenRezept = await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.PalettenReszept));

            await ReloadLayoutItems();
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    private async Task LoadPaletLagersDataAsync()
    {
        if (_processOrderHeader.Id == 0)
            return;
        var allPaletLagers = await Sender.Send(new GetPaletLagerEntriesFromOrderHeaderIdQuery(_processOrderHeader.Id));

        var paletLagers = allPaletLagers.ToList();
        if (!paletLagers.Any())
            return;

        foreach (var processOrderPosition in ProcessOrderPositions)
        {
            var palets = paletLagers.Where(p => p.ProcessOrderPositionId == processOrderPosition.Id).ToList();
            if (palets.Any())
                processOrderPosition.PaletLager = new ObservableCollection<PaletLager>(palets);
        }

        OneWasPrinted = ProcessOrderPositions.Any(p =>
            p.PaletLager.Any(palet => !string.IsNullOrWhiteSpace(palet.ChargNr)));

        await ReloadLayoutItems();
    }

    private async Task UpdateButton()
    {
        if (_loadingSaveOrder is null)
            return;

        await _loadingSaveOrder.Run();
    }

    private async Task Update()
    {
        try
        {
            _processOrderHeader.Positionen = ProcessOrderPositions.ToList();
            await Sender.Send(new ModifyProcessOrderHeaderCommand(_processOrderHeader), CancellationToken.None);
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }

        StateHasChanged();
    }

    private async Task DeleteButton()
    {
        if (_loadingDeleteOrder is null)
            return;

        await _loadingDeleteOrder.Run();
    }

    private async Task Delete()
    {
        try
        {
            await Sender.Send(new DeleteProcessOrderHeaderCommand(_processOrderHeader.Id), CancellationToken.None);

            _processOrderHeader = new ProcessOrderHeader();
            NavigationManager.NavigateTo($"/ProcessControllingSearch");
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }

        StateHasChanged();
    }

    ProcessOrderPosition? SelectedProcessOrderPositionForPrint { get; set; } = null;
    List<PaletLager>? SelectedPaletLagers { get; set; } = null;
    PrintModeForProcessPosition PrintModeForProcessPosition { get; set; } = PrintModeForProcessPosition.All;
    bool OnlyForOnePallet { get; set; } = false;
    bool PrintWasStarted { get; set; } = false;
    bool PrintAgain { get; set; } = false;
    short PrintPaletteOrSackCounter { get; set; } = 0;

    private async Task PrintProcessPosOrSack(ProcessOrderPosition? processOrderPosition, PrintModeForProcessPosition printModeForProcessPosition)
    {
        if (_loadingPrintIndicator is null || processOrderPosition is null)
            return;

        SelectedProcessOrderPositionForPrint = processOrderPosition;
        SelectedPaletLagers = processOrderPosition.PaletLager.ToList();
        PrintModeForProcessPosition = printModeForProcessPosition;
        OnlyForOnePallet = false;
        PrintAgain = SelectedProcessOrderPositionForPrint!.IsAllPalletPrinted() && SelectedProcessOrderPositionForPrint!.IsAllSackPrinted();

        await _loadingPrintIndicator.Run();

        SelectedPaletLagers = null;
    }

    private async Task PrintPaletteOrSack(ProcessOrderPosition? processOrderPosition, PaletLager? paletLager, PrintModeForProcessPosition printModeForProcessPosition)
    {
        if (_loadingPrintIndicator is null || paletLager is null)
            return;

        if (processOrderPosition is null)
            return;

        SelectedProcessOrderPositionForPrint = processOrderPosition;
        SelectedPaletLagers = [paletLager];
        PrintModeForProcessPosition = printModeForProcessPosition;
        OnlyForOnePallet = true;
        PrintAgain = SelectedPaletLagers.First().IsLagerSuccessPrinted();

        await _loadingPrintIndicator.Run();

        SelectedPaletLagers = null;
    }

    private async Task PrintPaletteOrSack()
    {
        if (SelectedPaletLagers is null || SelectedPaletLagers.Count == 0)
            return;

        if (PrintWasStarted)
            return;

        try
        {
            var selectedPaletteIdToSackIndexs = new Dictionary<long, PrintCountPalletAndSack>();

            foreach (var selectedPaletLager in SelectedPaletLagers)
            {
                if (selectedPaletteIdToSackIndexs.ContainsKey(selectedPaletLager.Id))
                    continue;

                selectedPaletteIdToSackIndexs.Add(selectedPaletLager.Id, new PrintCountPalletAndSack(1, selectedPaletLager.AnzSack ?? 0));
            }

            DialogService.Close();
            var param = new PrintPalletWithSackComponent.PrintPalletWithSackModel()
            {
                PalletCount = 1,
                SackCount = selectedPaletteIdToSackIndexs.Sum(kv => kv.Value.NumberOfCopyForSackCount),
                DoPrint = false,
            };
            
            if (OnlyForOnePallet)
            {
                param.ShowPalletCount = PrintModeForProcessPosition == PrintModeForProcessPosition.Palette;
                param.ShowSackCount = PrintModeForProcessPosition == PrintModeForProcessPosition.Sack;
        
            }

            if (SelectedProcessOrderPositionForPrint?.ProcessArticleId is not null)
            {
                var processArticle = GetProzessArticle(SelectedProcessOrderPositionForPrint.ProcessArticleId);
                var customer = processArticle.CustomerNumber == 0 ? null : await KundenService.GetCustomerDtoOverNumber(processArticle.CustomerNumber);
                param.PalletCount = processArticle.NumberOfPalletEtikettToPrint ?? 1;
                param.Etikettentyp = _etikettType.FirstOrDefault(p => p.Key == processArticle.EtikTyp)?.Value ?? string.Empty;
                param.Palettentyp = _palettType.FirstOrDefault(p => p.Key == processArticle.PalettenTyp)?.Value ?? string.Empty;
                param.Palettiererrezept = _palettenRezept.FirstOrDefault(p => p.Key == processArticle.PalettiererRezept)?.Value ?? string.Empty;
                param.Note = processArticle.Note ?? string.Empty;
                param.CustomerInformation = customer?.FullCustomerInformation ?? string.Empty;
            }

            var title = PrintAgain
                ? $"Wiederholungsdruck ({SelectedProcessOrderPositionForPrint?.OrderNumber})"
                : $"Drucken ({SelectedProcessOrderPositionForPrint?.OrderNumber})";
            await DialogService.OpenAsync(title, ds =>
                    @<PrintPalletWithSackComponent PrintParameter="@param"
                                                   DialogService="DialogService"/>
                ,
                new DialogOptions()
                {
                    Width = "50%",
                    Height = "70%"
                });

            if (param.DoPrint)
                await DoPrintPaletteOrSack(param);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    private async Task DoPrintPaletteOrSack(PrintPalletWithSackComponent.PrintPalletWithSackModel printParam)
    {
        if (SelectedPaletLagers is null || SelectedPaletLagers.Count == 0)
            return;

        if (PrintWasStarted)
            return;

        PrintWasStarted = true;

        try
        {
            if (_loadingPrintIndicator is not null)
                await _loadingPrintIndicator.ShowLoadingDialog();
            var processOrderPositionId = SelectedPaletLagers.First().ProcessOrderPositionId;

            foreach (var selectedPaletLager in SelectedPaletLagers)
            {
                if (PrintAgain ||
                    !selectedPaletLager.IsLagerSuccessPrinted() ||
                    !selectedPaletLager.IsAllSackPrinted())
                    await Sender.Send(new SetFlagPrintedStartPaletLagerCommand(selectedPaletLager.Id));
            }

            var printPosPaletteAsPdfCommand = new PrintProcessPositionCommand(processOrderPositionId,
                SearchParametersService.ProcessOrderEditParam.SelectedPrinterForPalett ?? "",
                SearchParametersService.ProcessOrderEditParam.SelectedPrinterForSack ?? "",
                new PrintCountPalletAndSack(printParam.PalletCount, printParam.SackCount),
                OnlyForOnePallet ? SelectedPaletLagers.First().Id : 0,
                ApplicationPath.GetPath(),
                "",
                PrintModeForProcessPosition,
                ApplicationPath.IAmBlazorServer(),
                ChargeNummerTemplate,
                PrintAgain);


            await Sender.Send(printPosPaletteAsPdfCommand, CancellationToken.None);
            await LoadPaletLagersDataAsync();

            if (_loadingPrintIndicator is not null)
                await _loadingPrintIndicator.HideDialog();

            if (!OnlyForOnePallet)
            {
                DialogService.Close();
                await Task.Delay(500);

                var nextPosition = ProcessOrderPositions
                    .SkipWhile(p => p.Id != processOrderPositionId)
                    .Skip(1)
                    .FirstOrDefault();

                if (PrintAgain &&
                    nextPosition is not null &&
                    nextPosition.IsAllPalletPrinted() &&
                    nextPosition.IsAllSackPrinted())
                    return;

                var printNextPosition = false;
                var printCurrentPositionAgain = false;
                var isLastPos = nextPosition is null;
                var result = await DialogService.Confirm($"Ist der Auftrag {SelectedProcessOrderPositionForPrint?.OrderNumber} abgeschlossen?",
                    "Information",
                    new ConfirmOptions()
                    {
                        OkButtonText = isLastPos ? "Abschließen" : "Weiter",
                        CancelButtonText = "Erneut Drucken"
                    });

                if (nextPosition is null)
                {
                    printCurrentPositionAgain = result is false;
                    printNextPosition = false;
                }
                else
                {
                    printNextPosition = result is true;
                    printCurrentPositionAgain = result is false;
                }

                if (printNextPosition)
                {
                    PrintWasStarted = false;
                    if (nextPosition is not null &&
                        !string.IsNullOrWhiteSpace(nextPosition.LayoutSet) &&
                        !string.IsNullOrWhiteSpace(nextPosition.LayoutPet))
                        await PrintProcessPosOrSack(nextPosition, PrintModeForProcessPosition.All);
                }
                else if (printCurrentPositionAgain)
                {
                    PrintWasStarted = false;
                    await PrintProcessPosOrSack(SelectedProcessOrderPositionForPrint, PrintModeForProcessPosition.All);
                }
            }
        }
        catch (Exception e)
        {
            PrintWasStarted = false;
            if (PrintPaletteOrSackCounter < 3)
            {
                PrintPaletteOrSackCounter++;
                await DoPrintPaletteOrSack(printParam);
            }

            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            PrintWasStarted = false;
            await LoadPaletLagersDataAsync();
        }
    }

    private IRadzenFormComponent? _editor;
    private bool _editorFocused;
    private string _columnEditing = string.Empty;
    private List<ProcessOrderPosition> _processOrderPositionToUpdate = [];

    bool IsEditing(string columnName, ProcessOrderPosition processOrderPosition)
    {
        // Comparing strings is quicker than checking the contents of a List, so let the property check fail first.
        return _columnEditing == columnName && _processOrderPositionToUpdate.Contains(processOrderPosition);
    }

    void EditRow(ProcessOrderPosition processOrderPosition)
    {
        _processOrderPositionToUpdate =
        [
            processOrderPosition
        ];
    }

    void OnCellClick(DataGridCellMouseEventArgs<ProcessOrderPosition> args)
    {
        // This sets which column is currently being edited.
        _columnEditing = args.Column.Property;
        EditRow(args.Data);
    }

    private ProcessArticle GetProzessArticle(long processArticleId)
    {
        var result = _allProcessArticles.FirstOrDefault(p => p.Id == processArticleId);
        return result ?? new ProcessArticle();
    }

    ProcessOrderPosition? SelectedProcessOrderPosition { get; set; } = null;
    bool DoPrintPalette { get; set; } = false;

    private async Task ShowPrintViewWithLoading(ProcessOrderPosition processOrderPosition, bool doPrintPalette)
    {
        if (_loadingPdfViewIndicator is null)
            return;

        SelectedProcessOrderPosition = processOrderPosition;
        DoPrintPalette = doPrintPalette;

        await _loadingPdfViewIndicator.Run();

        SelectedProcessOrderPosition = null;
    }

    private async Task ShowPrintView()
    {
        if (SelectedProcessOrderPosition is null)
            return;

        if (PrintWasStarted)
            return;

        PrintWasStarted = true;

        try
        {
            var mode = DoPrintPalette ? PrintModeForProcessPosition.Palette : PrintModeForProcessPosition.Sack;

            var printPosPaletteAsPdfCommand = new PdfAsBase64ProcessPositionCommand(SelectedProcessOrderPosition.Id,
                ApplicationPath.GetPath(),
                "",
                mode,
                ApplicationPath.IAmBlazorServer());
            var base64 = await Sender.Send(printPosPaletteAsPdfCommand, CancellationToken.None);
            if (!string.IsNullOrEmpty(base64))
                await OpenPrinter(base64);
        }
        catch (Exception e)
        {
            DialogService.Close();
            /*
            IPrintObject? dataForDesigner = null;
            var repoIdOrPath = string.Empty;
            if (SelectedProcessOrderPosition.PaletLager.Count > 0)
            {
                dataForDesigner = DoPrintPalette ? LabelPalletDto.Create(SelectedProcessOrderPosition.PaletLager.First()) : LabelBagDto.Create(SelectedProcessOrderPosition.PaletLager.First(), 1);
                repoIdOrPath = DoPrintPalette ? SelectedProcessOrderPosition.LayoutPet : SelectedProcessOrderPosition.LayoutSet;
            }

            if (dataForDesigner is null)
            {
                await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
                return;
            }

            var answer = await DialogService.Confirm($"{Environment.NewLine} {e.InnerException?.Message ?? e.Message}", "Fehler beim Öffnen der PDF", new ConfirmOptions() { OkButtonText = "Designer öffnen", CancelButtonText = "Schließen" });
            if (answer is true)
            {
                _ = Task.Run(() => { ListLabelFactory.OpenWebDesigner(dataForDesigner, repoIdOrPath); }).ConfigureAwait(false);
               
                _allLayoutCanUseKeyToDescription = [];
            } */
        }
        finally
        {
            PrintWasStarted = false;
        }
    }

    IEnumerable<CustomizedRepositoryItem> _allLayoutCanUseKeyToDescription = [];

    private async Task ReloadLayoutItems()
    {
        try
        {
            //IPrintObject objectToPrint = doPrintPalette ? LabelPalletDto.Dummy : LabelBagDto.Dummy;
            if (!_allLayoutCanUseKeyToDescription.Any())
                _allLayoutCanUseKeyToDescription = await Sender.Send(new AllRepositoryItemsFromPrintObjectQuery(LabelPalletDto.Dummy));
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }

    ProcessOrderPosition _draggedProcessOrderPosition = new();

    void RowRender(RowRenderEventArgs<ProcessOrderPosition> args)
    {
        args.Attributes.Add("title", "Ziehen Sie die Zeile zum Neuordnen");
        args.Attributes.Add("style", "cursor:grab");
        args.Attributes.Add("draggable", "true");
        args.Attributes.Add("ondragover", "event.preventDefault();event.target.closest('.rz-data-row').classList.add('my-class')");
        args.Attributes.Add("ondragleave", "event.target.closest('.rz-data-row').classList.remove('my-class')");
        args.Attributes.Add("ondragstart", EventCallback.Factory.Create<DragEventArgs>(this, () => _draggedProcessOrderPosition = args.Data));
        args.Attributes.Add("ondrop", EventCallback.Factory.Create<DragEventArgs>(this, () =>
        {
            var draggedIndex = ProcessOrderPositions.IndexOf(_draggedProcessOrderPosition);
            var droppedIndex = ProcessOrderPositions.IndexOf(args.Data);

            ProcessOrderPositions.Remove(_draggedProcessOrderPosition);
            ProcessOrderPositions.Insert(draggedIndex <= droppedIndex
                ? droppedIndex++
                : droppedIndex, _draggedProcessOrderPosition);

            JsRuntime.InvokeVoidAsync("eval", $"document.querySelector('.my-class').classList.remove('my-class')");
        }));
    }

    private async Task TogglePopupForPosAsync(ProcessOrderPosition processOrderPosition)
    {
        await DialogService.OpenAsync<ProcessPositionDetails>($"Prozessposition Informationen",
            new Dictionary<string, object>()
            {
                { "ProcessOrderPosition", processOrderPosition },
                { "ProcessArticle", GetProzessArticle(processOrderPosition.ProcessArticleId) }
            },
            new DialogOptions()
            {
                CloseDialogOnOverlayClick = true,
                Width = "80%",
                Height = "90%"
            });
    }

    private ButtonStyle GetButtonStyleForPrintPos(ProcessOrderPosition processOrderPosition)
    {
        var result = ButtonStyle.Primary;


        if (processOrderPosition.IsStartedPrinted())
            result = ButtonStyle.Warning;
        else if (processOrderPosition.IsAllPalletPrinted() && processOrderPosition.IsAllSackPrinted())
            result = ButtonStyle.Success;

        return result;
    }

    private ButtonStyle GetButtonStyleForPrintPallet(PaletLager paletLager)
    {
        var result = ButtonStyle.Primary;


        if (paletLager.IsLagerStartedPrinted())
            result = ButtonStyle.Warning;
        else if (paletLager.IsLagerSuccessPrinted() &&
                 !paletLager.IsAllSackPrinted())
            result = ButtonStyle.Warning;
        else if (paletLager.IsLagerSuccessPrinted() &&
                 paletLager.IsAllSackPrinted())
            result = ButtonStyle.Success;

        return result;
    }

    private string GetButtonIconForPrintPos(ProcessOrderPosition paletLager)
    {
        var result = "print";

        if (paletLager.IsAllPalletPrinted() && paletLager.IsAllSackPrinted())
            result = "print_add";
        else if (paletLager.IsStartedPrinted())
            result = "print_add";

        return result;
    }

    private ButtonStyle GetButtonStyleForLindeQueue(PaletLager paletLager)
    {
        var result = ButtonStyle.Primary;

        if (paletLager.FlagsWorker.IsLindeQueueCreatedFlagSet())
            result = ButtonStyle.Light;
        if (paletLager.FlagsWorker.IsLindeQueuePickFlagSet())
            result = ButtonStyle.Dark;
        if (paletLager.FlagsWorker.IsLindeQueueDropFlagSet())
            result = ButtonStyle.Info;
        if (paletLager.FlagsWorker.IsLindeQueueWaitingFlagSet())
            result = ButtonStyle.Warning;
        if (paletLager.FlagsWorker.IsLindeQueueDoneFlagSet())
            result = ButtonStyle.Success;
        if (paletLager.FlagsWorker.IsLindeQueueErrorFlagSet())
            result = ButtonStyle.Danger;

        return result;
    }

    private async Task AddToLindeQueue(PaletLager paletLager)
    {
        try
        {
            var startpos = await Sender.Send(new GetAllWepStellplatzQuery());
            if (startpos.Count != 1)
            {
                NotificationService.Notify(NotificationSeverity.Error, "Fehler", "Es wurde kein Produktionsstelplatz angelegt");
                return;
            }
            var palletTypeKey = await Sender.Send(new GetPalletTypeOfPaletLagerQuery(paletLager.Id));
            var palletTypeCombobox = (await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.PalettenTyp))).ToList().Where(c => c.Key == palletTypeKey).First();
            var stellplatz = await Sender.Send(new GetEmptyStellplatzListByPalletTypeQuery(palletTypeCombobox.Value, true));
            if (stellplatz is null || stellplatz.Count == 0)
            {
                throw new Exception($"Es konnte kein freier Stellplatz für den Palettentyp {palletTypeCombobox} gefunden werden, der vom Roboter angefahren werden kann");
            }
            var hallposition = await Sender.Send(new GetHallPositionOfStellplatzQuery(stellplatz.First().Id));
            await Sender.Send(new CreateStoringLindeQueueEntryCommand(paletLager, startpos.First().Description, hallposition));
            await Sender.Send(new SetLindeQueueWaitingFlagCommand(paletLager.Id));
            NotificationService.Notify(NotificationSeverity.Success, "Erfolg", $"Die Palette {paletLager.NVENr} wurde an den Roboter geschickt");
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
}