@page "/ProcessArticleCreationPage"
@page "/ProcessArticleEditPage/{ProcessArticleNumber:long}"


@inherits BasePage

@using Commons.ComboBoxs
@using WingCore.domain.Models
@using MediatR
@using PrinterService
@using Radzen.Blazor.Rendering
@using WingCore.application.Contract.IModels
@using WingCore.application.Contract.Services
@using wingLager.application.ComboBoxs
@using wingLager.application.PrintableDtos
@using wingLager.application.ProcessArticles
@using wingLager.domain.ComboBoxs
@using wingLager.domain.ProcessArticles
@using WinGng.RazorLib.Components.Common.LoadingPage
@using WinGng.RazorLib.Components.Pages.MasterData.Costumer
@using wingPrinterListLabel.application.Contracts
@using wingPrinterListLabel.application.RepositoryItems
@using wingPrinterListLabel.domain

@inject NavigationManager NavigationManager
@inject DialogService DialogService
@inject ISender Sender
@inject Lazy<IArticleService> ArticleService
@inject IKundenService KundenService
@inject IApplicationPath ApplicationPath
@inject IListLabelFactory ListLabelFactory


<LoadingIndicator @ref="_saveDataLoadingIndicator"
                  DoLoadDataCallback="SetProcessArticle"
                  Option="_saveLoadingIndicatorDataOptions"
                  DialogService="DialogService"/>
<LoadingIndicator @ref="_deleteDataLoadingIndicator"
                  DoLoadDataCallback="DeleteProcessArticle"
                  Option="_deleteLoadingIndicatorDataOptions"
                  DialogService="DialogService"/>


<RadzenContent Container="main">
    <ChildContent>
        <RadzenRow Visible="@(!IsOnlyShowForShowMode)">
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text="@Localizer["ProcessArticles"]">
            </RadzenHeading>

        </RadzenRow>
        <RadzenTemplateForm @ref="@TemplateForm" Data="ProcessArticle" TItem="ProcessArticle" >

            <RadzenRow Gap="1rem" style="margin-bottom:1rem;" AlignItems="AlignItems.Center" Visible="@(!IsOnlyShowForShowMode)">
                <RadzenColumn SizeMD="6">
                    <RadzenStack Gap="1rem" style="margin-bottom:1rem;" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Start">
                        <RadzenButton ButtonType="ButtonType.Button" Icon="search" Text="@Localizer["Search"]" Click="@OpenProcessArticleSearch"/>
                        @if (CanSave)
                        {
                            <RadzenButton ButtonType="ButtonType.Button" Icon="add" Text="@Localizer["New"]" Click="@ResetProcessArticle" Visible="@(ProcessArticle.Id!=0)"/>
                            <RadzenButton ButtonType="ButtonType.Button" Icon="save" Text="@Localizer["Save"]" Click="@SetProcessArticleButton"/>
                            @*<RadzenButton ButtonType="ButtonType.Button" Icon="content_copy" Text="Kopieren" Click="@CopyProcessArticle" Visible="@(ProcessArticle.Id!=0)"/>*@
                        }
                    </RadzenStack>
                </RadzenColumn>
                <RadzenColumn SizeMD="6">
                    <RadzenStack Gap="1rem" style="margin-bottom:1rem; width:100%" Orientation="Orientation.Horizontal" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.End">
                        <RadzenButton Icon="delete" Visible="@CanSave" Click="DeleteProcessArticleButton" Text="@Localizer["Delete"]" ButtonStyle="ButtonStyle.Primary"/>
                    </RadzenStack>
                </RadzenColumn>
            </RadzenRow>
            <RadzenRow Gap="1rem" class="rz-p-0 rz-p-lg-4">
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["BasicData"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem" >
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["Number"]"/>
                                                <RadzenText>
                                                    @ProcessArticle.Number
                                                    <RadzenButton Visible="@ShowOpenRelationToProcessArticle" Click=@OpenRelationToProcessArticle Icon="open_in_new" Size="ButtonSize.ExtraSmall" ButtonStyle="ButtonStyle.Light"/>
                                                </RadzenText>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["BasicArticle"]"/>
                                                <RadzenDropDownDataGrid 
                                                    readonly="@(IsOnlyShowForShowMode)"
                                                    AllowClear="true"
                                                    @bind-Value="SelectedBaseArticle"
                                                    AllowVirtualization="true"
                                                    AllowFiltering="true"
                                                    Data="@AllMainArticles"
                                                    TextProperty="@nameof(Artikel.ArtikelnrAsString)"
                                                    AllowColumnResize="true"
                                                    AllowFilteringByAllStringColumns="true"
                                                    SearchTextPlaceholder="@Localizer["SelectAMainArticle"]"
                                                    EmptyText="@Localizer["NoResults"]"
                                                    Change=@(arg => SetBasisArticle(arg))>
                                                    <Columns>
                                                        <RadzenDropDownDataGridColumn Property="@nameof(Artikel.ArtikelnrAsString)" Title="@Localizer["ArticleNumber"]" Width="10"/>
                                                        <RadzenDropDownDataGridColumn Property="@nameof(Artikel.ArtBezText1)" Title="@Localizer["Description"]" Width="40"/>
                                                        <RadzenDropDownDataGridColumn Property="@nameof(Artikel.ArtBezText2)" Title="@(Localizer["Description"] + " " + 2)" Width="40"/>
                                                    </Columns>
                                                </RadzenDropDownDataGrid>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Visible="@(CanSave)">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["CustomerData"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["Customer"]"/>
                                                <RadzenFormField Text="@Localizer["Customer"]" Variant="Variant.Outlined" Style="margin-top:-10px;width:100%;">
                                                    <Start>
                                                    </Start>
                                                    <ChildContent>
                                                        <RadzenTextBox @ref=_customerSearchTextBox
                                                                       Name="CustomerSearch"
                                                                       Value="@SelectedCustomerName"
                                                                       Placeholder="@Localizer["SearchFor"]"
                                                                       Icon="search"
                                                                       ValueChanged="@(a => OnInput(a))"

                                                                       @onkeypress="HandleKeyPress"
                                                                       @onclick="@(_ => HandleClickToSearchCustomer(_customerSearchTextBox.Element))"/>
                                                    </ChildContent>
                                                    <End>
                                                        <RadzenIcon Icon="search" IconStyle="IconStyle.Secondary"/>
                                                    </End>
                                                </RadzenFormField>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["ReferenceNumber"]"/>
                                                <RadzenFormField Variant="Variant.Outlined" Style="margin-top:-10px;width:100%;">
                                                    <ChildContent>
                                                        <RadzenTextBox @bind-Value="@ProcessArticle.CustomerReference"/>
                                                    </ChildContent>
                                                </RadzenFormField>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>
                   <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Visible="@(CanSave)">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["Bag"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["EanBag"]"/>
                                                <RadzenTextBox Name="EanSack" readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.EanSack"/>
                                                <RadzenRequiredValidator Component="EanSack"
                                                                         Text="@Localizer["EanBagIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["LabelType"]"/>
                                                <RadzenDropDown Name="Etikettentyp" readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.EtikTyp" Data="@_etikettType" TextProperty="@nameof(Combobox.Value)" ValueProperty="Key" AllowClear="true"/>
                                                <RadzenRequiredValidator Component="Etikettentyp"
                                                                         Text="@Localizer["LabelTypeIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["BagType"]"/>
                                                <RadzenDropDown readonly="@(IsOnlyShowForShowMode)" @bind-Value="@SelectedSackType" Data="@_sackType" TextProperty="@nameof(Combobox.Value)"  AllowClear="true" Change="@(v=> OnChangeValueFromSackTyp(v))"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["BagCount"]"/>
                                                <RadzenNumeric Name="Sackanzahl" readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.SackAnzahl"/>
                                                <RadzenRequiredValidator Component="Sackanzahl"
                                                                         Text="@Localizer["BagCountIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["BagContent"]"/>
                                                <RadzenNumeric Name="Sackinhalt" readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.SackInhalt"/>
                                                <RadzenRequiredValidator Component="Sackinhalt"
                                                                         Text="@Localizer["BagContentIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["PrintLayoutForBagLabel"]"/>
                                                <RadzenStack Orientation="Orientation.Horizontal" Gap="6px">
                                                    <RadzenStack Orientation="Orientation.Vertical" Gap="1rem"  Style="width:90%">
                                                        <RadzenDropDown readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.LayoutSackEtik"
                                                                        Data="@_allLayoutCanUseKeyToDescription"
                                                                        TextProperty="@nameof(CustomizedRepositoryItem.UIName)"
                                                                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                                                        TValue="@string"
                                                                        ValueProperty="InternalID"
                                                                        AllowClear="true"
                                                                        AllowFiltering="true"
                                                                        InputAttributes="@(new Dictionary<string,object>(){ { "aria-label", Localizer["SelectPrintLayout"] }})"
                                                                        Placeholder="@Localizer["SelectPrintLayout"]"/>
                                                    </RadzenStack>
                                                    <RadzenButton Click="@(_ => OpenDesignerForLayout(ProcessArticle.LayoutSackEtik,false))" Icon="edit" ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.ExtraSmall" Style="width:9%"
                                                                  MouseEnter="@(args => ShowTooltip(args, Localizer["OpenDesigner"], new TooltipOptions() { Position = TooltipPosition.Left }))"/>
                                                </RadzenStack>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Visible="@(CanSave)">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["Note"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="12">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Style="width: 100%">
                                                <RadzenTextArea readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.Note" Style="width: 100%" aria-label="TextArea" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>

                </RadzenColumn>
                <RadzenColumn Size="12" SizeMD="6">
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Visible="@(CanSave)">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["Description"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="12">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["Description"]"/>
                                                <RadzenTextBox Name="Beschreibung" readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.DetailDescription" MaxLength="250"/>
                                                <RadzenRequiredValidator Component="Beschreibung"
                                                                         Text="@Localizer["DescriptionIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["Name"]"/>
                                                <RadzenTextBox readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.Description" MaxLength="120"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@(Localizer["Name"] + " " + 2)"/>
                                                <RadzenTextBox readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.Description2" MaxLength="120"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@(Localizer["Name"] + " " + 3)"/>
                                                <RadzenTextBox readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.Description3" MaxLength="120"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@(Localizer["Name"] + " " + 4)"/>
                                                <RadzenTextBox readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.Description4" MaxLength="120"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Visible="@(CanSave)">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["Pallet"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["EanPallet"]"/>
                                                <RadzenTextBox Name="ProcessArticleEanPalette" @bind-Value="@ProcessArticle.EanPalette" readonly="@(IsOnlyShowForShowMode)"/>
                                                <RadzenRequiredValidator Component="ProcessArticleEanPalette"
                                                                         Text="@Localizer["EanPalletIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["PalletType"]"/>
                                                <RadzenDropDown Name="Palettentyp" readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.PalettenTyp" Data="@_palettType" TextProperty="@nameof(Combobox.Value)" ValueProperty="Key" AllowClear="true"/>
                                                <RadzenRequiredValidator Component="Palettentyp"
                                                                         Text="@Localizer["PalletTypeIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="4">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["HeightCm"]"/>
                                                <RadzenNumeric readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.DimensionInMilliMeter.HeightInCm"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="4">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["WidthCm"]"/>
                                                <RadzenNumeric readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.DimensionInMilliMeter.WidthInCm"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="4">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["DepthCm"]"/>
                                                <RadzenNumeric readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.DimensionInMilliMeter.DepthInCm"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["PalletizerRecipe"]"/>
                                                <RadzenDropDown readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.PalettiererRezept" Data="@_palettenRezept" TextProperty="@nameof(Combobox.Value)" ValueProperty="Key" AllowClear="true"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["PrintCount"]"/>
                                                <RadzenNumeric Name="Druckanzahl" readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.NumberOfPalletEtikettToPrint"/>
                                                <RadzenRequiredValidator Component="Druckanzahl"
                                                                         Text="@Localizer["PrintCountIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["PrintLayoutForPalletLabel"]"/>
                                                <RadzenStack Orientation="Orientation.Horizontal" Gap="6px">
                                                    <RadzenStack Orientation="Orientation.Vertical" Gap="1rem"  Style="width:90%">
                                                        <RadzenDropDown readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.LayoutPalEtik"
                                                                        Data="@_allLayoutCanUseKeyToDescription"
                                                                        TextProperty="@nameof(CustomizedRepositoryItem.UIName)"
                                                                        FilterCaseSensitivity="FilterCaseSensitivity.CaseInsensitive"
                                                                        TValue="@string"
                                                                        ValueProperty="InternalID"
                                                                        AllowClear="true"
                                                                        AllowFiltering="true"
                                                                        InputAttributes="@(new Dictionary<string,object>(){ { "aria-label", Localizer["SelectPrintLayout"] }})"
                                                                        Placeholder="@Localizer["SelectPrintLayout"]"/>
                                                    </RadzenStack>
                                                    <RadzenButton Click="@(_ => OpenDesignerForLayout(ProcessArticle?.LayoutPalEtik,true))" Icon="edit" ButtonStyle="ButtonStyle.Primary" Size="ButtonSize.ExtraSmall" Style="width:9%"
                                                                  MouseEnter="@(args => ShowTooltip(args, Localizer["OpenDesigner"], new TooltipOptions() { Position = TooltipPosition.Left }))"/>
                                                </RadzenStack>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>
           
                    <RadzenStack Orientation="Orientation.Vertical" Gap="6px" Visible="@(CanSave)">
                        <RadzenFieldset AllowCollapse="true" Style="background-color:white">
                            <HeaderTemplate>
                                <RadzenStack Orientation="Orientation.Horizontal" Gap="0.25rem">
                                    <b>@Localizer["AdditionalData"]</b>
                                </RadzenStack>
                            </HeaderTemplate>
                            <ChildContent>
                                <RadzenStack Orientation="Orientation.Vertical" Gap="1rem">
                                    <RadzenRow Gap="1rem">
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="@Localizer["BestBeforeDateMonth"]"/>
                                                <RadzenNumeric Name="Mhd" readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.MhdTimeSpan"/>
                                                <RadzenRequiredValidator Component="Mhd"
                                                                         Text="@Localizer["BestBeforeDateMonthIsMandatory"]" />
                                            </RadzenStack>
                                        </RadzenColumn>
                                        <RadzenColumn Size="12" SizeSM="6">
                                            <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                                                <RadzenLabel Text="RezeptWickler"/>
                                                <RadzenDropDown readonly="@(IsOnlyShowForShowMode)" @bind-Value="@ProcessArticle.RezeptWickler" Data="@_wicklerRezept" TextProperty="@nameof(Combobox.Value)" ValueProperty="Key" AllowClear="true"/>
                                            </RadzenStack>
                                        </RadzenColumn>
                                    </RadzenRow>
                                </RadzenStack>
                            </ChildContent>
                        </RadzenFieldset>
                    </RadzenStack>
           
                </RadzenColumn>
            </RadzenRow>
        </RadzenTemplateForm>
    </ChildContent>
</RadzenContent>

@code {
    [Parameter] public long ProcessArticleNumber { get; set; }
    [Parameter] public bool IsOnlyShowForShowMode { get; set; } = false;
    [Parameter] public bool ShowOpenRelationToProcessArticle { get; set; } = false;

    ProcessArticle ProcessArticle { get; set; } = new ();
    IEnumerable<Combobox> _sackType = [];
    IEnumerable<Combobox> _palettType = [];
    IEnumerable<Combobox> _etikettType = [];
    IEnumerable<Combobox> _palettenRezept = [];
    IEnumerable<Combobox> _wicklerRezept = [];
    Combobox? SelectedSackType { get; set; }

    private List<Artikel> AllMainArticles { get; set; } = [];
    private Artikel SelectedBaseArticle { get; set; } = new ();
    private string SelectedCustomerName { get; set; } = string.Empty;

    bool CanSave => ProcessArticle.BasisArticleId != 0 ;

    readonly LoadingIndicatorOptions _saveLoadingIndicatorDataOptions = new(false, false, false, false);
    LoadingIndicator? _saveDataLoadingIndicator = default!;
    
    readonly LoadingIndicatorOptions _deleteLoadingIndicatorDataOptions = new(false, false, false, false);
    LoadingIndicator? _deleteDataLoadingIndicator = default!;
    
    
    protected override async Task OnInitializedAsync()
    {
        try
        {
            await Reload(ProcessArticleNumber);
            
            AllMainArticles = ArticleService.Value.GetAll(false).ToList();
            if (ProcessArticle.BasisArticleId != 0)
            {
                SelectedBaseArticle = AllMainArticles.FirstOrDefault(e => e.Id == ProcessArticle.BasisArticleId) ?? new();
            }

            if (ProcessArticle.CustomerId != 0)
            {
                var selectedCustomer = await KundenService.GetCustomer(false, ProcessArticle.CustomerId);
                SelectedCustomerName = selectedCustomer?.FullName() ?? string.Empty;
            }

            await ReloadLayoutItems();
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }

        await base.OnInitializedAsync();
    }

    private async Task Reload(long processArticleNumber)
    {
        ProcessArticleNumber = processArticleNumber;
        
        ProcessArticle = await Sender.Send(new ProcessArticlesOverNumberQuery(processArticleNumber, true)) ?? new ProcessArticle();
        
        _sackType = await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.Sack));
        _palettType = await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.PalettenTyp));
        _etikettType = await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.EtikettTyp));
        _palettenRezept = await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.PalettenReszept));
        _wicklerRezept = await Sender.Send(new GetAllComboBoxByTypeQuery(ComboboxType.WicklerRezept));
        SelectedBaseArticle = new Artikel();
        SelectedCustomerName = string.Empty;
        
        StateHasChanged();
    }
    
    private async Task ResetProcessArticle()
    {
        ProcessArticleNumber = 0;
        ProcessArticle.EanPalette = string.Empty;
        ProcessArticle.EanSack = string.Empty;
        
        await Reload(0);

        ProcessArticle = new ProcessArticle();
    }
    
    private async Task SetProcessArticleButton()
    {
        if (_saveDataLoadingIndicator is null)
            return;

        await _saveDataLoadingIndicator.Run();
    }
    
    private async Task SetProcessArticle()
    {
        try
        {
            if(!IsValid())
                return;
            
            if(ProcessArticle.Id != 0)
                await Sender.Send(new UpdateProcessArticleCommand(ProcessArticle));
            else
                await Sender.Send(new CreateProcessArticleCommand(ProcessArticle));
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    
    private async Task DeleteProcessArticleButton()
    {
        if (_deleteDataLoadingIndicator is null)
            return;

        await _deleteDataLoadingIndicator.Run();
    }
    
    private async Task DeleteProcessArticle()
    {
        try
        {
            if(ProcessArticle.Id != 0)
                await Sender.Send(new DeleteProcessArticleCommand(ProcessArticle.Id));

            await Reload(0);
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    
    private async Task CopyProcessArticle()
    {
        try
        {
            ProcessArticle.Id = 0;
            ProcessArticle.EanPalette = string.Empty;
            ProcessArticle.EanSack = string.Empty;
            
            await Sender.Send(new CreateProcessArticleCommand(ProcessArticle));
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    
    private void OpenProcessArticleSearch()
    {
        NavigationManager.NavigateTo($"/processArticleSearch");
    }

    private async Task SelectCustomerMasterData()
    {
        try
        {
            var result = await DialogService.OpenAsync<CustomerMasterDataSearchPage>("",
                new Dictionary<string, object>() { { "DoOpenCustomerPager", false } },
                new DialogOptions() 
                {
                    Resizable = false, 
                    Draggable = false,
                    Width = "90%", 
                    Height = "90%",
                });
            if (result is not Kunden resultCast)
                return;
            
            SelectedCustomerName = resultCast.FullName();
            ProcessArticle.CustomerId = resultCast.Id;
            ProcessArticle.CustomerNumber = resultCast.Kdnummer;
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }
    }


    private void SetBasisArticle(object? value)
    {
        if (value is null)
        {
            ProcessArticle.BasisArticleId = 0;
            ProcessArticle.BasisArticleNumber = 0;
        }
        
        if(value is not Artikel valueCast)
            return;

        if(ProcessArticle.Id == 0)
            ProcessArticle.ResetDataFields();
        
        ProcessArticle.BasisArticleId = valueCast.Id;
        ProcessArticle.BasisArticleNumber = valueCast.Artikelnr;

        if(string.IsNullOrWhiteSpace(ProcessArticle.Description) )
            ProcessArticle.Description = valueCast.ArtBezText1 ?? string.Empty;
        
        if(string.IsNullOrWhiteSpace(ProcessArticle.Description2))
            ProcessArticle.Description2 = valueCast.ArtBezText2;
        
        if(string.IsNullOrWhiteSpace(ProcessArticle.Description3))
            ProcessArticle.Description3 = valueCast.ArtBezText3;
        
        if(string.IsNullOrWhiteSpace(ProcessArticle.Description4))
            ProcessArticle.Description4 = valueCast.ArtBezText4;
        
        if(string.IsNullOrWhiteSpace(ProcessArticle.ChargeNr))
            ProcessArticle.ChargeNr = valueCast.ArtChargenNr;
        
        if(string.IsNullOrWhiteSpace(ProcessArticle.LayoutSackEtik))
            ProcessArticle.LayoutSackEtik = valueCast.DruckFormSack;
        
        if(string.IsNullOrWhiteSpace(ProcessArticle.LayoutPalEtik))
            ProcessArticle.LayoutPalEtik = valueCast.DruckForm;

        if (TimeSpan.TryParse(valueCast.ArtHaltbarkeit, out var timeSpan))
        {
            var months = (short)timeSpan.TotalDays;
            ProcessArticle.MhdTimeSpan = months;
        }
        
        StateHasChanged();
    }

    private Task OnChangeValueFromSackTyp(object value)
    {
        ProcessArticle.SackInhalt = null;
        ProcessArticle.SackTyp = null;
        
        if(value is not Combobox valueCast)
            return Task.CompletedTask;

        ProcessArticle.SackTyp = valueCast.Value;
        if(long.TryParse(valueCast.Key, out var newValue))
            ProcessArticle.SackInhalt = newValue;
        
        return Task.CompletedTask;
    }
    
    IEnumerable<CustomizedRepositoryItem> _allLayoutCanUseKeyToDescription = [];

    private async Task ReloadLayoutItems()
    {
        try
        {
            _allLayoutCanUseKeyToDescription = await Sender.Send(new AllRepositoryItemsFromPrintObjectQuery(LabelPalletDto.Dummy));
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    
    private async Task OpenDesignerForLayout(string? layoutForPrint, bool doPrintPalette)
    {
        try
        {
            IPrintObject objectToPrint = doPrintPalette ? LabelPalletDto.Dummy : LabelBagDto.Dummy;
            
            if (ApplicationPath.IAmBlazorServer())
            {
                _ = Task.Run(() =>
                {
                    ListLabelFactory.OpenWebDesigner(objectToPrint, "");
                }).ConfigureAwait(false);
            }
            else
            {
                //TODO listlabel design fot maui app
                //await ListLabelFactory.OpenDesktopDesigner(objectToPrint);
                //DesigneHelper.OpenDesigner(LabelOutgoingInvoice.Dummy(),layoutForPrint,ApplicationPath.GetPath());
            }
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
        finally
        {
            DialogService.Close();
        }
    }
    
    private Task OpenRelationToProcessArticle()
    {
        if (ProcessArticle?.Id is null)
            return Task.CompletedTask;

        NavigationManager.NavigateTo($"/ProcessArticleEditPage/{ProcessArticle.Number}");

        StateHasChanged();
        return Task.CompletedTask;
    }
    
    RadzenTextBox? _customerSearchTextBox;
    Popup? _popup;
    CustomerMasterDataSearchPage? _customerMasterDataSearchPagref;
    
    async Task OnRowSelect(Kunden customer)
    {
        ProcessArticle.CustomerId = customer.Id;
        ProcessArticle.CustomerNumber = customer.Kdnummer;
        
        SelectedCustomerName = customer.FullName();
        await _popup.CloseAsync();
        StateHasChanged();
    }
    
    async Task ResetCustomerInfo()
    {
        ProcessArticle.CustomerId = 0;
        ProcessArticle.CustomerNumber = 0;
        
        SelectedCustomerName = string.Empty;
        await InvokeAsync(StateHasChanged);
    }
    
    private Timer? _debounceTimer;
    private string LastSearchedCustomer { get; set; } = string.Empty;
    async Task OnInput(string args)
    {
        var newSelectedCustomerName = args;
        
        SelectedCustomerName = newSelectedCustomerName;
        await InvokeAsync(StateHasChanged);
        
        await Task.Yield();
        if (string.Equals(LastSearchedCustomer, newSelectedCustomerName, StringComparison.InvariantCultureIgnoreCase))
        {
            await ResetCustomerInfo();
            return;
        }
            
        await OnSearchCustomer(newSelectedCustomerName,false);
    }
    
    async Task OnSearchCustomer(string selectedCustomerName, bool now)
    {
        if (string.IsNullOrWhiteSpace(selectedCustomerName))
        {
            ProcessArticle.CustomerId = 0;
            ProcessArticle.CustomerNumber = 0;
        }
        
        LastSearchedCustomer = selectedCustomerName;
        
        // Setze den Timer zurück
        if(_debounceTimer is not null)
            await _debounceTimer.DisposeAsync();
        _debounceTimer = new Timer(async void (_) =>
        {
            try
            {
                await InvokeAsync(async () =>
                {
                    await _customerMasterDataSearchPagref?.SearchAllCustomerMasterData(LastSearchedCustomer)!;
                });
            }
            catch (Exception e)
            {
                await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
            }
        }, null, now ? 1 : 1500, Timeout.Infinite);
    }
    
    private async Task HandleKeyPress(KeyboardEventArgs args)
    {
        if (args.Key != "Enter")
            return;
        if(_customerSearchTextBox?.Element is null)
            return;

        await Task.Yield();
        if(_debounceTimer is not null)
            await _debounceTimer.DisposeAsync();
        await OnSearchCustomer(SelectedCustomerName,true);
    }

    bool _isPopupCustomerSearchIsOpen;

    private async Task HandleClickToSearchCustomer(ElementReference? elementReference)
    {
        try
        {
            if(_popup is null)
                return;
            if(_isPopupCustomerSearchIsOpen)
                return;
            if(elementReference is null)
                return;
            if(_customerSearchTextBox is null)
                return;
            await _customerSearchTextBox.FocusAsync();
            await _popup.ToggleAsync(elementReference.Value);
        }
        catch (Exception e)
        {
            await DialogService.ShowError(e.InnerException?.Message ?? e.Message);
        }
    }
    
    RadzenTemplateForm<ProcessArticle> TemplateForm { get; set; } = default!;
    private bool IsValid()
    {
        StateHasChanged();
        return TemplateForm.EditContext.Validate();
    }
}

<Popup @ref="@_popup"
       id="popup"
       Open="@(_=> _isPopupCustomerSearchIsOpen = true)"
       Close="@(_=> _isPopupCustomerSearchIsOpen = false)"
       AutoFocusFirstElement="false" class="customerSearch-popup">
    <CustomerMasterDataSearchPage @ref="_customerMasterDataSearchPagref"
                                  DoOpenCustomerPager="false"
                                  OwnRowDoubleClick="@OnRowSelect"
                                  AsPopUp="true"
    />
</Popup>