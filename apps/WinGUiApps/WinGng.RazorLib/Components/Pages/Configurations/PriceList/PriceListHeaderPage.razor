@page "/PriceListHeaders"
@using MediatR
@using WingCore.application.ApiDtos.MasterData.V1
@using WingCore.application.PriceList.Queries
@using WinGng.RazorLib.Components.Common.LoadingPage

@inject IMediator Mediator
@inject NotificationService NotificationService
@inject DialogService DialogService
@inject NavigationManager NavigationManager

@inherits BasePage

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="LoadData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>

<RadzenStack>
    <RadzenFormField Text=@Localizer["PriceList"] Variant="@Variant.Outlined" class="rz-mb-3 rz-ms-4 rz-w-25">
        <Start>
            <RadzenIcon Icon="search"/>
        </Start>
        <ChildContent>
            <RadzenTextBox @oninput="args => UpdateVisiblePriceListHeaders(args.Value?.ToString())"/>
        </ChildContent>
    </RadzenFormField>
    <RadzenDataList 
        Data="@_visiblePriceListHeaders"
        TItem="PriceListHeaderDtoV1"
        PageSize="5"
        PagerHorizontalAlign="HorizontalAlign.Left"
        ShowPagingSummary="true"
        Style="max-height: 82vh; overflow-y: auto">
        <Template Context="priceListHeader">
            <RadzenRow Gap="0">
                <RadzenColumn Size="12" SizeLG="3" class="rz-p-4 product-title">
                    <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5" class="rz-color-on-secondary-lighter">@(priceListHeader.Text1)</RadzenText>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeLG="7" class="rz-p-4">
                    <RadzenRow Gap="0">
                        <RadzenColumn Size="12" SizeMD="6" SizeLG="5">
                            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5" class="rz-mb-0">@(priceListHeader.Text2)</RadzenText>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6" SizeLG="2">
                            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5" class="rz-mb-0">@(priceListHeader.Number)</RadzenText>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6" SizeLG="2">
                            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5" class="rz-mb-0">@(priceListHeader.Date.ToShortDateString())</RadzenText>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeLG="2" class="rz-p-4">
                    <RadzenButton Icon="edit" 
                                  Shade="Shade.Lighter" 
                                  Click="() => PriceListEditButtonClick(priceListHeader)" Style="width: 100%"/>
                </RadzenColumn>
            </RadzenRow>
        </Template>
    </RadzenDataList>
</RadzenStack>

@code{
    private IEnumerable<PriceListHeaderDtoV1> _allPriceListHeaders = new List<PriceListHeaderDtoV1>();
    private IEnumerable<PriceListHeaderDtoV1> _visiblePriceListHeaders = new List<PriceListHeaderDtoV1>();
    
    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(true, false, false, false, 0, 6);
    LoadingIndicator? _loadingIndicator;
    
    private async Task LoadData()
    {
        var allPriceListHeaders = await Mediator.Send(new AllPriceListHeadersQuery());
        
        _allPriceListHeaders = allPriceListHeaders.ToList();
        _visiblePriceListHeaders = _allPriceListHeaders;
    }
    
    private void PriceListEditButtonClick(PriceListHeaderDtoV1 priceListHeader)
    {
        NavigationManager.NavigateTo($"/priceListEdit/{priceListHeader.Id}");
    }

    private void UpdateVisiblePriceListHeaders(string? input)
    {
        input = input is not null ? input.ToUpper() : string.Empty;
        
        _visiblePriceListHeaders = _allPriceListHeaders.Where(ph => 
                ph.Text1?.ToUpper().Contains(input) == true ||
                ph.Text2?.ToUpper().Contains(input) == true ||
                ph.Number.ToString().Contains(input)||
                ph.Date.ToShortDateString().Contains(input)
                )
            .ToList();
    }
}