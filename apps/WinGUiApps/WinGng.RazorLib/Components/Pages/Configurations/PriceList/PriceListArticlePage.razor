@page "/PriceListEdit/{PriceListHeaderId:long}"
@using MediatR
@using WingCore.application.ApiDtos.MasterData.V1
@using WingCore.application.Contract.Services
@using WingCore.application.PriceList.Queries
@using WingCore.domain.Models
@using WinGng.RazorLib.Components.Common.LoadingPage

@inject IMediator Mediator
@inject NotificationService NotificationService
@inject DialogService DialogService
@inject IKundenService KundenService
@inject NavigationManager NavigationManager

@inherits BasePage
<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="LoadData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>

<RadzenStack>
    <div class="d-flex gap-2 justify-content-end text-end">
        <RadzenButton class="rz-base" Click="HandleBackButtonClick">@Localizer["Cancel"]</RadzenButton>
        <RadzenButton Click="SaveChanges">@Localizer["Save"]</RadzenButton>
    </div>
    <RadzenRow>
        <RadzenColumn Size="6">
            <RadzenRow style="height: 100%">
                <RadzenColumn Size="12" class="card">
                    <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5" class="rz-color-on-secondary-lighter">@Localizer["PriceList"] @_priceListHeader.Number</RadzenText>
                    <RadzenRow>
                        <RadzenColumn Size="4">
                            <RadzenFormField Text="@Localizer["Name"]" Variant="Variant.Outlined" Style="width: 100%">
                                <RadzenTextBox @bind-Value="@_priceListHeader.Text1" />
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn Size="4">
                            <RadzenFormField Text="@Localizer["Date"]" Variant="Variant.Outlined" Style="width: 100%">
                                <RadzenDatePicker @bind-Value="@_priceListHeader.Date" DateFormat="dd.MM.yyyy"/>
                            </RadzenFormField>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenColumn>
                <RadzenColumn Size="12" class="card">
                    <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5">@Localizer["Articles"]</RadzenText>
                    <RadzenRow class="rz-mb-5">
                        <RadzenColumn>
                            <RadzenFormField Text="@Localizer["Currency"]" Variant="Variant.Outlined" Style="width: 100%">
                                <RadzenDropDown @bind-Value=@_selectedCurrency Data=@_currencies />
                            </RadzenFormField>
                        </RadzenColumn>
                        <RadzenColumn></RadzenColumn>
                        <RadzenColumn></RadzenColumn>
                    </RadzenRow>
                    <div style="max-height: 55vh; overflow-y: auto">
                        @foreach (var priceList in _priceLists)
                        {
                            <RadzenRow class="rz-mb-4">
                                <RadzenColumn Size="4">
                                    <RadzenFormField Text="@Localizer["Designation"]" Variant="Variant.Outlined" Style="width: 100%">
                                        <RadzenTextBox @bind-Value="priceList.ArtBez"/>
                                    </RadzenFormField>
                                </RadzenColumn>
                                <RadzenColumn Size="4">
                                    <RadzenFormField Text="@Localizer["PriceInKG"]" Variant="Variant.Outlined" Style="width: 100%">
                                        <RadzenNumeric @bind-Value="priceList.Price1"
                                                       Format="C"/>
                                    </RadzenFormField>
                                </RadzenColumn>
                                <RadzenColumn Size="2">
                                    <RadzenFormField>
                                        <RadzenButton Icon="delete"
                                                      ButtonStyle="ButtonStyle.Primary"
                                                      Variant="Variant.Flat"
                                                      Size="ButtonSize.Medium"
                                                      Click="@(() => DeleteArticle(priceList))"
                                                      aria-label="@Localizer["Cancel"]"/>
                                    </RadzenFormField>
                                </RadzenColumn>
                            </RadzenRow>
                        }
                    </div>
                    <RadzenButton Icon="add_circle" Click="HandleAddArticleButtonClick" class="rz-mt-3" />
                </RadzenColumn>
            </RadzenRow>
        </RadzenColumn>
        <RadzenColumn Size="6" class="card">
            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H5">@Localizer["Customers"]</RadzenText>
                    <RadzenFormField Text="@Localizer["Customer"]" Variant="Variant.Outlined" class="rz-mb-3">
                        <Start>
                            <RadzenIcon Icon="search" />
                        </Start>
                        <ChildContent>
                            <RadzenTextBox @oninput="args => HandleCustomerSearchInputChange(args.Value?.ToString())" />
                        </ChildContent>
                    </RadzenFormField> 
            <RadzenTable Style="min-height: 75vh; max-height: 75vh; overflow-y: auto">
                <RadzenTableHeader>
                    <RadzenTableHeaderRow>
                        <RadzenTableHeaderCell>
                           @Localizer["Name"] 
                        </RadzenTableHeaderCell>
                        <RadzenTableHeaderCell>
                           @Localizer["Address"] 
                        </RadzenTableHeaderCell>
                        <RadzenTableHeaderCell>
                           @Localizer["Email"] 
                        </RadzenTableHeaderCell>
                        <RadzenTableHeaderCell>
                           @Localizer["InPriceList"] 
                        </RadzenTableHeaderCell>
                    </RadzenTableHeaderRow>
                </RadzenTableHeader>
                <RadzenTableBody>
                    @foreach (var customer in _visibleCustomers)
                    {
                        <RadzenTableRow>
                            <RadzenTableCell Title="@customer.Kdname1">@customer.Kdname1</RadzenTableCell>
                            <RadzenTableCell Title="@($"{customer.Kdstrasse} {customer.Kdplz} {customer.Kdort}")">
                                @customer.Kdstrasse @customer.Kdplz @customer.Kdort
                            </RadzenTableCell>
                            <RadzenTableCell Title="@customer.Kdemail">@customer.Kdemail</RadzenTableCell>
                            <RadzenTableCell Title="@Localizer["InPriceList"]">
                                @if (CustomerIsInPriceList(customer))
                                {
                                    <RadzenIcon
                                        Icon="check_circle"
                                        IconStyle="IconStyle.Success"
                                        onclick="@(() => ChangeCustomer(customer))"
                                        Style="cursor: pointer"/>
                                }
                                else
                                {
                                    <RadzenIcon
                                        Icon="radio_button_unchecked"
                                        IconStyle="IconStyle.Success"
                                        onclick="@(() => ChangeCustomer(customer))"
                                        Style="cursor: pointer"/>
                                }
                            </RadzenTableCell>
                        </RadzenTableRow>
                    }
                </RadzenTableBody>
            </RadzenTable>
        </RadzenColumn>
    </RadzenRow>
</RadzenStack>

@code{
    [Parameter] public required long PriceListHeaderId { get; set; }
    private IList<PriceListDtoV1> _priceLists = new List<PriceListDtoV1>();
    private IList<PriceListDtoV1> _priceListsToDelete = [];
    
    private IList<Kunden> _allCustomers = new List<Kunden>();
    private List<Kunden> _visibleCustomers = [];
    private List<Kunden> _changedCustomers = [];
    private PriceListHeaderDtoV1 _priceListHeader = PriceListHeaderDtoV1.Create(); 
    
    private readonly IEnumerable<string> _currencies = ["EUR", "DOLLAR"];
    private string _selectedCurrency = "EUR";
    private string _customerSearchInput = string.Empty;
    
    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(true, false, false, false, 0, 6);
    LoadingIndicator? _loadingIndicator;

    private async Task LoadData()
    {
        var priceListHeader = await Mediator.Send(new GetPriceListHeaderByIdQuery(PriceListHeaderId));
        _priceListHeader = priceListHeader ?? throw new Exception("Price list header not found");

        var allPriceLists = await Mediator.Send(new AllPriceListsQuery());
        _priceLists = allPriceLists.Where(priceList => priceList.Nr == _priceListHeader.Number).ToList();
        
        var allCustomers = KundenService.GetAllCustomers(false); 
        _allCustomers = allCustomers.ToList();
        _visibleCustomers = _allCustomers.ToList();
        SortVisibleCustomers();
    }
    
    private void HandleBackButtonClick()
    {
        NavigationManager.NavigateTo("/priceListHeaders");
    }

    private void HandleAddArticleButtonClick()
    {
        var emptyArticle = new PriceListDtoV1
        {
            Nr = _priceListHeader.Number
        };
        
        _priceLists.Add(emptyArticle);
    }

    private void SortVisibleCustomers()
    {
        _visibleCustomers = _visibleCustomers.OrderByDescending(CustomerIsInPriceList).ToList();
    }

    private void HandleCustomerSearchInputChange(string? input)
    {
        input = input is not null ? input.ToUpper() : string.Empty;
        _customerSearchInput = input; 
        
        UpdateVisibleCustomers();
    }

    private void UpdateVisibleCustomers()
    {
        var filteredCustomers = _allCustomers;

        if (!string.IsNullOrEmpty(_customerSearchInput))
        {
            filteredCustomers = filteredCustomers.Where(c => c.Kdname1?.ToUpper().Contains(_customerSearchInput) == true ||
                                                         c.Kdstrasse?.ToUpper().Contains(_customerSearchInput) == true ||
                                                         c.Kdplz?.ToUpper().Contains(_customerSearchInput) == true ||
                                                         c.Kdort?.ToUpper().Contains(_customerSearchInput) == true ||
                                                         c.Kdemail?.ToUpper().Contains(_customerSearchInput) == true 
                )
                .ToList();
        }
        
        _visibleCustomers = filteredCustomers.ToList();
        SortVisibleCustomers();
    }

    private bool CustomerIsInPriceList(Kunden customer)
    {
        return customer.KdpreislNr1 == _priceListHeader.Number ||
       customer.KdpreislNr2 == _priceListHeader.Number ||
       customer.KdpreislNr3 == _priceListHeader.Number ||
       customer.KdpreislNr4 == _priceListHeader.Number ||
       customer.KdpreislNr5 == _priceListHeader.Number ||
       customer.KdpreislNr6 == _priceListHeader.Number;
    }

    private void ChangeCustomer(Kunden changedCustomer)
    {
        var customerInVisibleCustomers = _visibleCustomers.FirstOrDefault(c => c.Id == changedCustomer.Id);
        
        if (customerInVisibleCustomers is null)
            return;
        
        ChangeCustomerPriceList(customerInVisibleCustomers);
        
        _changedCustomers.RemoveAll(c => c.Id == changedCustomer.Id);
        _changedCustomers.Add(changedCustomer);

        _visibleCustomers = _visibleCustomers.Where(c => c.Id != changedCustomer.Id).ToList();
        _visibleCustomers.Add(changedCustomer);
        
        UpdateVisibleCustomers();
        StateHasChanged();
    }

    private void ChangeCustomerPriceList(Kunden customer)
    {
        if (CustomerIsInPriceList(customer))
        {
            if (customer.KdpreislNr1 == _priceListHeader.Number) customer.KdpreislNr1 = 0;
            else if (customer.KdpreislNr2 == _priceListHeader.Number) customer.KdpreislNr2 = 0;
            else if (customer.KdpreislNr3 == _priceListHeader.Number) customer.KdpreislNr3 = 0;
            else if (customer.KdpreislNr4 == _priceListHeader.Number) customer.KdpreislNr4 = 0;
            else if (customer.KdpreislNr5 == _priceListHeader.Number) customer.KdpreislNr5 = 0;
            else if (customer.KdpreislNr6 == _priceListHeader.Number) customer.KdpreislNr6 = 0;
        }
        else
        {
            if (customer.KdpreislNr1 == 0) customer.KdpreislNr1 = _priceListHeader.Number;
            else if (customer.KdpreislNr2 == 0) customer.KdpreislNr2 = _priceListHeader.Number;
            else if (customer.KdpreislNr3 == 0) customer.KdpreislNr3 = _priceListHeader.Number;
            else if (customer.KdpreislNr4 == 0) customer.KdpreislNr4 = _priceListHeader.Number;
            else if (customer.KdpreislNr5 == 0) customer.KdpreislNr5 = _priceListHeader.Number;
            else if (customer.KdpreislNr6 == 0) customer.KdpreislNr6 = _priceListHeader.Number;
            else throw new Exception("No free KdPreislNr available to add");
        }
    }
    
    async Task DeleteArticle(PriceListDtoV1 priceListDtoV1)
    {
        var confirmed = await DialogService.Confirm( Localizer["ShouldTheArticleReallyBeDeleted", priceListDtoV1.ArtBez ?? ""], 
                                                            Localizer["DeleteArticle"],
                                                            new ConfirmOptions()
                                                            {
                                                                OkButtonText = Localizer["Yes"], 
                                                                CancelButtonText = Localizer["No"] 
                                                            }) ?? false;

        if (!confirmed) return;
        
        _priceListsToDelete.Add(priceListDtoV1);
        _priceLists = _priceLists.Where(priceList => priceList != priceListDtoV1).ToList();
    }

    async Task SaveChanges()
    {
        try
        {
            foreach (var priceList in _priceLists)
            {
                if (priceList.Id == 0)
                {
                    await Mediator.Send(new AddPriceListCommand(priceList));
                }
                else
                {
                    await Mediator.Send(new UpdatePriceListCommand(priceList));
                }
            }

            foreach (var priceList in _priceListsToDelete)
            {
                await Mediator.Send(new DeletePriceListCommand(priceList));
            }
            
            foreach (var customer in _changedCustomers)
            {
                await Mediator.Send(new UpdateCustomerPriceListsCommand(
                    customer.Id,
                    customer.KdpreislNr1,
                    customer.KdpreislNr2,
                    customer.KdpreislNr3,
                    customer.KdpreislNr4,
                    customer.KdpreislNr5,
                    customer.KdpreislNr6));
            }

        }
        catch (Exception e)
        {
            NotificationService.Notify(new NotificationMessage { 
                Severity = NotificationSeverity.Error, 
                Summary = Localizer["Error"], 
                Detail = Localizer["PriceListCouldNotBeSaved"], 
                Duration = 6000 });
            throw new Exception(e.Message);
        }
       
        NavigationManager.NavigateTo("/priceListHeaders");
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Success, 
            Summary = Localizer["Success"], 
            Detail = Localizer["PriceListSaved"], 
            Duration = 6000
        });
    }
}