@page "/articleListPage"

@using MediatR
@using WingCore.application.Article.Queries
@using WingCore.application.DataTransferObjects.Articles
@using WingCore.data.Repositories.Helper
@using WinGng.RazorLib.Components.Common.LoadingPage
@inject DialogService DialogService
@inject IMediator Mediator
@inject NavigationManager NavigationManager
@inject NotificationService NotificationService
@inherits BasePage

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="LoadData"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>
<LoadingIndicator @ref="_deleteCheckLoadingIndicator"
                  DoLoadDataCallback="DeleteArticleWhenPossible"
                  Option="_optionsLoadConfigData" DialogService="DialogService"/>

<RadzenStack>
    <RadzenRow>
        <RadzenColumn>
            <RadzenFormField Text="Artikel" Variant="@Variant.Outlined" class="rz-mb-3 rz-ms-4 rz-w-25">
                <Start>
                    <RadzenIcon Icon="search"/>
                </Start>
                <ChildContent>
                    <RadzenTextBox @oninput="args => UpdateArticleSearch(args.Value?.ToString() ?? string.Empty)" Value="@_searchInput"/>
                </ChildContent>
            </RadzenFormField>
        </RadzenColumn> 
        <RadzenColumn>
            <div class="d-flex justify-content-end text-end">
                <RadzenButton
                    Icon="add_circle"
                    Shade="Shade.Lighter"
                    Click="() => ArticleAddButtonClick()" />
            </div>
        </RadzenColumn>
    </RadzenRow>
    <RadzenDataList Data="@_visibleArticles" TItem="ArticleDto" PageSize="5" PagerHorizontalAlign="HorizontalAlign.Left" ShowPagingSummary="true">
        <Template Context="article">
            <RadzenRow Gap="0">
                <RadzenColumn Size="12" SizeLG="3" class="rz-p-4 product-title">
                    <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H6" class="rz-color-on-secondary-lighter">@(article.ArtBezText1)</RadzenText>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeLG="7" class="rz-p-4">
                    <RadzenRow Gap="0">
                        <RadzenColumn Size="12" SizeMD="6" SizeLG="5">
                            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H6" class="rz-mb-0">@(article.Artikelnr)</RadzenText>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6" SizeLG="2">
                            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H6" class="rz-mb-0">@(article.ArtSbg)</RadzenText>
                        </RadzenColumn>
                        <RadzenColumn Size="12" SizeMD="6" SizeLG="2">
                            <RadzenText TextStyle="TextStyle.H6" TagName="TagName.H6" class="rz-mb-0">@(article.ArtBezugsgr)</RadzenText>
                        </RadzenColumn>
                    </RadzenRow>
                </RadzenColumn>
                <RadzenColumn Size="12" SizeLG="2" class="rz-p-4">
                    <RadzenStack Orientation="Orientation.Horizontal" Gap="0.5rem">
                        <RadzenButton
                            Icon="edit"
                            Shade="Shade.Lighter"
                            Click="() => ArticleEditButtonClick(article)" Style="width: 100%"/>
                        <RadzenButton
                            Icon="delete"
                            Shade="Shade.Lighter"
                            Click="() => ArticleDeleteButtonClick(article)" Style="width: 100%"/>
                    </RadzenStack>
                </RadzenColumn>
            </RadzenRow>
        </Template>
    </RadzenDataList>
</RadzenStack>

@code{
    private IEnumerable<ArticleDto> _allArticles = new List<ArticleDto>();
    private IEnumerable<ArticleDto> _visibleArticles = new List<ArticleDto>();

    readonly LoadingIndicatorOptions _optionsLoadConfigData = new(true, false, false, false, 0, 6);
    LoadingIndicator? _loadingIndicator;
    LoadingIndicator? _deleteCheckLoadingIndicator;

    private ArticleDto? _articleToDeleteCheck;
    private string _searchInput = string.Empty;

    private async Task LoadData()
    {
        var allArticles = await Mediator.Send(new GetAllArticlesQuery());

        _allArticles = allArticles.ToList();
        _visibleArticles = _allArticles;
    }

    private void ArticleAddButtonClick()
    {
        NavigationManager.NavigateTo("/createArticlePage");
    }
    
    
    private void ArticleEditButtonClick(ArticleDto article)
    {
        NavigationManager.NavigateTo($"/editArticlePage/{article.Artikelnr}");
    }

    private async Task ArticleDeleteButtonClick(ArticleDto article)
    {
        var confirmed = await DialogService.Confirm($"Soll der Artikel '{article.ArtBezText1}' wirklich gelöscht werden?", 
            "Artikel löschen",
            new ConfirmOptions()
            {
                OkButtonText = "Ja", 
                CancelButtonText = "Nein"
            }) ?? false;

        DialogService.Close();
        
        if (!confirmed) return;

        if (_deleteCheckLoadingIndicator is null)
            return;

        _articleToDeleteCheck = article;
        
        await _deleteCheckLoadingIndicator.Run();
    }

    private async Task DeleteArticleWhenPossible()
    {
        if (_articleToDeleteCheck is null)
            return;
        
        
        var successfulDeleted = await Mediator.Send(new DeleteArticleCommand(_articleToDeleteCheck));
        
        if (!successfulDeleted)
        {
            NotificationService.Notify(new NotificationMessage { 
                Severity = NotificationSeverity.Error, 
                Summary = "Fehler", 
                Detail = "Artikel konnte nicht gelöscht werden, da er in anderer Tabelle verwendet wird.", 
                Duration = 6000 });
            
            return;
        }

        _allArticles = _allArticles.Where(article => article != _articleToDeleteCheck);
        
        ResetArticleSearch();
        
        NotificationService.Notify(new NotificationMessage
        {
            Severity = NotificationSeverity.Success, 
            Summary = "Erfolg", 
            Detail = "Artikel wurde gelöscht", 
            Duration = 6000
        });
    }

    private void UpdateArticleSearch(string input)
    {
        _searchInput = input;
        UpdateVisibleArticles(input);
    }
    
    private void UpdateVisibleArticles(string? input = null)
    {
        input = input is not null ? input.ToUpper() : string.Empty;

        _visibleArticles = _allArticles.Where(a =>
                a.ArtBezText1?.ToUpper().Contains(input) == true ||
                a.Artikelnr.ToString().Contains(input) ||
                a.ArtSbg!.ToString().Contains(input) ||
                a.ArtBezugsgr!.ToString().Contains(input)
                )
            .ToList();
    }

    private void ResetArticleSearch()
    {
        _searchInput = string.Empty;
        UpdateVisibleArticles();
    }
}
