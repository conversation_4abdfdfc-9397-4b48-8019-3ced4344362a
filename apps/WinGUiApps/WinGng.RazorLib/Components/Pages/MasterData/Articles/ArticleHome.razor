@page "/articleHomePage"

@using MediatR
@using WingCore.application.Article.Queries
@using WingCore.application.DataTransferObjects.Articles
@using WinGng.RazorLib.Components.Layout
@inject NotificationService NotificationService
@inject NavigationManager NavigationManager
@inject IMediator Mediator
@inherits BasePage

<VASLogoLayout
    SearchPlaceholderText="Artikel über Artikelnummer suchen..."
    ButtonList="@ButtonList"
    OnButtonClickGoToSettings = "HandleButtonClickSettingsPage"
    OnKeyDown="args => HandleKeyDown(args)">
</VASLogoLayout>

@code{
    List<VASLogoLayout.ModelButton> ButtonList =>
        [
            new("add_circle",
                "Artikel erstellen",
                EventCallback.Factory.Create<MouseEventArgs>(this, _ => HandleButtonClickCreateArticle())),
            new("pageview",
                "Artikel laden",
                EventCallback.Factory.Create<MouseEventArgs>(this, _ => HandleButtonClickArticlePage()))
        ];
    
    private async Task HandleKeyDown(string input)
    {
        ArticleDto? foundArticle = null;

        if(long.TryParse(input, out var articleId))
            foundArticle = await Mediator.Send(new GetArticleByNumberQuery(articleId));

        if (foundArticle is null)
        {
            NotificationService.Notify(new NotificationMessage
            {
                Severity = NotificationSeverity.Warning,
                Summary = "Kein Artikel gefunden",
                Detail = $"Kein Artikel mit der Artikelnummer {input} gefunden",
                Duration = 4000
            }); 
        }
        else
        {
            NavigationManager.NavigateTo("editArticlePage/" + articleId);
        }
    }
    
    private void HandleButtonClickCreateArticle() => NavigationManager.NavigateTo("/createArticlePage");
    private void HandleButtonClickArticlePage() => NavigationManager.NavigateTo("/articleListPage");
    private void HandleButtonClickSettingsPage() => NavigationManager.NavigateTo("/articleListPage");

}