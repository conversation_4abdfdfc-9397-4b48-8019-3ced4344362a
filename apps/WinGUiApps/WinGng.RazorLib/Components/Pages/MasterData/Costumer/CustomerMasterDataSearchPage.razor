@page "/customerMasterDataSearch"
@using WingCore.domain.Models
@using WingCore.application.Contract.IModels.Helper
@using WingCore.application.Contract.Services
@using WinGng.RazorLib.Components.Common.LoadingPage

@inherits BasePage

@inject DialogService DialogService
@inject NavigationManager NavigationManager
@inject SearchParametersService SearchParametersService
@inject IKundenService KundenService

<LoadingIndicator @ref="_loadingIndicator"
                  DoLoadDataCallback="SearchAllCustomerMasterData"
                  Option="_options" DialogService="DialogService"/>
@if (!AsPopUp)
{
<RadzenContent Container="main">
    <ChildContent>
        <RadzenRow >
            @* <RadzenIcon Icon="assessment"> *@
            @* </RadzenIcon> *@
            <RadzenHeading Size="H1" style="display: inline-block" Text="@Localizer["CustomerSearch"]">
            </RadzenHeading>

        </RadzenRow>

        <RadzenHeading Size="H2" style="display: inline-block" Text="@Localizer["SearchParameters"]">
        </RadzenHeading>
        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenColumn Size="12" SizeSM="6">
                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                    @Localizer["Search"]
                    <RadzenTextBox @bind-Value="@SearchParametersService.CostumerSearchParam.ValueToSearchOverAllFields"/>
                    
                </RadzenStack>
            </RadzenColumn>
            <RadzenColumn Size="12" SizeSM="6">
                <RadzenStack Orientation="Orientation.Vertical" Gap="6px">
                 
                </RadzenStack>
            </RadzenColumn>
        </RadzenRow>
        <RadzenRow Gap="1rem" style="font-size: large">
            <RadzenStack Orientation="Orientation.Horizontal" JustifyContent="JustifyContent.Start" Gap="1rem" Class="rz-mt-8 rz-mb-4">
                <RadzenButton ButtonType="ButtonType.Submit" Size="ButtonSize.Large" Icon="search" Text="@Localizer["SearchFor"]" Click="@SearchAllCustomerMasterDataButton"/>
            </RadzenStack>
        </RadzenRow>
        <RadzenRow Gap="1rem" style="font-size: large;">
            <div style="width: 100%">
                <RadzenPanel>
                    <ChildContent>
                        <RadzenDataGrid
                            Style="@GridHeight"
                            @ref="@_grid"
                            Count="@_count"
                            Data="@_customerSearch"
                            AllowSorting="true"
                            AllowFiltering="true"
                            AllowPaging="true"
                            ShowPagingSummary="true"
                            PagingSummaryFormat="@Localizer["PagingSummaryFormat"]"
                            GroupPanelText="@Localizer["GroupPanelText"]"
                            PageSize="30"
                            Render="@OnRender"
                            AllowColumnResize="true"
                            PagerHorizontalAlign="HorizontalAlign.Center"
                            LogicalFilterOperator="LogicalFilterOperator.Or" ColumnWidth="100%"
                            SelectionMode="DataGridSelectionMode.Single"
                            @bind-Value="@SelectedCustomers"
                            AllowGrouping="true"
                            TItem="Kunden"
                            RowDoubleClick="@(e => OwnRowDoubleClick?.Invoke(e.Data) ?? EditCustomer(e.Data))">

                            <EmptyTemplate>
                                <p style="color: lightgrey; font-size: 24px; text-align: center; margin: 2rem;">@Localizer["NoCustomersFound"]</p>
                            </EmptyTemplate>
                            <Columns>
                                <RadzenDataGridColumn Property="Kdnummer" Sortable="true" Title="@Localizer["Number"]"></RadzenDataGridColumn>
                                <RadzenDataGridColumn Property="Kdsbg" Sortable="true" Title="@Localizer["Matchcode"]"></RadzenDataGridColumn>
                                <RadzenDataGridColumn Property="KdansprPartner" Sortable="true" Title="@Localizer["ContactPerson"]"></RadzenDataGridColumn>
                                <RadzenDataGridColumn Property="Kdanrede" Sortable="true" Title="@Localizer["Salutation"]"></RadzenDataGridColumn>
                                <RadzenDataGridColumn Property="Kdname1" Sortable="true" Title="@Localizer["Name"]"></RadzenDataGridColumn>
                                <RadzenDataGridColumn Property="Kdname2" Sortable="false" Title="@(Localizer["Name"] + " " + 2)"/>
                                <RadzenDataGridColumn Property="Kdname3" Sortable="false" Title="@(Localizer["Name"] + " " + 3)"/>
                                <RadzenDataGridColumn Property="Kdstrasse" Sortable="false" Title="@Localizer["Street"]"/>
                                <RadzenDataGridColumn Property="Kdplz" Sortable="false" Title="@Localizer["ZipCode"]"/>
                                <RadzenDataGridColumn Property="Kdort" Sortable="false" Title="@Localizer["City"]"/>
                                <RadzenDataGridColumn Property="Kdplzpostfach" Sortable="false" Title="@Localizer["POBox"]"/>
                                <RadzenDataGridColumn Property="Kdland" Sortable="false" Title="@Localizer["Country"]"/>
                                <RadzenDataGridColumn Property="KdbldKennung" Sortable="false" Title="@Localizer["Identifier"]"/>
                            </Columns>
                        </RadzenDataGrid>
                    </ChildContent>
                    <SummaryTemplate>
                        <RadzenCard class="rz-mt-4">
                            <b>@_count @Localizer["Customers"]</b>
                        </RadzenCard>
                    </SummaryTemplate>
                </RadzenPanel>
            </div>
        </RadzenRow>
    </ChildContent>
</RadzenContent>
}
else
{
    <RadzenDataGrid
                        Style="height:100%;width: 100%;"
                        @ref="@_grid"
                        Count="@_count"
                        Data="@_customerSearch"
                        AllowSorting="true"
                        AllowFiltering="true"
                        AllowPaging="true"
                        ShowPagingSummary="true"
                        PagingSummaryFormat="@Localizer["PagingSummaryFormat"]"
                        GroupPanelText="@Localizer["GroupPanelText"]"
                        PageSize="30"
                        Render="@OnRender"
                        AllowColumnResize="true"
                        PagerHorizontalAlign="HorizontalAlign.Center"
                        LogicalFilterOperator="LogicalFilterOperator.Or" ColumnWidth="100%"
                        SelectionMode="DataGridSelectionMode.Single"
                        @bind-Value="@SelectedCustomers"
                        AllowGrouping="false"
                        IsLoading=@_isLoading
                        TItem="Kunden"
                        RowDoubleClick="@(e => OwnRowDoubleClick?.Invoke(e.Data) ?? EditCustomer(e.Data))">

                        <EmptyTemplate>
                            <p style="color: lightgrey; font-size: 24px; text-align: center; margin: 2rem;">@Localizer["NoCustomersFound"]</p>
                        </EmptyTemplate>
                        <Columns>
                            <RadzenDataGridColumn Property="Kdnummer" Sortable="true" Title="@Localizer["Number"]"></RadzenDataGridColumn>
                            <RadzenDataGridColumn Property="Kdsbg" Sortable="true" Title="@Localizer["Matchcode"]"></RadzenDataGridColumn>
                            <RadzenDataGridColumn Property="Kdname1" Sortable="true" Title="@Localizer["Name"]"></RadzenDataGridColumn>
                            <RadzenDataGridColumn Property="Kdname2" Sortable="false" Title="@(Localizer["Name"] + " " + 2)"/>
                            <RadzenDataGridColumn Property="Kdname3" Sortable="false" Title="@(Localizer["Name"] + " " + 3)"/>
                        </Columns>
                    </RadzenDataGrid>
}

@code
{
    [Parameter] public bool DoOpenCustomerPager { get; set; } = true;
    [Parameter] public bool AsPopUp { get; set; } = false;
    RadzenDataGrid<Kunden>? _grid;
    int _count;

    IEnumerable<Kunden>? _customerSearch;
    LoadingIndicator? _loadingIndicator;
    readonly LoadingIndicatorOptions _options = new(false, false, false, false);
    private string GridHeight => $"height: {(Dimensions.Height <= 800? 800 : Dimensions.Height) - 550}px";

    IList<Kunden> SelectedCustomers { get; set; } = [];

    private async Task SearchAllCustomerMasterDataButton()
    {
        if (_loadingIndicator is null)
            return;

        await _loadingIndicator.Run();
    }

    void OnRender(DataGridRenderEventArgs<Kunden> args)
    {
        if (args.FirstRender)
        {
            args.Grid.Sorts.Add(new SortDescriptor() { Property = "Kdnummer", SortOrder = SortOrder.Descending });
        }
    }

    private async Task SearchAllCustomerMasterData()
    {
        await SearchAllCustomerMasterData(SearchParametersService.CostumerSearchParam.ValueToSearchOverAllFields);
    }
    
    
    bool _isLoading = false;
    public async Task SearchAllCustomerMasterData(string? valueToSearchOverAllFields)
    {
        try
        {
            _isLoading = true;
            await InvokeAsync(StateHasChanged);
            _customerSearch = [];
            var searchParam = new CustomerSearchParam()
            {
                ValueToSearchOverAllFields = valueToSearchOverAllFields
            };
            var result = Task.Run(async () => await KundenService.GetCustomer(false, searchParam));
            while (!result.IsCompleted)
                await Task.Delay(500);

            _customerSearch = result.Result;

            _options.CurrentPercent = 1;

            if (_customerSearch is not null)
                _count = await Task.FromResult(_customerSearch.Count());
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }
        finally
        {
            _isLoading = false;
        }
        await InvokeAsync(StateHasChanged);
    }
    
    public delegate Task RowDoubleClickDelegate(Kunden e);
    [Parameter]
    public RowDoubleClickDelegate? OwnRowDoubleClick { get; set; }
    
    public Kunden? SelectedCostumerMasterData;
    private async Task EditCustomer(Kunden kunden)
    {
        try
        {
            SelectedCostumerMasterData = kunden;
            
            if (DoOpenCustomerPager)
            {
                NavigationManager.NavigateTo($"/editCostumerMasterData/{kunden.Kdnummer}");
            }
            else
            {
                DialogService.Close(kunden);
            }
        }
        catch (Exception e)
        {
            DialogService.Close();
            await DialogService.ShowError(e.Message);
        }
    }
}