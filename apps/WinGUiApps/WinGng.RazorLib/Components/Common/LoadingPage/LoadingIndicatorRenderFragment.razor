<style>
    /* Override dialog background for loading indicator */
    .rz-dialog {
        background-color: transparent !important;
        box-shadow: none !important;
        border: none !important;
    }

    .vas-loader-wrapper {
        position: relative;
        width: 140px;
        height: 140px;
        transform-style: preserve-3d;
        animation: vas-multiSpin 3s cubic-bezier(0.4, 0.0, 0.6, 1) infinite;
        margin: 0 auto;
    }

    .vas-logo-block {
        position: absolute;
        border-radius: 8px;
        transition: all 0.3s cubic-bezier(0.4, 0.0, 0.6, 1);
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
    }

    /* Block 1 - Top Left (Light Purple) */
    .vas-block-1 {
        width: 60px;
        height: 60px;
        background: linear-gradient(135deg, #938bf5 0%, #938bf5 100%);
        top: 0;
        left: 0;
        animation: vas-separateTopLeft 3s cubic-bezier(0.4, 0.0, 0.6, 1) infinite;
    }

    /* Block 2 - Top Right (Medium Purple) */
    .vas-block-2 {
        width: 70px;
        height: 60px;
        background: linear-gradient(135deg, #706cbc 0%, #706cbc 100%);
        top: 0;
        right: 0;
        animation: vas-separateTopRight 3s cubic-bezier(0.4, 0.0, 0.6, 1) infinite;
    }

    /* Block 3 - Bottom Left (Dark Purple) */
    .vas-block-3 {
        width: 85px;
        height: 70px;
        background: linear-gradient(135deg, #2d2d4c 0%, #2d2d4c 100%);
        bottom: 0;
        left: 0;
        animation: vas-separateBottomLeft 3s cubic-bezier(0.4, 0.0, 0.6, 1) infinite;
    }

    /* Block 4 - Bottom Right (Medium-Dark Purple) */
    .vas-block-4 {
        width: 45px;
        height: 70px;
        background: linear-gradient(135deg, #4f4c84 0%, #4f4c84 100%);
        bottom: 0;
        right: 0;
        animation: vas-separateBottomRight 3s cubic-bezier(0.4, 0.0, 0.6, 1) infinite;
    }

    .vas-loading-text {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: #333;
        font-weight: bold;
        font-size: 14px;
        text-align: center;
        z-index: 10;
        white-space: nowrap;
    }

    /* Multiple rotations with detailed keyframes */
    @@keyframes vas-multiSpin {
        0% { transform: rotate(0deg) scale(1); }
        1% { transform: rotate(7.2deg) scale(1.005); }
        2% { transform: rotate(14.4deg) scale(1.01); }
        3% { transform: rotate(21.6deg) scale(1.015); }
        4% { transform: rotate(28.8deg) scale(1.02); }
        5% { transform: rotate(36deg) scale(1.025); }
        6% { transform: rotate(43.2deg) scale(1.03); }
        7% { transform: rotate(50.4deg) scale(1.035); }
        8% { transform: rotate(57.6deg) scale(1.04); }
        9% { transform: rotate(64.8deg) scale(1.045); }
        10% { transform: rotate(72deg) scale(1.05); }
        11% { transform: rotate(79.2deg) scale(1.055); }
        12% { transform: rotate(86.4deg) scale(1.06); }
        13% { transform: rotate(93.6deg) scale(1.065); }
        14% { transform: rotate(100.8deg) scale(1.07); }
        15% { transform: rotate(108deg) scale(1.075); }
        16% { transform: rotate(115.2deg) scale(1.08); }
        17% { transform: rotate(122.4deg) scale(1.085); }
        18% { transform: rotate(129.6deg) scale(1.09); }
        19% { transform: rotate(136.8deg) scale(1.095); }
        20% { transform: rotate(144deg) scale(1.1); }
        21% { transform: rotate(151.2deg) scale(1.095); }
        22% { transform: rotate(158.4deg) scale(1.09); }
        23% { transform: rotate(165.6deg) scale(1.085); }
        24% { transform: rotate(172.8deg) scale(1.08); }
        25% { transform: rotate(180deg) scale(1.075); }
        26% { transform: rotate(187.2deg) scale(1.07); }
        27% { transform: rotate(194.4deg) scale(1.065); }
        28% { transform: rotate(201.6deg) scale(1.06); }
        29% { transform: rotate(208.8deg) scale(1.055); }
        30% { transform: rotate(216deg) scale(1.05); }
        31% { transform: rotate(223.2deg) scale(1.045); }
        32% { transform: rotate(230.4deg) scale(1.04); }
        33% { transform: rotate(237.6deg) scale(1.035); }
        34% { transform: rotate(244.8deg) scale(1.03); }
        35% { transform: rotate(252deg) scale(1.025); }
        36% { transform: rotate(259.2deg) scale(1.02); }
        37% { transform: rotate(266.4deg) scale(1.015); }
        38% { transform: rotate(273.6deg) scale(1.01); }
        39% { transform: rotate(280.8deg) scale(1.005); }
        40% { transform: rotate(288deg) scale(1); }
        41% { transform: rotate(295.2deg) scale(0.995); }
        42% { transform: rotate(302.4deg) scale(0.99); }
        43% { transform: rotate(309.6deg) scale(0.985); }
        44% { transform: rotate(316.8deg) scale(0.98); }
        45% { transform: rotate(324deg) scale(0.975); }
        46% { transform: rotate(331.2deg) scale(0.97); }
        47% { transform: rotate(338.4deg) scale(0.965); }
        48% { transform: rotate(345.6deg) scale(0.96); }
        49% { transform: rotate(352.8deg) scale(0.955); }
        50% { transform: rotate(360deg) scale(0.95); }
        51% { transform: rotate(367.2deg) scale(0.955); }
        52% { transform: rotate(374.4deg) scale(0.96); }
        53% { transform: rotate(381.6deg) scale(0.965); }
        54% { transform: rotate(388.8deg) scale(0.97); }
        55% { transform: rotate(396deg) scale(0.975); }
        56% { transform: rotate(403.2deg) scale(0.98); }
        57% { transform: rotate(410.4deg) scale(0.985); }
        58% { transform: rotate(417.6deg) scale(0.99); }
        59% { transform: rotate(424.8deg) scale(0.995); }
        60% { transform: rotate(432deg) scale(1); }
        61% { transform: rotate(439.2deg) scale(1.005); }
        62% { transform: rotate(446.4deg) scale(1.01); }
        63% { transform: rotate(453.6deg) scale(1.015); }
        64% { transform: rotate(460.8deg) scale(1.02); }
        65% { transform: rotate(468deg) scale(1.025); }
        66% { transform: rotate(475.2deg) scale(1.03); }
        67% { transform: rotate(482.4deg) scale(1.035); }
        68% { transform: rotate(489.6deg) scale(1.04); }
        69% { transform: rotate(496.8deg) scale(1.045); }
        70% { transform: rotate(504deg) scale(1.05); }
        71% { transform: rotate(511.2deg) scale(1.045); }
        72% { transform: rotate(518.4deg) scale(1.04); }
        73% { transform: rotate(525.6deg) scale(1.035); }
        74% { transform: rotate(532.8deg) scale(1.03); }
        75% { transform: rotate(540deg) scale(1.025); }
        76% { transform: rotate(547.2deg) scale(1.02); }
        77% { transform: rotate(554.4deg) scale(1.015); }
        78% { transform: rotate(561.6deg) scale(1.01); }
        79% { transform: rotate(568.8deg) scale(1.005); }
        80% { transform: rotate(576deg) scale(1); }
        81% { transform: rotate(583.2deg) scale(0.995); }
        82% { transform: rotate(590.4deg) scale(0.99); }
        83% { transform: rotate(597.6deg) scale(0.985); }
        84% { transform: rotate(604.8deg) scale(0.98); }
        85% { transform: rotate(612deg) scale(0.975); }
        86% { transform: rotate(619.2deg) scale(0.98); }
        87% { transform: rotate(626.4deg) scale(0.985); }
        88% { transform: rotate(633.6deg) scale(0.99); }
        89% { transform: rotate(640.8deg) scale(0.995); }
        90% { transform: rotate(648deg) scale(1); }
        91% { transform: rotate(655.2deg) scale(1.005); }
        92% { transform: rotate(662.4deg) scale(1.01); }
        93% { transform: rotate(669.6deg) scale(1.005); }
        94% { transform: rotate(676.8deg) scale(1); }
        95% { transform: rotate(684deg) scale(0.995); }
        96% { transform: rotate(691.2deg) scale(0.99); }
        97% { transform: rotate(698.4deg) scale(0.995); }
        98% { transform: rotate(705.6deg) scale(1); }
        99% { transform: rotate(712.8deg) scale(1); }
        100% { transform: rotate(720deg) scale(1); }
    }

    /* Enhanced separation animations with detailed keyframes */
    @@keyframes vas-separateTopLeft {
        0%, 100% { transform: translate(0, 0) rotate(0deg); }
        5% { transform: translate(-2px, -2px) rotate(2deg); }
        10% { transform: translate(-5px, -5px) rotate(5deg); }
        15% { transform: translate(-8px, -8px) rotate(8deg); }
        20% { transform: translate(-12px, -12px) rotate(12deg); }
        25% { transform: translate(-15px, -15px) rotate(15deg); }
        30% { transform: translate(-18px, -18px) rotate(18deg); }
        35% { transform: translate(-22px, -22px) rotate(22deg); }
        40% { transform: translate(-25px, -25px) rotate(25deg); }
        45% { transform: translate(-28px, -28px) rotate(28deg); }
        50% { transform: translate(-30px, -30px) rotate(30deg); }
        55% { transform: translate(-28px, -28px) rotate(28deg); }
        60% { transform: translate(-25px, -25px) rotate(25deg); }
        65% { transform: translate(-22px, -22px) rotate(22deg); }
        70% { transform: translate(-18px, -18px) rotate(18deg); }
        75% { transform: translate(-15px, -15px) rotate(15deg); }
        80% { transform: translate(-12px, -12px) rotate(12deg); }
        85% { transform: translate(-8px, -8px) rotate(8deg); }
        90% { transform: translate(-5px, -5px) rotate(5deg); }
        95% { transform: translate(-2px, -2px) rotate(2deg); }
    }

    @@keyframes vas-separateTopRight {
        0%, 100% { transform: translate(0, 0) rotate(0deg); }
        5% { transform: translate(2px, -2px) rotate(-2deg); }
        10% { transform: translate(5px, -5px) rotate(-5deg); }
        15% { transform: translate(8px, -8px) rotate(-8deg); }
        20% { transform: translate(12px, -12px) rotate(-12deg); }
        25% { transform: translate(15px, -15px) rotate(-15deg); }
        30% { transform: translate(18px, -18px) rotate(-18deg); }
        35% { transform: translate(22px, -22px) rotate(-22deg); }
        40% { transform: translate(25px, -25px) rotate(-25deg); }
        45% { transform: translate(28px, -28px) rotate(-28deg); }
        50% { transform: translate(30px, -30px) rotate(-30deg); }
        55% { transform: translate(28px, -28px) rotate(-28deg); }
        60% { transform: translate(25px, -25px) rotate(-25deg); }
        65% { transform: translate(22px, -22px) rotate(-22deg); }
        70% { transform: translate(18px, -18px) rotate(-18deg); }
        75% { transform: translate(15px, -15px) rotate(-15deg); }
        80% { transform: translate(12px, -12px) rotate(-12deg); }
        85% { transform: translate(8px, -8px) rotate(-8deg); }
        90% { transform: translate(5px, -5px) rotate(-5deg); }
        95% { transform: translate(2px, -2px) rotate(-2deg); }
    }

    @@keyframes vas-separateBottomLeft {
        0%, 100% { transform: translate(0, 0) rotate(0deg); }
        5% { transform: translate(-2px, 2px) rotate(-2deg); }
        10% { transform: translate(-5px, 5px) rotate(-5deg); }
        15% { transform: translate(-8px, 8px) rotate(-8deg); }
        20% { transform: translate(-12px, 12px) rotate(-12deg); }
        25% { transform: translate(-15px, 15px) rotate(-15deg); }
        30% { transform: translate(-18px, 18px) rotate(-18deg); }
        35% { transform: translate(-22px, 22px) rotate(-22deg); }
        40% { transform: translate(-25px, 25px) rotate(-25deg); }
        45% { transform: translate(-28px, 28px) rotate(-28deg); }
        50% { transform: translate(-30px, 30px) rotate(-30deg); }
        55% { transform: translate(-28px, 28px) rotate(-28deg); }
        60% { transform: translate(-25px, 25px) rotate(-25deg); }
        65% { transform: translate(-22px, 22px) rotate(-22deg); }
        70% { transform: translate(-18px, 18px) rotate(-18deg); }
        75% { transform: translate(-15px, 15px) rotate(-15deg); }
        80% { transform: translate(-12px, 12px) rotate(-12deg); }
        85% { transform: translate(-8px, 8px) rotate(-8deg); }
        90% { transform: translate(-5px, 5px) rotate(-5deg); }
        95% { transform: translate(-2px, 2px) rotate(-2deg); }
    }

    @@keyframes vas-separateBottomRight {
        0%, 100% { transform: translate(0, 0) rotate(0deg); }
        5% { transform: translate(2px, 2px) rotate(2deg); }
        10% { transform: translate(5px, 5px) rotate(5deg); }
        15% { transform: translate(8px, 8px) rotate(8deg); }
        20% { transform: translate(12px, 12px) rotate(12deg); }
        25% { transform: translate(15px, 15px) rotate(15deg); }
        30% { transform: translate(18px, 18px) rotate(18deg); }
        35% { transform: translate(22px, 22px) rotate(22deg); }
        40% { transform: translate(25px, 25px) rotate(25deg); }
        45% { transform: translate(28px, 28px) rotate(28deg); }
        50% { transform: translate(30px, 30px) rotate(30deg); }
        55% { transform: translate(28px, 28px) rotate(28deg); }
        60% { transform: translate(25px, 25px) rotate(25deg); }
        65% { transform: translate(22px, 22px) rotate(22deg); }
        70% { transform: translate(18px, 18px) rotate(18deg); }
        75% { transform: translate(15px, 15px) rotate(15deg); }
        80% { transform: translate(12px, 12px) rotate(12deg); }
        85% { transform: translate(8px, 8px) rotate(8deg); }
        90% { transform: translate(5px, 5px) rotate(5deg); }
        95% { transform: translate(2px, 2px) rotate(2deg); }
    }
</style>

<RadzenStack @ref=LoadingElement Orientation="Orientation.Vertical" AlignItems="AlignItems.Center" JustifyContent="JustifyContent.Center" Wrap="FlexWrap.Wrap" Style="background: transparent !important; padding: 100px; height: 100%; width: 100%;" Gap="2rem">
    <div style="position: relative; display: inline-block;">
        <div class="vas-loader-wrapper">
            <div class="vas-logo-block vas-block-1"></div>
            <div class="vas-logo-block vas-block-2"></div>
            <div class="vas-logo-block vas-block-3"></div>
            <div class="vas-logo-block vas-block-4"></div>
        </div>
        @if (Option.ShowStepNumbers || Option.ShowPercentage)
        {
            <div class="vas-loading-text">
                @if (Option.ShowStepNumbers)
                {
                    @($"{Option.CurrentStep} / {Option.TotalSteps}")
                }
                @if (Option.ShowPercentage)
                {
                    @($"{Option.CurrentPercent}%")
                }
            </div>
        }
    </div>
    @if (Option.ShowText)
    {
        @($"{Option.Text}")
    }
    @if (Option.ShowOwnStepText)
    {
        @(string.Format(Option.OwnStepText, Option.CurrentStep, Option.TotalSteps));
    }
</RadzenStack>

@code {

    [Parameter] public LoadingIndicatorOptions Option { get; set; } = new();
    public RadzenStack? LoadingElement { get; set; }

    protected override void OnInitialized()
    {
        Option.UpdateAction = StateHasChanged;
    }
}