
@code {

    [Parameter]
    public EventCallback<object> DoLoadDataCallbackWithArg { get; set; }
    [Parameter]
    public EventCallback DoLoadDataCallback { get; set; }

    [Parameter, EditorRequired]
    public DialogService DialogService { get; set; } = default!;
    
    [Parameter, EditorRequired]
    public LoadingIndicatorOptions Option { get; set; }  = new();

    [Parameter]
    public object? Arg { get; set; }

    [Parameter]
    public string HeightAndWidth { get; set; } = "auto";
    
    
    private CancellationTokenSource? _cts;

    protected async override Task OnAfterRenderAsync(bool firstRender)
    {
        if (firstRender)
            if (Option.StartAfterRender)
                await this.Run(Arg);
    }

    public async Task Run()
    {
        await this.ShowLoadingDialog();
        await DoLoadDataCallback.InvokeAsync();
        Option.CurrentStep = Option.TotalSteps;
        await this.HideDialog();
    }

    public async Task Run(object? arg)
    {
        Arg = arg;
        
        await this.ShowLoadingDialog();
        if(DoLoadDataCallback.HasDelegate)
            await DoLoadDataCallback.InvokeAsync(Arg);
        if(DoLoadDataCallbackWithArg.HasDelegate)
            await DoLoadDataCallbackWithArg.InvokeAsync(Arg);
        Option.CurrentStep = Option.TotalSteps;
        await this.HideDialog();
    }
    
    public async Task ShowLoadingDialog()
    {
        _cts = new CancellationTokenSource();
        _ = InvokeAsync(async () => await LoadingDialog());
        await Task.Run(() => CheckForHide(), _cts.Token);
    }

    public async Task HideDialog()
    {
        if (_cts is not null)
            _cts.Cancel();
        await InvokeAsync(StateHasChanged);
    }

    private async void CheckForHide()
    {
        if (_cts is null) return;

        while (!_cts.IsCancellationRequested)
            await Task.Delay(500);
        await InvokeAsync(() => DialogService.Close());
    }

    private async Task LoadingDialog()
    {
        await DialogService.OpenAsync<LoadingIndicatorRenderFragment>(
            "",
                new Dictionary<string, object> { { "Option", Option } },
                new DialogOptions()
                {
                    ShowTitle = false,
                    Style = $"min-height:600px;min-width:600px;width:600px;height:600px;",
                    CloseDialogOnEsc = false,
                    CssClass = "loading-dialog",
                }

                );
    }

}
