namespace WinGng.RazorLib.Components.Common.SearchParam;

public record ProcessArticleSearchParamForComponent
{
    public string SelectedCustomerName { get; set; } = string.Empty;
    public CustomerSearchMode SelectCustomerSearchMode { get; set; } = CustomerSearchMode.WithCustomer;
    
    public IEnumerable<CustomerSearchMode> CustomerSearchModes
        => [
            CustomerSearchMode.WithCustomer,
            CustomerSearchMode.WithoutCustomer,
            CustomerSearchMode.OnlyCustomer
        ];
}

public record CustomerSearchMode(int Mode)
{
    public static CustomerSearchMode WithCustomer => new(1);
    public static CustomerSearchMode WithoutCustomer => new(2);
    public static CustomerSearchMode OnlyCustomer => new(3);
        
    public bool IsWithCustomer => Mode == 1;
    public bool IsWithoutCustomer => Mode == 2;
    public bool IsOnlyCustomer => Mode == 3;

    public override string ToString()
    {
        return Mode.ToString();
    }
};