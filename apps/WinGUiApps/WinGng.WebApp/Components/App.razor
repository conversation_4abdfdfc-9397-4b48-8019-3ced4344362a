@using Microsoft.AspNetCore.Components.Web
<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <base href="/"/>
    <link rel="stylesheet" href="@Assets["app.css"]"/>
    <link href="assets/css/styles.css" rel="stylesheet" />
    <link href="css/site.css" rel="stylesheet" />
    <link rel="shortcut icon" type="image/x-icon" href="Logo.ico" />
    <link rel="stylesheet" href="@Assets["css/controlling.css"]" />
    <link href="css/MainLayout.css" rel="stylesheet" />
    <link href="css/GrainAccounting/GrainAccounting.css" rel="stylesheet" />
    <link href="css/GrainAccounting/GrainAccountingPositions.css" rel="stylesheet" />
    <link href="css/GrainAccounting/GrainAccountingOneFieldDialog.css" rel="stylesheet" />
    <link href="css/WarehouseManagement/ Management.css" rel="stylesheet" />
</head>

<body>
<Routes @rendermode="new InteractiveServerRenderMode(prerender: false)"/>
@* <Routes/> *@
<script src="https://cdn.tailwindcss.com"></script>

<script src="_framework/blazor.web.js"></script>
<script src="_content/Radzen.Blazor/Radzen.Blazor.js?v=@(typeof(Radzen.Colors).Assembly.GetName().Version)"></script>
<script src="combit-webreportviewer-30.2.min.js"></script>
<script src="combit-webreportdesigner-30.2.min.js"></script>
<script src="js/interop.js"></script>
<script>
    window.registerViewportChangeCallback = (dotnetHelper) => {
        window.addEventListener('load', () => {
            dotnetHelper.invokeMethodAsync('OnResize', window.innerWidth, window.innerHeight);
        });
        window.addEventListener('resize', () => {
            dotnetHelper.invokeMethodAsync('OnResize', window.innerWidth, window.innerHeight);
        });
    }

    window.getDimensions = function () {
        return {
            width: window.innerWidth,
            height: window.innerHeight
        };
    };
</script>
</body>

</html>