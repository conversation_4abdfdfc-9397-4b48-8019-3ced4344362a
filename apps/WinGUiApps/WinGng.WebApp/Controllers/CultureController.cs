using Microsoft.AspNetCore.Localization;
using Microsoft.AspNetCore.Mvc;

namespace WinGng.WebApp.Controllers;

[Route("[controller]/[action]")]
public class CultureController : Controller
{
    public IActionResult Set(string culture, string redirectUri)
    {
        if (!string.IsNullOrWhiteSpace(culture))
        {
            HttpContext.Response.Cookies.Append(
                CookieRequestCultureProvider.DefaultCookieName,
                CookieRequestCultureProvider.MakeCookieValue(new RequestCulture(culture)),
                new CookieOptions
                {
                    Path = "/",
                    SameSite = SameSiteMode.Lax,
                    IsEssential = true,
                    Expires= DateTime.MaxValue
                });
        }

        return LocalRedirect(redirectUri);
    }
}