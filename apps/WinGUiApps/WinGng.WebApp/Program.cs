using System.Text;
using Licencing;
using Microsoft.AspNetCore.Components.Authorization;
using Languages;
using Microsoft.FluentUI.AspNetCore.Components;
using QuartzScheduler;
using Radzen;
using Serilog;
using Serilog.Events;
using WinGng.WebApp.Components;
using ServiceDefaults;
using WingCore.application.Contract.IModels;
using wingLager.ioc;
using WinGng.RazorLib.Extensions;
using WinGng.WebApp.Extensions;
using WinGng.WebApp.Middleware;
using wingPrinterListLabel.ioc;

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddRazorComponents().AddInteractiveServerComponents();

builder.Services.AddFluentUIComponents();

builder.Services.AddRazorPages();
builder.Services.AddDataProtection();
builder.Services.AddHttpContextAccessor();
builder.Services.AddMemoryCache();
builder.Services.AddRadzenComponents();
builder.Services.AddAuthentication();
        
builder.Services.ConfigureCors();
builder.Services.ConfigureLoggerService();
builder.Services.ConfigureSqlContext();
builder.Services.ConfigureServiceManager();
builder.Services.AddMediatR(mediatRServiceConfiguration =>
{
    mediatRServiceConfiguration.RegisterServicesFromAssemblies(typeof(Program).Assembly);
});
builder.Services.AddSerilog(new LoggerConfiguration()
    //.MinimumLevel.Verbose()
    .MinimumLevel.Override("Microsoft", LogEventLevel.Error)
    .MinimumLevel.Override("Microsoft.AspNetCore.Authorization", LogEventLevel.Error)
    .Enrich.FromLogContext()
    .WriteTo.File(
        Path.Combine(Path.Combine(AppContext.BaseDirectory, "Logs"), "WinGWebAppApp_.log"),
        encoding: Encoding.UTF8,
        flushToDiskInterval: TimeSpan.FromSeconds(1),
        fileSizeLimitBytes: 1024 * 1024 * 100,
        rollingInterval: RollingInterval.Day,
        rollOnFileSizeLimit: true,
        retainedFileCountLimit: 10)
    .CreateLogger());

//builder.Logging.AddDebug();

builder.Services.AddSingleton<IApplicationPath, ApplicationPath>();
builder.Services.AddAuthorizationCore();
builder.Services.AddScoped<AuthenticationStateProvider, ExternalAuthStateProvider>();
builder.Services.AddScoped<SearchParametersService>();
builder.Services.AddScoped<DbContextLoaderService>();

builder.Services.AddAuthorizationCore(options =>
{
    foreach (var possibleClaimsWithDescriptionAndType in LicenseClaims.PossibleClaimsWithDescriptionAndTypes)
    {
        options.AddPolicy(possibleClaimsWithDescriptionAndType.Key, policy => policy.RequireClaim(possibleClaimsWithDescriptionAndType.Key));
    }
});
        
builder.Services.ConfigureScheduler(false);
builder.Host.UseWindowsService();

builder.Services.AddWingLagerServices();
builder.Services.AddWingPrinterListLabelServices();
builder.Services.AddPrinterConfigurations(builder.Environment.WebRootPath);


builder.Services.AddRazorPages(options =>
{
    options.Conventions.AllowAnonymousToPage("/WebReportDesigner");
});

builder.Services.AddSignalR(e => {
    e.MaximumReceiveMessageSize = *********;
});


builder.Services.AddControllers();

builder.Services.AddLocalizationServices();


var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Error", createScopeForErrors: true);
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

//app.UseHttpsRedirection();

app.UseRouting();

app.UseStaticFiles();
app.UseAntiforgery();
app.UseAuthentication();
app.UseAuthorization();
app.MapControllers();
app.UseMiddleware<CultureMiddleware>();
app.ConfigureLocalization(builder.Configuration);

app.MapRazorComponents<App>()
    .AddInteractiveServerRenderMode()
    .AddAdditionalAssemblies(typeof(WinGng.RazorLib.Assemblies).Assembly);

app.AddWingPrinterListLabelConfigurations();

app.Run();
public partial class Program;