<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <OutputType>Exe</OutputType>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
        <IsAspireHost>true</IsAspireHost>
        <UserSecretsId>07EB72A0-BCBE-48D2-B200-36C134A7C7AD</UserSecretsId>
    </PropertyGroup>
    <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />
    <ItemGroup>
        <PackageReference Include="Aspire.Hosting.AppHost" Version="9.4.0" />
        <PackageReference Include="Aspire.Hosting.PostgreSQL" Version="9.4.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\apps\WingNg.LicenseManager\WingNg.LicenseManager.csproj" />
    </ItemGroup>

</Project>
