<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFrameworks>;net9.0</TargetFrameworks>
        <TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\..\Modules\WingCore\WingCore.application\WingCore.application.csproj" />
      <ProjectReference Include="..\..\Languages\Languages.csproj" />
    </ItemGroup>

    <ItemGroup>
      <PackageReference Include="CsvHelper" Version="33.1.0" />
    </ItemGroup>

</Project>
