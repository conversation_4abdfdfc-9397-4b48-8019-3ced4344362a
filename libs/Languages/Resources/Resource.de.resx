<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
    <data name="Connect" xml:space="preserve">
        <value>Verbinden</value>
    </data>
    <data name="Save" xml:space="preserve">
        <value>Speichern</value>
    </data>
    <data name="FromDate" xml:space="preserve">
        <value>Von Datum</value>
    </data>
    <data name="ToDate" xml:space="preserve">
        <value>Bis Datum</value>
    </data>
    <data name="FromInvoiceNumber" xml:space="preserve">
        <value>Von Rechnungsnummer</value>
    </data>
    <data name="ToInvoiceNumber" xml:space="preserve">
        <value>Bis Rechnungsnummer</value>
    </data>
    <data name="OutgoingInvoice" xml:space="preserve">
        <value>Ausgangsrechnung</value>
    </data>
    <data name="IncomingInvoice" xml:space="preserve">
        <value>Eingangsrechnung</value>
    </data>
    <data name="EInvoices" xml:space="preserve">
        <value>E-Rechnungen</value>
    </data>
    <data name="GrainAccounting" xml:space="preserve">
        <value>Getreideabrechnung</value>
    </data>
    <data name="Search" xml:space="preserve">
        <value>Suche</value>
    </data>
    <data name="DeactivatePaging" xml:space="preserve">
        <value>Paging deaktivieren</value>
    </data>
    <data name="ActivatePaging" xml:space="preserve">
        <value>Paging aktivieren</value>
    </data>
    <data name="PagingSummaryFormat" xml:space="preserve">
        <value>Seite {0} von {1} ({2} Einträge)</value>
    </data>
    <data name="GroupPanelText" xml:space="preserve">
        <value>Ziehen Sie einen Spaltenkopf hierher und lassen Sie ihn los, um nach dieser Spalte zu gruppieren</value>
    </data>
    <data name="SelectAllItems" xml:space="preserve">
        <value>Alle Elemente auswählen</value>
    </data>
    <data name="SelectItem" xml:space="preserve">
        <value>Element auswählen</value>
    </data>
    <data name="InvoiceNumber" xml:space="preserve">
        <value>Rechnungsnummer</value>
    </data>
    <data name="Total" xml:space="preserve">
        <value>Total</value>
    </data>
    <data name="Type" xml:space="preserve">
        <value>Typ</value>
    </data>
    <data name="Date" xml:space="preserve">
        <value>Datum</value>
    </data>
    <data name="CreationDate" xml:space="preserve">
        <value>Erstellungsdatum</value>
    </data>
    <data name="TotalAmount" xml:space="preserve">
        <value>Gesamtbetrag</value>
    </data>
    <data name="Email" xml:space="preserve">
        <value>E-Mail</value>
    </data>
    <data name="EInvoice" xml:space="preserve">
        <value>E-Rechnung</value>
    </data>
    <data name="Exported" xml:space="preserve">
        <value>Exportiert</value>
    </data>
    <data name="EmailSent" xml:space="preserve">
        <value>E-Mail versendet</value>
    </data>
    <data name="Invoices" xml:space="preserve">
        <value>Rechnungen</value>
    </data>
    <data name="Close" xml:space="preserve">
        <value>Schließen</value>
    </data>
    <data name="Export" xml:space="preserve">
        <value>Export</value>
    </data>
    <data name="Taxes" xml:space="preserve">
        <value>Steuern</value>
    </data>
    <data name="Positions" xml:space="preserve">
        <value>Positionen</value>
    </data>
    <data name="Invoicing" xml:space="preserve">
        <value>Fakturierung</value>
    </data>
    <data name="InvoiceOverview" xml:space="preserve">
        <value>Rechnungsübersicht</value>
    </data>
    <data name="CancellationNumber" xml:space="preserve">
        <value>Stornonummer</value>
    </data>
    <data name="EmailForEInvoice" xml:space="preserve">
        <value>E-Mail für E-Rechnung</value>
    </data>
    <data name="EmailPreview" xml:space="preserve">
        <value>E-Mail Vorschau</value>
    </data>
    <data name="SearchParameters" xml:space="preserve">
        <value>Suchparameter</value>
    </data>
    <data name="PDFViewer" xml:space="preserve">
        <value>PDF Vorschau</value>
    </data>
    <data name="OwnStepText" xml:space="preserve">
        <value>{0} von {1} Rechnungen bearbeitet!</value>
    </data>
    <data name="OutgoingInvoiceInformation" xml:space="preserve">
        <value>Ausgangsrechnung Informationen</value>
    </data>
    <data name="OpenDesigner" xml:space="preserve">
        <value>Designer öffnen</value>
    </data>
    <data name="OpenInvoice" xml:space="preserve">
        <value>Rechnung öffnen</value>
    </data>
    <data name="NoInvoicesFound" xml:space="preserve">
        <value>Keine Rechnungen gefunden</value>
    </data>
    <data name="ErrorWhenOpeningThePDF" xml:space="preserve">
        <value>Fehler beim Öffnen der PDF</value>
    </data>
    <data name="CancellationInvoiceNumber" xml:space="preserve">
        <value>Storno Rechnungsnummer</value>
    </data>
    <data name="InvoiceHolder" xml:space="preserve">
        <value>Rechnungsträger</value>
    </data>
    <data name="InvoiceDate" xml:space="preserve">
        <value>Rechnungsdatum</value>
    </data>
    <data name="InvoiceIssuer" xml:space="preserve">
        <value>Rechnungsersteller</value>
    </data>
    <data name="OriginalInvoiceNumber" xml:space="preserve">
        <value>Originale Rechnungsnummer</value>
    </data>
    <data name="TotalGross" xml:space="preserve">
        <value>Gesamt Brutto</value>
    </data>
    <data name="TotalNet" xml:space="preserve">
        <value>Gesamt Netto</value>
    </data>
    <data name="TotalTaxes" xml:space="preserve">
        <value>Gesamt Steuern</value>
    </data>
    <data name="ValueDate" xml:space="preserve">
        <value>Valutadatum</value>
    </data>
    <data name="AutoLogin" xml:space="preserve">
        <value>Automatisch anmelden</value>
    </data>
    <data name="ClientDatabase" xml:space="preserve">
        <value>Mandantendatenbank</value>
    </data>
    <data name="ClientNo" xml:space="preserve">
        <value>Mandant Nr.</value>
    </data>
    <data name="DatabaseName" xml:space="preserve">
        <value>Datenbank Name</value>
    </data>
    <data name="DatabasePassword" xml:space="preserve">
        <value>Datenbank Passwort</value>
    </data>
    <data name="DatabasePath" xml:space="preserve">
        <value>Datenbank Pfad</value>
    </data>
    <data name="DatabaseUsername" xml:space="preserve">
        <value>Datenbank Benutzername</value>
    </data>
    <data name="ManageConnection" xml:space="preserve">
        <value>Verbindung verwalten</value>
    </data>
    <data name="MasterDatabase" xml:space="preserve">
        <value>Stammdatenbank</value>
    </data>
    <data name="MasterDatabaseNotReachable" xml:space="preserve">
        <value>Stammdatenbank ist nicht erreichbar: {0}</value>
    </data>
    <data name="NoDatabaseExistsForClient" xml:space="preserve">
        <value>Für diesen Mandanten({0}) existiert keine Datenbank.</value>
    </data>
    <data name="OpenConfigurationFolder" xml:space="preserve">
        <value>Konfigurationsordner öffnen</value>
    </data>
    <data name="WarehouseDatabase" xml:space="preserve">
        <value>Lagerdatenbank</value>
    </data>
    <data name="Address" xml:space="preserve">
        <value>Adresse </value>
    </data>
    <data name="Name" xml:space="preserve">
        <value>Name</value>
    </data>
    <data name="User" xml:space="preserve">
        <value>Benutzer</value>
    </data>
    <data name="Percent" xml:space="preserve">
        <value>Prozent</value>
    </data>
    <data name="TaxAmount" xml:space="preserve">
        <value>Steuerbetrag</value>
    </data>
    <data name="NetAmount" xml:space="preserve">
        <value>Nettobetrag</value>
    </data>
    <data name="Position" xml:space="preserve">
        <value>Position</value>
    </data>
    <data name="ArticleNumber" xml:space="preserve">
        <value>Artikelnummer</value>
    </data>
    <data name="Description" xml:space="preserve">
        <value>Beschreibung</value>
    </data>
    <data name="TotalDiscount" xml:space="preserve">
        <value>Gesamt Rabatt</value>
    </data>
    <data name="TotalNetWithDiscount" xml:space="preserve">
        <value>Gesamt Netto mit Rabatt</value>
    </data>
    <data name="Account" xml:space="preserve">
        <value>Konto</value>
    </data>
    <data name="DeliveryNoteNumber" xml:space="preserve">
        <value>Lieferscheinnummer</value>
    </data>
    <data name="No" xml:space="preserve">
        <value>Nein</value>
    </data>
    <data name="GrainAccountingInformation" xml:space="preserve">
        <value>Getreideabrechnung Informationen</value>
    </data>
    <data name="IncomingInvoiceInformation" xml:space="preserve">
        <value>Eingangsrechnung Informationen</value>
    </data>
    <data name="VAT" xml:space="preserve">
        <value>Steuern</value>
    </data>
    <data name="VATAmount" xml:space="preserve">
        <value>Steuerbetrag</value>
    </data>
    <data name="TotalVAT" xml:space="preserve">
        <value>Gesamtsteuern</value>
    </data>
    <data name="Ausgangsrechnung" xml:space="preserve">
        <value>Ausgangsrechnung</value>
    </data>
    <data name="Eingangsrechnung" xml:space="preserve">
        <value>Eingangsrechnung</value>
    </data>
    <data name="Getreideabrechnung" xml:space="preserve">
        <value>Getreideabrechnung</value>
    </data>
    <data name="MasterData" xml:space="preserve">
        <value>Stammdaten</value>
    </data>
    <data name="CustomerData" xml:space="preserve">
        <value>Kundendaten</value>
    </data>
    <data name="CompanyMasterData" xml:space="preserve">
        <value>Firmenstammdaten</value>
    </data>
    <data name="ProcessArticles" xml:space="preserve">
        <value>Prozess-Artikel</value>
    </data>
    <data name="BasicData" xml:space="preserve">
        <value>Basisdaten</value>
    </data>
    <data name="CustomerNo" xml:space="preserve">
        <value>Kunden Nr.</value>
    </data>
    <data name="Matchcode" xml:space="preserve">
        <value>Matchcode</value>
    </data>
    <data name="Salutation" xml:space="preserve">
        <value>Anrede</value>
    </data>
    <data name="ContactPerson" xml:space="preserve">
        <value>Ansprechpartner</value>
    </data>
    <data name="Street" xml:space="preserve">
        <value>Straße</value>
    </data>
    <data name="ZipCodeCity" xml:space="preserve">
        <value>Plz / Ort</value>
    </data>
    <data name="Country" xml:space="preserve">
        <value>Land</value>
    </data>
    <data name="FederalState" xml:space="preserve">
        <value>Bundesland</value>
    </data>
    <data name="Communication" xml:space="preserve">
        <value>Kommunikation</value>
    </data>
    <data name="Phone" xml:space="preserve">
        <value>Telefon</value>
    </data>
    <data name="Fax" xml:space="preserve">
        <value>Telefax</value>
    </data>
    <data name="Internet" xml:space="preserve">
        <value>Internet</value>
    </data>
    <data name="IdentNumber" xml:space="preserve">
        <value>Identnummer</value>
    </data>
    <data name="ILN" xml:space="preserve">
        <value>ILN</value>
    </data>
    <data name="Kdleh" xml:space="preserve">
        <value>Kdleh</value>
    </data>
    <data name="CustomerSearch" xml:space="preserve">
        <value>Kundensuche</value>
    </data>
    <data name="SearchFor" xml:space="preserve">
        <value>Suchen</value>
    </data>
    <data name="NoCustomersFound" xml:space="preserve">
        <value>Keine Kunden gefunden</value>
    </data>
    <data name="Number" xml:space="preserve">
        <value>Nummer</value>
    </data>
    <data name="ZipCode" xml:space="preserve">
        <value>Plz</value>
    </data>
    <data name="City" xml:space="preserve">
        <value>Ort</value>
    </data>
    <data name="POBox" xml:space="preserve">
        <value>Postfach</value>
    </data>
    <data name="Identifier" xml:space="preserve">
        <value>Kennung</value>
    </data>
    <data name="Customers" xml:space="preserve">
        <value>Kunden</value>
    </data>
    <data name="New" xml:space="preserve">
        <value>Neu</value>
    </data>
    <data name="Delete" xml:space="preserve">
        <value>Löschen</value>
    </data>
    <data name="BasicArticle" xml:space="preserve">
        <value>Basis Artikel</value>
    </data>
    <data name="SelectAMainArticle" xml:space="preserve">
        <value>Wählen Sie einen Hauptartikel</value>
    </data>
    <data name="ReferenceNumber" xml:space="preserve">
        <value>Referenznummer</value>
    </data>
    <data name="Bag" xml:space="preserve">
        <value>Sack</value>
    </data>
    <data name="EanBag" xml:space="preserve">
        <value>Ean Sack</value>
    </data>
    <data name="EanBagIsMandatory" xml:space="preserve">
        <value>Ean Sack ist pflicht</value>
    </data>
    <data name="LabelType" xml:space="preserve">
        <value>Etikettentyp</value>
    </data>
    <data name="LabelTypeIsMandatory" xml:space="preserve">
        <value>Etikettentyp ist pflicht</value>
    </data>
    <data name="BagType" xml:space="preserve">
        <value>Sacktyp</value>
    </data>
    <data name="BagCount" xml:space="preserve">
        <value>Sackanzahl</value>
    </data>
    <data name="BagCountIsMandatory" xml:space="preserve">
        <value>Sackanzahl ist pflicht</value>
    </data>
    <data name="BagContent" xml:space="preserve">
        <value>Sackinhalt</value>
    </data>
    <data name="BagContentIsMandatory" xml:space="preserve">
        <value>Sackinhalt ist pflicht</value>
    </data>
    <data name="PrintLayoutForBagLabel" xml:space="preserve">
        <value>Drucklayout für Sacketikett</value>
    </data>
    <data name="SelectPrintLayout" xml:space="preserve">
        <value>Drucklayout auswählen</value>
    </data>
    <data name="HeightCm" xml:space="preserve">
        <value>Höhe (cm)</value>
    </data>
    <data name="WidthCm" xml:space="preserve">
        <value>Breite (cm)</value>
    </data>
    <data name="DepthCm" xml:space="preserve">
        <value>Tiefe (cm)</value>
    </data>
    <data name="Note" xml:space="preserve">
        <value>Notiz</value>
    </data>
    <data name="Dimensions" xml:space="preserve">
        <value>Maße</value>
    </data>
    <data name="DescriptionIsMandatory" xml:space="preserve">
        <value>Beschreibung ist pflicht</value>
    </data>
    <data name="Pallet" xml:space="preserve">
        <value>Palette</value>
    </data>
    <data name="EanPallet" xml:space="preserve">
        <value>Ean Palette</value>
    </data>
    <data name="EanPalletIsMandatory" xml:space="preserve">
        <value>Ean Palette ist pflicht</value>
    </data>
    <data name="PalletType" xml:space="preserve">
        <value>Palettentyp</value>
    </data>
    <data name="PalletizerRecipe" xml:space="preserve">
        <value>Palettiererrezept</value>
    </data>
    <data name="PrintCount" xml:space="preserve">
        <value>Druckanzahl</value>
    </data>
    <data name="PrintCountIsMandatory" xml:space="preserve">
        <value>Druckanzahl ist pflicht</value>
    </data>
    <data name="PrintLayoutForPalletLabel" xml:space="preserve">
        <value>Drucklayout für Palettenetikett</value>
    </data>
    <data name="AdditionalData" xml:space="preserve">
        <value>Zusatzdaten</value>
    </data>
    <data name="BestBeforeDateMonth" xml:space="preserve">
        <value>Mhd (Monat)</value>
    </data>
    <data name="BestBeforeDateMonthIsMandatory" xml:space="preserve">
        <value>Mhd (Monat) ist pflicht</value>
    </data>
    <data name="NoResults" xml:space="preserve">
        <value>Keine Ergebnisse</value>
    </data>
    <data name="EasiExport" xml:space="preserve">
        <value>Easi Export</value>
    </data>
    <data name="EasiAdfinityInterface" xml:space="preserve">
        <value>Easi Adfinity Schnittstelle</value>
    </data>
    <data name="APIParameters" xml:space="preserve">
        <value>API Parameter</value>
    </data>
    <data name="WeighingSlip" xml:space="preserve">
        <value>Wiegeschein</value>
    </data>
    <data name="Configuration" xml:space="preserve">
        <value>Konfiguration</value>
    </data>
    <data name="Connection" xml:space="preserve">
        <value>Verbindung</value>
    </data>
    <data name="Username" xml:space="preserve">
        <value>Benutzername</value>
    </data>
    <data name="Password" xml:space="preserve">
        <value>Passwort</value>
    </data>
    <data name="BaseURL" xml:space="preserve">
        <value>Basis URL</value>
    </data>
    <data name="AdfinityDatabase" xml:space="preserve">
        <value>Adfinity Datenbank</value>
    </data>
    <data name="AdfinityEnvir" xml:space="preserve">
        <value>Adfinity Envir</value>
    </data>
    <data name="ArticleReferenceSize" xml:space="preserve">
        <value>Artikelbezugsgröße</value>
    </data>
    <data name="Assignments" xml:space="preserve">
        <value>Zuordnungen</value>
    </data>
    <data name="SaveConfiguration" xml:space="preserve">
        <value>Konfiguration speichern</value>
    </data>
    <data name="ElectronicWgsConfiguration" xml:space="preserve">
        <value>Elektronischer Wiegeschein Konfiguration</value>
    </data>
    <data name="ElectronicWgs" xml:space="preserve">
        <value>Elektronischer Wiegeschein</value>
    </data>
    <data name="FromWeighingSlipNumber" xml:space="preserve">
        <value>Von Wiegescheinnummer</value>
    </data>
    <data name="ToWeighingSlipNumber" xml:space="preserve">
        <value>Bis Wiegescheinnummer</value>
    </data>
    <data name="ReExportExportedWeighingSlips" xml:space="preserve">
        <value>Exportierte Wiegescheine wieder exportieren</value>
    </data>
    <data name="Send" xml:space="preserve">
        <value>Senden</value>
    </data>
    <data name="CreateJob" xml:space="preserve">
        <value>Auftrag erstellen</value>
    </data>
    <data name="Error" xml:space="preserve">
        <value>Fehler</value>
    </data>
    <data name="NoWeighingSlipsFound" xml:space="preserve">
        <value>Keine Wiegescheine gefunden</value>
    </data>
    <data name="CountOfWeighingSlips" xml:space="preserve">
        <value>Anzahl der Wiegescheine</value>
    </data>
    <data name="SuccessfullySent" xml:space="preserve">
        <value>Erfolgreich gesendet</value>
    </data>
    <data name="Duration" xml:space="preserve">
        <value>Dauer</value>
    </data>
    <data name="ReExportExportedInvoices" xml:space="preserve">
        <value>Exportierte Rechnungen wieder exportieren</value>
    </data>
    <data name="CountOfInvoices" xml:space="preserve">
        <value>Anzahl der Rechnungen</value>
    </data>
    <data name="ServerName" xml:space="preserve">
        <value>Servername</value>
    </data>
    <data name="NameIsMandatory" xml:space="preserve">
        <value>Name ist erforderlich</value>
    </data>
    <data name="TimeInterval" xml:space="preserve">
        <value>Zeitintervall</value>
    </data>
    <data name="TimeIntervalIsMandatory" xml:space="preserve">
        <value>Zeitintervall ist erforderlich</value>
    </data>
    <data name="Create" xml:space="preserve">
        <value>Erstellen</value>
    </data>
    <data name="Tasks" xml:space="preserve">
        <value>Vorgänge</value>
    </data>
    <data name="Jobs" xml:space="preserve">
        <value>Aufträge</value>
    </data>
    <data name="Reload" xml:space="preserve">
        <value>Neu laden</value>
    </data>
    <data name="Status" xml:space="preserve">
        <value>Status</value>
    </data>
    <data name="Data" xml:space="preserve">
        <value>Daten</value>
    </data>
    <data name="Server" xml:space="preserve">
        <value>Server</value>
    </data>
    <data name="StartTime" xml:space="preserve">
        <value>Startzeit</value>
    </data>
    <data name="NextTriggerTime" xml:space="preserve">
        <value>Nächster Auslösezeitpunkt</value>
    </data>
    <data name="Execute" xml:space="preserve">
        <value>Ausführen</value>
    </data>
    <data name="Stop" xml:space="preserve">
        <value>Stopp</value>
    </data>
    <data name="Start" xml:space="preserve">
        <value>Start</value>
    </data>
    <data name="Logging" xml:space="preserve">
        <value>Protokollierung</value>
    </data>
    <data name="CreateTask" xml:space="preserve">
        <value>Vorgang erstellen</value>
    </data>
    <data name="Normal" xml:space="preserve">
        <value>Normal</value>
    </data>
    <data name="Paused" xml:space="preserve">
        <value>Pausiert</value>
    </data>
    <data name="Completed" xml:space="preserve">
        <value>Abgeschlossen</value>
    </data>
    <data name="Blocked" xml:space="preserve">
        <value>Blockiert</value>
    </data>
    <data name="NotAvailable" xml:space="preserve">
        <value>Nicht vorhanden</value>
    </data>
    <data name="Unknown" xml:space="preserve">
        <value>Unbekannt</value>
    </data>
    <data name="Logs" xml:space="preserve">
        <value>Protokolle</value>
    </data>
    <data name="ComboBoxes" xml:space="preserve">
        <value>Blätterfelder</value>
    </data>
    <data name="ComboBoxesConfiguration" xml:space="preserve">
        <value>Blätterfelder Konfiguration</value>
    </data>
    <data name="Index" xml:space="preserve">
        <value>Index</value>
    </data>
    <data name="CreateComboBox" xml:space="preserve">
        <value>Blätterfeld erstellen</value>
    </data>
    <data name="EditComboBox" xml:space="preserve">
        <value>Blätterfeld bearbeiten</value>
    </data>
    <data name="ElectronicInvoice" xml:space="preserve">
        <value>Elektronische Rechnung</value>
    </data>
    <data name="ElectronicInvoiceConfiguration" xml:space="preserve">
        <value>Elektronische Rechnung Konfiguration</value>
    </data>
    <data name="SenderName" xml:space="preserve">
        <value>Absendername</value>
    </data>
    <data name="SenderEmail" xml:space="preserve">
        <value>Absender E-Mail</value>
    </data>
    <data name="Port" xml:space="preserve">
        <value>Port</value>
    </data>
    <data name="TimeoutInMilliseconds" xml:space="preserve">
        <value>Zeitüberschreitung in Millisekunden</value>
    </data>
    <data name="PrintLayout" xml:space="preserve">
        <value>Drucklayout</value>
    </data>
    <data name="OpenDesignerForEInvoice" xml:space="preserve">
        <value>Designer für E-Rechnung öffnen</value>
    </data>
    <data name="SendTestEmail" xml:space="preserve">
        <value>Test E-Mail senden</value>
    </data>
    <data name="DeleteEmailConfiguration" xml:space="preserve">
        <value>Lösche E-Mail Konfiguration</value>
    </data>
    <data name="Subject" xml:space="preserve">
        <value>Betreff</value>
    </data>
    <data name="FileName" xml:space="preserve">
        <value>Dateiname</value>
    </data>
    <data name="SelectCountry" xml:space="preserve">
        <value>Land auswählen</value>
    </data>
    <data name="EmailCouldNotBeCreated" xml:space="preserve">
        <value>E-Mail konnte nicht erstellt werden</value>
    </data>
    <data name="EmailTestPreview" xml:space="preserve">
        <value>E-Mail Testvorschau</value>
    </data>
    <data name="To" xml:space="preserve">
        <value>An</value>
    </data>
    <data name="Attachment" xml:space="preserve">
        <value>Anhang</value>
    </data>
    <data name="APIKeys" xml:space="preserve">
        <value>API Schlüssel</value>
    </data>
    <data name="Key" xml:space="preserve">
        <value>Schlüssel</value>
    </data>
    <data name="AddAPIKey" xml:space="preserve">
        <value>API Schlüssel hinzufügen</value>
    </data>
    <data name="License" xml:space="preserve">
        <value>Lizenz</value>
    </data>
    <data name="AddFromClipboard" xml:space="preserve">
        <value>Aus der Zwischenablage hinzufügen</value>
    </data>
    <data name="LicenseData" xml:space="preserve">
        <value>Lizenzdaten</value>
    </data>
    <data name="LicenseInformation" xml:space="preserve">
        <value>Lizenzinformationen</value>
    </data>
    <data name="ValidUntil" xml:space="preserve">
        <value>Gültig bis</value>
    </data>
    <data name="Modules" xml:space="preserve">
        <value>Module</value>
    </data>
    <data name="Licenses" xml:space="preserve">
        <value>Lizenzen</value>
    </data>
    <data name="InvalidLicenseDataError" xml:space="preserve">
        <value>Die Eingabe ist keine gültige Base-64-Zeichenfolge, da sie ein nicht-Base-64-Zeichen, mehr als zwei Auffüllzeichen oder ein ungültiges Zeichen unter den Auffüllzeichen enthält.</value>
    </data>
  <data name="Language" xml:space="preserve">
      <value>Sprache</value>
  </data>
  <data name="ZUGFeRD" xml:space="preserve">
      <value>ZUGFeRD</value>
  </data>
  <data name="ZUGFeRDXRechnung" xml:space="preserve">
      <value>ZUGFeRD (XRechnung)</value>
  </data>
  <data name="XRechnung" xml:space="preserve">
      <value>XRechnung</value>
  </data>
  <data name="EDIFACTTrueCommerce" xml:space="preserve">
      <value>EDIFACT (TrueCommerce)</value>
  </data>
  <data name="EDIFACTStradEdi" xml:space="preserve">
      <value>EDIFACT (StradEdi)</value>
  </data>
  <data name="DatabaseIsBeingLoaded" xml:space="preserve">
      <value>Datenbank wird geladen</value>
  </data>
  <data name="ProcessArticleSearch" xml:space="preserve">
      <value>Prozessartikelsuche</value>
  </data>
  <data name="NoOrderFound" xml:space="preserve">
      <value>Kein Auftrag gefunden</value>
  </data>
  <data name="Orders" xml:space="preserve">
      <value>Aufträge</value>
  </data>
  <data name="WithCustomer" xml:space="preserve">
      <value>Mit Kunde</value>
  </data>
  <data name="WithoutCustomer" xml:space="preserve">
      <value>Ohne Kunde</value>
  </data>
  <data name="OnlyCustomer" xml:space="preserve">
      <value>Nur Kunde</value>
  </data>
  <data name="CommissionTrack" xml:space="preserve">
      <value>Spur</value>
  </data>
  <data name="Order" xml:space="preserve">
      <value>Auftrag</value>
  </data>
  <data name="PrintLayoutForDeliveryNote" xml:space="preserve">
      <value>Drucklayout für Lieferschein</value>
  </data>
  <data name="BeginningExportDate" xml:space="preserve">
      <value>Anfang des Exportdatums</value>
  </data>
  <data name="PrintLayoutForPalletNote" xml:space="preserve">
      <value>Drucklayout für Palettenschein</value>
  </data>
  <data name="DATEVExport" xml:space="preserve">
      <value>DATEV-Export</value>
  </data>
  <data name="ExportParameters" xml:space="preserve">
      <value>Export-Parameter</value>
  </data>
  <data name="StartOfFinancialYear" xml:space="preserve">
      <value>Wirtschaftsjahresbeginn</value>
  </data>
  <data name="ConsultantNumber" xml:space="preserve">
      <value>Beraternummer</value>
  </data>
  <data name="GeneralLedgerAccountLength" xml:space="preserve">
      <value>Sachkontenlänge</value>
  </data>
  <data name="SuffixForAccounts" xml:space="preserve">
      <value>Suffix für Konten</value>
  </data>
  <data name="FilePath" xml:space="preserve">
      <value>Dateipfad</value>
  </data>
  <data name="UseExternalInvoiceNumberForIncomingInvoice" xml:space="preserve">
      <value>Externe Rechnungsnummer für Eingangsrechnung verwenden</value>
  </data>
  <data name="ExportOfInvoicesSuccessfullyCompleted" xml:space="preserve">
      <value>Der Export von {0} Rechnungen wurde erfolgreich abgeschlossen. Dauer: {1}.</value>
  </data>
  <data name="GeneralLedgerAccountLengthError" xml:space="preserve">
      <value>Sachkontenlänge darf nicht größer als 9 und kleiner als 4 sein.</value>
  </data>
  <data name="StartOfFinancialYearFirstDayOfMonthError" xml:space="preserve">
      <value>Wirtschaftsjahresbeginn muss immer der erste Tag des Monats sein.</value>
  </data>
  <data name="EDIFACTExport" xml:space="preserve">
      <value>EDIFACT-Export</value>
  </data>
  <data name="ADDISONAKTEtsenitExport" xml:space="preserve">
      <value>ADDISON AKTE (tse:nit) Export</value>
  </data>
  <data name="FromCreationDate" xml:space="preserve">
      <value>Von Erstelldatum</value>
  </data>
  <data name="FromInvoiceDate" xml:space="preserve">
      <value>Von Rechnungsdatum</value>
  </data>
  <data name="UntilCreationDate" xml:space="preserve">
      <value>Bis Erstelldatum</value>
  </data>
  <data name="UntilInvoiceDate" xml:space="preserve">
      <value>Bis Rechnungsdatum</value>
  </data>
  <data name="UntilInvoiceNumber" xml:space="preserve">
      <value>Bis Rechnungsnummer</value>
  </data>
  <data name="ADDISONClientNumber" xml:space="preserve">
      <value>ADDISON-Mandantennummer</value>
  </data>
  <data name="UseInvoiceDateInsteadOfSystemDateForExport" xml:space="preserve">
      <value>Rechnungsdatum statt Systemdatum für den Export nutzen</value>
  </data>
  <data name="WithSubsequentBooking" xml:space="preserve">
      <value>Mit Folgebuchung</value>
  </data>
  <data name="SubsequentPostingsForVATAreGeneratedAutomatically" xml:space="preserve">
      <value>Folgebuchungen für USt (Istverst. und EG-Erwerb) werden automatisch erzeugt</value>
  </data>
  <data name="ADDISONPipelineExport" xml:space="preserve">
      <value>ADDISON Pipeline Export</value>
  </data>
  <data name="FileNameWithTimestamp" xml:space="preserve">
      <value>Dateiname mit Zeitstempel</value>
  </data>
  <data name="EDIFACT" xml:space="preserve">
      <value>EDIFACT</value>
  </data>
  <data name="DATEV" xml:space="preserve">
      <value>DATEV</value>
  </data>
  <data name="ADDISONAKTEtsenit" xml:space="preserve">
      <value>ADDISON AKTE (tse:nit)</value>
  </data>
  <data name="ADDISONPipeline" xml:space="preserve">
      <value>ADDISON Pipeline</value>
  </data>
  <data name="EasiAdfinity" xml:space="preserve">
      <value>Easi Adfinity</value>
  </data>
  <data name="SearchForSuppliers" xml:space="preserve">
      <value>Nach Lieferanten suchen</value>
  </data>
  <data name="SearchAccounting" xml:space="preserve">
      <value>Abrechnung suchen</value>
  </data>
  <data name="LoadWeighingSlips" xml:space="preserve">
      <value>Wiegescheine laden</value>
  </data>
  <data name="ContinueAccounting" xml:space="preserve">
      <value>Abrechnung fortsetzen</value>
  </data>
  <data name="Settings" xml:space="preserve">
      <value>Einstellungen</value>
  </data>
  <data name="NoSupplierFound" xml:space="preserve">
      <value>Kein Lieferant gefunden</value>
  </data>
  <data name="NoSupplierFoundForSearchTerm" xml:space="preserve">
      <value>Es wurde kein passender Lieferant für den Suchbegriff gefunden. Bitte überprüfen Sie Ihre Eingabe.</value>
  </data>
  <data name="PleaseEnterAValidAccountingNumber" xml:space="preserve">
      <value>Bitte geben Sie eine gültige Abrechnungsnummer ein.</value>
  </data>
  <data name="EnterAccountingNumber" xml:space="preserve">
      <value>Abrechnungsnummer eingeben</value>
  </data>
  <data name="WeighingSlipNumber" xml:space="preserve">
      <value>WGS Nummer</value>
  </data>
  <data name="SupplierNumber" xml:space="preserve">
      <value>Lieferantennummer</value>
  </data>
  <data name="Article" xml:space="preserve">
      <value>Artikel</value>
  </data>
  <data name="Weight" xml:space="preserve">
      <value>Gewicht</value>
  </data>
  <data name="Apply" xml:space="preserve">
      <value>Übernehmen</value>
  </data>
  <data name="InconsistentSelection" xml:space="preserve">
      <value>Inkonsistente Auswahl</value>
  </data>
  <data name="AllSelectedItemsSameWeighingSlipSupplierNumber" xml:space="preserve">
      <value>Alle ausgewählten Artikel müssen die gleiche Wiegescheinlieferantennummer haben.</value>
  </data>
  <data name="SelectionError" xml:space="preserve">
      <value>Auswahlfehler</value>
  </data>
  <data name="OnlySelectItemsWithSameWeighingSlipSupplierNumber" xml:space="preserve">
      <value>Sie können nur Artikel mit der gleichen Wiegescheinlieferantennummer auswählen.</value>
  </data>
  <data name="NoSelection" xml:space="preserve">
      <value>Keine Auswahl</value>
  </data>
  <data name="SelectAtLeastOneWeighingSlip" xml:space="preserve">
      <value>Bitte wählen Sie mindestens einen Wiegeschein aus.</value>
  </data>
  <data name="WeighingSlipsUsedForBillingConfirmation" xml:space="preserve">
      <value>{0} Wiegescheine werden für die Abrechnung verwendet. Möchten Sie fortfahren?</value>
  </data>
  <data name="Confirmation" xml:space="preserve">
      <value>Bestätigung</value>
  </data>
  <data name="Yes" xml:space="preserve">
      <value>Ja</value>
  </data>
  <data name="AnErrorOccuredWhenCreatingTheInvoice" xml:space="preserve">
      <value>Beim Erstellen der Abrechnung ist ein Fehler aufgetreten: {0}</value>
  </data>
  <data name="AccountingInformationIsLoaded" xml:space="preserve">
      <value>Abrechnungsinformationen werden geladen.</value>
  </data>
  <data name="ViewModeReadOnly" xml:space="preserve">
      <value>Ansichtsmodus (schreibgeschützt).</value>
  </data>
  <data name="GrainAccountingAlreadyEstablished" xml:space="preserve">
      <value>Getreideabrechnung bereits festgeschrieben</value>
  </data>
  <data name="YouMustDeleteTheEntryToChangeAnything" xml:space="preserve">
      <value>Sie müssen den Eintrag löschen, um etwas zu ändern</value>
  </data>
  <data name="EditModeActive" xml:space="preserve">
      <value>Bearbeitungsmodus aktiv</value>
  </data>
  <data name="ShowLess" xml:space="preserve">
      <value>Weniger anzeigen</value>
  </data>
  <data name="ShowMore" xml:space="preserve">
      <value>Mehr anzeigen</value>
  </data>
  <data name="Supplier" xml:space="preserve">
      <value>Lieferant</value>
  </data>
  <data name="InvoiceOffice" xml:space="preserve">
      <value>Rechnungsstelle</value>
  </data>
  <data name="Cancel" xml:space="preserve">
      <value>Abbrechen</value>
  </data>
  <data name="Edit" xml:space="preserve">
      <value>Bearbeiten</value>
  </data>
  <data name="DeleteWeighingSlip" xml:space="preserve">
      <value>WGS Löschen</value>
  </data>
  <data name="Contract" xml:space="preserve">
      <value>Kontrakt</value>
  </data>
  <data name="NoContract" xml:space="preserve">
      <value>Kein Kontrakt</value>
  </data>
  <data name="Designation" xml:space="preserve">
      <value>Bezeichnung</value>
  </data>
  <data name="LaboratoryValue" xml:space="preserve">
      <value>Laborwert</value>
  </data>
  <data name="UnitPrice" xml:space="preserve">
      <value>Einzelpreis</value>
  </data>
  <data name="TotalPrice" xml:space="preserve">
      <value>Gesamtpreis</value>
  </data>
  <data name="ResetValue" xml:space="preserve">
      <value>Wert zurücksetzen</value>
  </data>
  <data name="EditValue" xml:space="preserve">
      <value>Wert bearbeiten</value>
  </data>
  <data name="DeleteValue" xml:space="preserve">
      <value>Wert löschen</value>
  </data>
  <data name="AddValue" xml:space="preserve">
      <value>Wert hinzufügen</value>
  </data>
  <data name="NetWeight" xml:space="preserve">
      <value>Nettogewicht</value>
  </data>
  <data name="NetPrice" xml:space="preserve">
      <value>Nettopreis</value>
  </data>
  <data name="AddWeighingSlips" xml:space="preserve">
      <value>WGS hinzufügen</value>
  </data>
  <data name="DeliveryWeight" xml:space="preserve">
      <value>Anliefergewicht</value>
  </data>
  <data name="Deductions" xml:space="preserve">
      <value>Abzüge</value>
  </data>
  <data name="AccountingWeight" xml:space="preserve">
      <value>Abrechnungsgewicht</value>
  </data>
  <data name="CounterfoilNo" xml:space="preserve">
      <value>Gegenbeleg Nr.</value>
  </data>
  <data name="AccountingDate" xml:space="preserve">
      <value>Abrechnungsdatum</value>
  </data>
  <data name="GrossAmount" xml:space="preserve">
      <value>Bruttosumme</value>
  </data>
  <data name="PrintPreview" xml:space="preserve">
      <value>Druckvorschau</value>
  </data>
  <data name="Complete" xml:space="preserve">
      <value>Abschließen</value>
  </data>
  <data name="ContractNo" xml:space="preserve">
      <value>Kontrakt Nr.</value>
  </data>
  <data name="AddContract" xml:space="preserve">
      <value>Kontrakt hinzufügen</value>
  </data>
  <data name="NoContractsConnected" xml:space="preserve">
      <value>Keine Verträge verbunden</value>
  </data>
  <data name="ErrorDuringLoading" xml:space="preserve">
      <value>Fehler beim Laden</value>
  </data>
  <data name="ErrorRetrievingTheHeader" xml:space="preserve">
      <value>Fehler beim Abrufen des Headers: {0}</value>
  </data>
  <data name="NotFound" xml:space="preserve">
      <value>Nicht gefunden</value>
  </data>
  <data name="GrainAccountingWithIDNotFound" xml:space="preserve">
      <value>Getreideabrechnung mit ID {0} nicht gefunden.</value>
  </data>
  <data name="ErrorWhenRetrievingThePositions" xml:space="preserve">
      <value>Fehler beim Abrufen der Positionen: {0}</value>
  </data>
  <data name="SupplierWithNumberNotFound" xml:space="preserve">
      <value>Lieferant mit Nummer {0} nicht gefunden</value>
  </data>
  <data name="ErrorRetrievingTheSupplier" xml:space="preserve">
      <value>Fehler beim Abrufen des Lieferanten: {0}</value>
  </data>
  <data name="DataError" xml:space="preserve">
      <value>Datenfehler</value>
  </data>
  <data name="NoSupplierNumberFoundInHeader" xml:space="preserve">
      <value>Keine Lieferantennummer im Header gefunden.</value>
  </data>
  <data name="ErrorWhenRetrievingTheContract" xml:space="preserve">
      <value>Fehler beim Abrufen des Kontrakts {0}: {1}</value>
  </data>
  <data name="Action" xml:space="preserve">
      <value>Aktion</value>
  </data>
  <data name="EditingForWeighingSlipInvoked" xml:space="preserve">
      <value>Bearbeitung für WGS {0} aufgerufen</value>
  </data>
  <data name="EditingCanceled" xml:space="preserve">
      <value>Bearbeitung abgebrochen</value>
  </data>
  <data name="ChangesForWeighingSlipSaved" xml:space="preserve">
      <value>Änderungen für WGS {0} gespeichert</value>
  </data>
  <data name="AreYouSure" xml:space="preserve">
      <value>Sind sie sicher?</value>
  </data>
  <data name="ReallyDeleteWeighingSlip" xml:space="preserve">
      <value>WGS wirklich löschen?</value>
  </data>
  <data name="Success" xml:space="preserve">
      <value>Erfolg</value>
  </data>
  <data name="WeighingSlipRemoved" xml:space="preserve">
      <value>WGS {0} entfernt</value>
  </data>
  <data name="ResetForCalled" xml:space="preserve">
      <value>Zurücksetzen für '{0}' aufgerufen</value>
  </data>
  <data name="ValueUpdated" xml:space="preserve">
      <value>Wert '{0}' aktualisiert</value>
  </data>
  <data name="UpdateFailed" xml:space="preserve">
      <value>Aktualisierung fehlgeschlagen</value>
  </data>
  <data name="ErrorDuringEditing" xml:space="preserve">
      <value>Fehler beim Bearbeiten: {0}</value>
  </data>
  <data name="Confirm" xml:space="preserve">
      <value>Bestätigen</value>
  </data>
  <data name="ReallyDeleteValue" xml:space="preserve">
      <value>Wert wirklich löschen?</value>
  </data>
  <data name="ValueRemoved" xml:space="preserve">
      <value>Wert '{0}' entfernt</value>
  </data>
  <data name="Info" xml:space="preserve">
      <value>Info</value>
  </data>
  <data name="NewValueAddedPlaceholder" xml:space="preserve">
      <value>Neuer Wert hinzugefügt (Platzhalter)</value>
  </data>
  <data name="NewWeighingSlipAddedPlaceholder" xml:space="preserve">
      <value>Neuer WGS hinzugefügt (Platzhalter)</value>
  </data>
  <data name="EnterNo" xml:space="preserve">
      <value>Nr. eingeben</value>
  </data>
  <data name="NewValue" xml:space="preserve">
      <value>Neuer Wert</value>
  </data>
  <data name="Unit" xml:space="preserve">
      <value>Einheit</value>
  </data>
  <data name="TotalPriceNet" xml:space="preserve">
      <value>Gesamtpreis Netto</value>
  </data>
  <data name="Eg" xml:space="preserve">
      <value>z.B.</value>
  </data>
  <data name="Ok" xml:space="preserve">
      <value>Ok</value>
  </data>
  <data name="NoEntriesFound" xml:space="preserve">
      <value>Keine Einträge gefunden</value>
  </data>
  <data name="MaskNo" xml:space="preserve">
      <value>Masken Nr.</value>
  </data>
  <data name="ParameterType" xml:space="preserve">
      <value>Parameterart</value>
  </data>
  <data name="CalculationType" xml:space="preserve">
      <value>Berechnungstyp</value>
  </data>
  <data name="CalculateFrom" xml:space="preserve">
      <value>Berechnen ab</value>
  </data>
  <data name="CalculateUntil" xml:space="preserve">
      <value>Berechnen bis</value>
  </data>
  <data name="BaseValue" xml:space="preserve">
      <value>Basiswert</value>
  </data>
  <data name="StepValue" xml:space="preserve">
      <value>Schrittwert</value>
  </data>
  <data name="Factor" xml:space="preserve">
      <value>Faktor</value>
  </data>
  <data name="BaseValueFrom" xml:space="preserve">
      <value>Basiswert von</value>
  </data>
  <data name="BaseValueTo" xml:space="preserve">
      <value>Basiswert bis</value>
  </data>
  <data name="ModifierValueStart" xml:space="preserve">
      <value>Modifikator Wert Start</value>
  </data>
  <data name="ModifierValueStep" xml:space="preserve">
      <value>Modifikator Wert Schritt</value>
  </data>
  <data name="SavingFailed" xml:space="preserve">
      <value>Speichern fehlgeschlagen</value>
  </data>
  <data name="AllFieldsMustBeFilled" xml:space="preserve">
      <value>Alle Felder müssen gefüllt sein.</value>
  </data>
  <data name="Update" xml:space="preserve">
      <value>Aktualisieren</value>
  </data>
  <data name="NoAccountingFound" xml:space="preserve">
      <value>Keine Abrechnung gefunden</value>
  </data>
  <data name="Open" xml:space="preserve">
      <value>Offen</value>
  </data>
  <data name="Price" xml:space="preserve">
      <value>Preis</value>
  </data>
  <data name="ErrorDuringLoadingTheData" xml:space="preserve">
      <value>Fehler beim Laden der Daten: {0}</value>
  </data>
  <data name="GrainParameters" xml:space="preserve">
      <value>Getreideparameter</value>
  </data>
  <data name="MaskType" xml:space="preserve">
      <value>Maskentyp</value>
  </data>
  <data name="Humidity" xml:space="preserve">
      <value>Feuchtigkeit</value>
  </data>
  <data name="Money" xml:space="preserve">
      <value>Geld</value>
  </data>
  <data name="Trimming" xml:space="preserve">
      <value>Besatz</value>
  </data>
  <data name="Import" xml:space="preserve">
      <value>Import</value>
  </data>
  <data name="OpenAPI" xml:space="preserve">
      <value>OpenAPI</value>
  </data>
  <data name="Home" xml:space="preserve">
      <value>Home</value>
  </data>
  <data name="NoOrderImported" xml:space="preserve">
      <value>Kein Auftrag importiert</value>
  </data>
  <data name="OrderNumber" xml:space="preserve">
      <value>Auftragsnummer</value>
  </data>
  <data name="PurchaseOrderNumber" xml:space="preserve">
      <value>Bestellnummer</value>
  </data>
  <data name="OrderDate" xml:space="preserve">
      <value>Auftragsdatum</value>
  </data>
  <data name="DeliveryFromDate" xml:space="preserve">
      <value>Lieferung von Datum</value>
  </data>
  <data name="DeliveryToDate" xml:space="preserve">
      <value>Lieferung bis Datum</value>
  </data>
  <data name="ImportOfOrdersWasSuccessfullyCompleted" xml:space="preserve">
      <value>Der Import von {0} Aufträgen wurde erfolgreich abgeschlossen. Dauer: {1}.</value>
  </data>
  <data name="Commission" xml:space="preserve">
      <value>Kommissionierung</value>
  </data>
  <data name="Printer" xml:space="preserve">
      <value>Drucker</value>
  </data>
  <data name="WithRobot" xml:space="preserve">
      <value>Mit Roboter</value>
  </data>
  <data name="SelectCommissionTrack" xml:space="preserve">
      <value>Kommssionsspur auswählen</value>
  </data>
  <data name="PrintSettings" xml:space="preserve">
      <value>Drucker Einstellungen</value>
  </data>
  <data name="GoodsRecipient" xml:space="preserve">
      <value>Warenempfänger</value>
  </data>
  <data name="PalletCount" xml:space="preserve">
      <value>Paletten Anzahl</value>
  </data>
  <data name="Customer" xml:space="preserve">
      <value>Kunde</value>
  </data>
  <data name="CommissionButton" xml:space="preserve">
      <value>kommissionieren</value>
  </data>
  <data name="PalletTypeIsMandatory" xml:space="preserve">
      <value>Palettentyp ist pflicht</value>
  </data>
  <data name="PriceList" xml:space="preserve">
      <value>Preisliste</value>
  </data>
  <data name="PriceInKG" xml:space="preserve">
      <value>Preis in KG</value>
  </data>
  <data name="InPriceList" xml:space="preserve">
      <value>In Preisliste</value>
  </data>
  <data name="ShouldTheArticleReallyBeDeleted" xml:space="preserve">
      <value>Soll der Artikel '{0}' wirklich gelöscht werden?</value>
  </data>
  <data name="DeleteArticle" xml:space="preserve">
      <value>Artikel löschen</value>
  </data>
  <data name="Articles" xml:space="preserve">
      <value>Artikel</value>
  </data>
  <data name="PriceListCouldNotBeSaved" xml:space="preserve">
      <value>Preisliste konnte nicht gespeichert werden</value>
  </data>
  <data name="PriceListSaved" xml:space="preserve">
      <value>Preisliste gespeichert</value>
  </data>
</root>