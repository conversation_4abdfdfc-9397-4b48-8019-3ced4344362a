using System.Globalization;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Localization;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Languages;

public static class LanguageResource
{
    public static readonly CultureInfo[] SupportedCultures =
    [
        new("de-DE"),
        new("en-US")
    ];

    public static void ConfigureLocalization(this WebApplication app, IConfiguration config)
    {
        var defaultCulture = config.GetSection("DefaultCulture").Value ?? "en-US";
        
        // Ensure the default culture is supported
        if (SupportedCultures.All(c => c.Name != defaultCulture))
            defaultCulture = "en-US";

        var localizationOptions = new RequestLocalizationOptions()
        {
            DefaultRequestCulture = new RequestCulture(defaultCulture),
            SupportedCultures = SupportedCultures,
            SupportedUICultures = SupportedCultures,
            RequestCultureProviders = new List<IRequestCultureProvider>
            {
                // 1. Check cookie first (user's explicit choice)
                new CookieRequestCultureProvider(),
                // 2. Fall back to configuration default
                new CustomDefaultCultureProvider(defaultCulture),
                // 3. Fall back to Accept-Language header
                new AcceptLanguageHeaderRequestCultureProvider()
            }
        };
        
        app.UseRequestLocalization(localizationOptions);
    }

    public static void AddLocalizationServices(this IServiceCollection services)
    {
        services.AddLocalization();
    }
}

// Custom provider to use the configuration default culture
public class CustomDefaultCultureProvider(string defaultCulture) : IRequestCultureProvider
{
    public Task<ProviderCultureResult?> DetermineProviderCultureResult(HttpContext httpContext)
    {
        return Task.FromResult<ProviderCultureResult?>(
            new ProviderCultureResult(defaultCulture));
    }
}

