namespace Licencing;

public record LicenseClaimType(string Value)
{
    public static readonly string StammDbConnectedValue = "StammDbConnected";
    public static readonly string StammDbConnectedFailedValue = "StammDbConnectedFailed";
    public static readonly string SuccessSigninValue = "SuccessSignin";
    public static readonly string ExportGroupValue = "ExportGroup";
    public static readonly string ImportGroupValue = "ImportGroup";
    public static readonly string InvoiceExportOrImportGroupValue = "InvoiceExportOrImportGroup";
    public static readonly string WingLagerGroupValue = "WingLagerGroup";
    public static readonly string ConfigurationGroupValue = "ConfigurationGroup";
    public static readonly string MasterDataGroupValue = "MasterDataGroup";
    public static readonly string GrainAccountingGroupValue = "GrainAccountingGroup";
    public static readonly string ArticleValue = "ArticleMasterData";
    public static readonly string PriceListValue = "PriceListGroup";
    
    public static readonly string DatevExportValue = "DatevExport";
    public static readonly string EdifactExportImportValue = "EdifactExportImport";
    public static readonly string AddisonTseNitExportValue = "AddisonTseNitExport";
    public static readonly string AddisonPipelineExportValue = "AddisonPipelineExport";
    public static readonly string EasiAdfinityRestApiInvoiceExportValue = "EasiAdfinityRestApiInvoiceExport";
    
    public static readonly string ProcessOderHandlingValue = "ProcessOderHandling";
    public static string InvoiceOverViewExportZugferdValue => "InvoiceOverViewExportZugferd";
    public static string SchedulerValue => "Scheduler";
    public static string OpenApi => "OpenApi";
    
    public static readonly string CustomerMasterData = "CustomerMasterData";
    public static readonly string GrainAccounting = "GrainAccounting";
    

    public bool IsDatevExport => Value == DatevExportValue;
    public bool IsInvoiceOverViewExportZugferd => Value == InvoiceOverViewExportZugferdValue;
    public bool IsScheduler => Value == SchedulerValue;
}


public static class LicenseClaims
{
    public static readonly Dictionary<string,string> PossibleClaimsWithDescriptionAndTypes =
    new(){
        {LicenseClaimType.StammDbConnectedValue, "StammDbConnected"},
        {LicenseClaimType.StammDbConnectedFailedValue, "StammDbConnectedFailed"},
        {LicenseClaimType.SuccessSigninValue, "SuccessSignin"},
        {LicenseClaimType.InvoiceExportOrImportGroupValue, "InvoiceExportImport"},
        {LicenseClaimType.ExportGroupValue, "Export"},
        {LicenseClaimType.ImportGroupValue, "Import"},
        {LicenseClaimType.DatevExportValue, "Datev Export"},
        {LicenseClaimType.EdifactExportImportValue, "Edifact Export/Import"},
        {LicenseClaimType.InvoiceOverViewExportZugferdValue, "ZUGFeRD Export"},
        {LicenseClaimType.AddisonTseNitExportValue, "Addsison (tse:nit) Export"},
        {LicenseClaimType.AddisonPipelineExportValue, "Addsison Pipeline Export"},
        {LicenseClaimType.ArticleValue, "Artikel"},
        {LicenseClaimType.PriceListValue, "Preisliste"},
        {LicenseClaimType.EasiAdfinityRestApiInvoiceExportValue, "Easi Export"},
        {LicenseClaimType.SchedulerValue, "Tasks"},
        {LicenseClaimType.ProcessOderHandlingValue, "Prozesssteuerung"},
        {LicenseClaimType.WingLagerGroupValue, "WingLagerGroupValue"},
        {LicenseClaimType.ConfigurationGroupValue, "ConfigurationGroupValue"},
        {LicenseClaimType.OpenApi, "OpenApi"},
        {LicenseClaimType.CustomerMasterData, "Kundenstammdaten"},
        {LicenseClaimType.GrainAccounting, "Getreideabrechnung"}
    };
    
    public static readonly Dictionary<string,List<string>> PossibleGroupClaimsWithDescriptionAndTypes =
        new(){
            {LicenseClaimType.InvoiceOverViewExportZugferdValue, [LicenseClaimType.InvoiceExportOrImportGroupValue,LicenseClaimType.ConfigurationGroupValue]},
            {LicenseClaimType.DatevExportValue, [LicenseClaimType.ExportGroupValue]},
            {LicenseClaimType.EdifactExportImportValue, [LicenseClaimType.ExportGroupValue,LicenseClaimType.ImportGroupValue,LicenseClaimType.InvoiceExportOrImportGroupValue]},
            {LicenseClaimType.AddisonTseNitExportValue, [LicenseClaimType.ExportGroupValue]},
            {LicenseClaimType.AddisonPipelineExportValue, [LicenseClaimType.ExportGroupValue]},
            {LicenseClaimType.EasiAdfinityRestApiInvoiceExportValue, [LicenseClaimType.ExportGroupValue]},
            {LicenseClaimType.ProcessOderHandlingValue, [LicenseClaimType.WingLagerGroupValue,LicenseClaimType.ConfigurationGroupValue]},
            {LicenseClaimType.OpenApi, [LicenseClaimType.ConfigurationGroupValue]},
            {LicenseClaimType.CustomerMasterData, [LicenseClaimType.MasterDataGroupValue]},
            {LicenseClaimType.ArticleValue, [LicenseClaimType.MasterDataGroupValue]},
            {LicenseClaimType.GrainAccounting, [LicenseClaimType.GrainAccountingGroupValue]},
        };
}

