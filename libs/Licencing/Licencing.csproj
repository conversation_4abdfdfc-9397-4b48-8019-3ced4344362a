<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
      <PackageReference Include="BouncyCastle.Cryptography" Version="2.6.2" />
      <PackageReference Include="Standard.Licensing" Version="1.2.1" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\ObjectDeAndSerialize\ObjectDeAndSerialize.csproj" />
    </ItemGroup>

</Project>
