<Project Sdk="Microsoft.NET.Sdk">

    <PropertyGroup>
        <TargetFramework>net9.0</TargetFramework>
        <ImplicitUsings>enable</ImplicitUsings>
        <Nullable>enable</Nullable>
    </PropertyGroup>

    <ItemGroup>
        <FrameworkReference Include="Microsoft.AspNetCore.App"/>
        <PackageReference Include="MediatR" Version="[12.5.0]" />

        <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="9.7.0" />
        <PackageReference Include="Microsoft.Extensions.ServiceDiscovery" Version="9.4.0" />
        <PackageReference Include="OpenTelemetry.Exporter.OpenTelemetryProtocol" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Extensions.Hosting" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Http" Version="1.12.0" />
        <PackageReference Include="OpenTelemetry.Instrumentation.Runtime" Version="1.12.0" />
    </ItemGroup>

    <ItemGroup>
      <ProjectReference Include="..\..\Modules\WingCore\WingCore.data\WingCore.data.csproj" />
      <ProjectReference Include="..\..\Modules\WingCore\WingCore.ioc\WingCore.ioc.csproj" />
    </ItemGroup>
</Project>
